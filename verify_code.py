# -*- coding: utf-8 -*-
"""
验证代码是否正确
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def verify_code():
    """验证代码逻辑"""
    print("=== 验证代码逻辑 ===")
    
    # 检查文件内容
    with open('src/core/message_processor.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键代码是否存在
    checks = [
        ('检测到新联系人消息，开始处理', '检测到新联系人消息的日志'),
        ('不是新联系人消息，跳过处理', '跳过处理的日志'),
        ('_handle_new_contact(wechat_instance, data)', '处理新联系人的调用'),
        ('已添加了', '新的检测条件1'),
        ('为你服务', '新的检测条件2'),
        ('当前为客服聊天', '新的检测条件3'),
    ]
    
    for check_str, description in checks:
        if check_str in content:
            print(f"✅ {description}: 存在")
        else:
            print(f"❌ {description}: 不存在")
    
    # 检查方法结构
    print("\n--- 检查方法结构 ---")
    
    # 查找on_sys_message方法
    if 'def on_sys_message(' in content:
        print("✅ on_sys_message方法存在")
        
        # 提取方法内容
        start = content.find('def on_sys_message(')
        if start != -1:
            # 找到下一个方法的开始
            next_method = content.find('\n    def ', start + 1)
            if next_method == -1:
                method_content = content[start:]
            else:
                method_content = content[start:next_method]
            
            # 检查方法内容
            if 'if self._is_new_contact_message(data):' in method_content:
                print("✅ 新联系人检测调用存在")
            else:
                print("❌ 新联系人检测调用不存在")
                
            if 'self._handle_new_contact(wechat_instance, data)' in method_content:
                print("✅ 处理新联系人调用存在")
            else:
                print("❌ 处理新联系人调用不存在")
    else:
        print("❌ on_sys_message方法不存在")
    
    print("\n--- 建议 ---")
    print("如果所有检查都通过，但程序仍然不工作，请：")
    print("1. 重启程序以加载最新代码")
    print("2. 检查日志文件中的完整错误信息")
    print("3. 确认系统消息的数据格式是否与预期一致")

if __name__ == "__main__":
    verify_code()
