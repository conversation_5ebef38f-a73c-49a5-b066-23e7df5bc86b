# 微信私聊机器人 - SQLite3数据库会话管理

## 功能概述

实现了基于SQLite3数据库的完整会话管理系统，提供持久化的会话状态管理、智能的间隔时间控制和自动的数据清理功能。

## 核心特性

### 1. 数据库持久化
- **活跃会话管理**: 所有活跃会话存储在数据库中
- **失效会话记录**: 完整记录会话的生命周期
- **数据持久化**: 程序重启后会话状态保持
- **自动清理**: 定期清理过期的历史记录

### 2. 智能会话创建
- **系统消息创建**: 好友验证、客服名片进入时自动创建
- **间隔时间控制**: 完成选择后需等待设定时间才能重新创建
- **新用户立即创建**: 无历史记录的用户可立即创建会话
- **超时自动失效**: 5分钟无响应自动失效会话

### 3. 完整的生命周期管理
- **创建** → **活跃** → **失效** → **清理**
- 每个阶段都有完整的数据记录和日志

## 数据库设计

### 活跃会话表 (active_sessions)
```sql
CREATE TABLE active_sessions (
    session_id TEXT PRIMARY KEY,        -- 会话ID（用户ID）
    chat_id TEXT NOT NULL,              -- 聊天ID
    start_time REAL NOT NULL,           -- 开始时间
    waiting_for_option INTEGER DEFAULT 1, -- 是否等待选项回复
    current_path TEXT DEFAULT '[]',     -- 当前对话路径（JSON）
    source TEXT DEFAULT 'system_message', -- 会话来源
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 失效会话表 (expired_sessions)
```sql
CREATE TABLE expired_sessions (
    session_id TEXT PRIMARY KEY,        -- 会话ID
    chat_id TEXT NOT NULL,              -- 聊天ID
    start_time REAL NOT NULL,           -- 开始时间
    end_time REAL NOT NULL,             -- 结束时间
    completion_time REAL,               -- 完成时间（用户完成选择）
    total_duration REAL,                -- 总持续时间
    source TEXT DEFAULT 'system_message', -- 会话来源
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expired_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 索引优化
- `idx_active_session_id`: 活跃会话ID索引
- `idx_active_start_time`: 活跃会话开始时间索引
- `idx_expired_session_id`: 失效会话ID索引
- `idx_expired_completion_time`: 失效会话完成时间索引

## 核心功能实现

### 1. 数据库管理器 (`SessionDatabaseManager`)

#### 主要方法
```python
# 创建活跃会话
create_active_session(session_id, chat_id, source) -> bool

# 获取活跃会话
get_active_session(session_id) -> Optional[Dict]

# 使会话失效
expire_session(session_id, completion_time) -> bool

# 获取失效会话
get_expired_session(session_id) -> Optional[Dict]

# 检查是否可以创建新会话
can_create_new_session(session_id, interval_seconds) -> bool

# 更新会话路径
update_session_path(session_id, current_path) -> bool

# 清理旧记录
cleanup_old_expired_sessions(days_to_keep) -> int

# 获取统计信息
get_session_statistics() -> Dict[str, int]
```

### 2. 会话创建逻辑

#### 系统消息创建会话
```python
def _handle_new_contact(self, wechat_instance, data):
    # 检查是否已有活跃会话
    existing_session = self.session_db.get_active_session(session_id)
    if existing_session:
        return
    
    # 在数据库中创建新会话
    if self.session_db.create_active_session(session_id, conversation_id, 'system_message'):
        # 发送欢迎消息
        wechat_instance.send_text(conversation_id, auto_reply)
```

#### 文本消息重新创建会话
```python
def _process_message(self, wechat_instance, room_wxid, from_wxid, ...):
    # 检查是否可以创建新会话
    if self.session_db.can_create_new_session(session_id, self.interval_seconds):
        self._create_new_session_from_text(...)
    else:
        # 忽略消息
        return
```

### 3. 会话失效处理

#### 用户完成选择
```python
if option_type == 'final':
    current_time = time.time()
    
    # 在数据库中将会话标记为失效（记录完成时间）
    if self.session_db.expire_session(session_id, current_time):
        self.logger.info(f"会话已在数据库中标记为失效: {session_id}")
```

#### 会话超时失效
```python
if current_time - session['start_time'] > 300:  # 5分钟超时
    # 在数据库中将会话标记为失效（超时失效，无完成时间）
    self.session_db.expire_session(session_id, None)
```

### 4. 智能间隔时间检查

```python
def can_create_new_session(self, session_id, interval_seconds):
    # 检查是否有活跃会话
    active_session = self.get_active_session(session_id)
    if active_session:
        return False
    
    # 检查失效会话的完成时间
    expired_session = self.get_expired_session(session_id)
    if not expired_session:
        # 没有任何会话记录，可以立即创建
        return True
    
    # 检查间隔时间
    completion_time = expired_session.get('completion_time')
    if completion_time:
        current_time = time.time()
        time_passed = current_time - completion_time
        return time_passed >= interval_seconds
    
    return True
```

## 使用流程

### 1. 新用户首次使用
1. **系统消息触发** → 检查数据库无记录 → 立即创建活跃会话
2. **发送欢迎消息** → 用户进行选项选择
3. **完成选择** → 会话失效并记录完成时间

### 2. 老用户重复使用
1. **发送文本消息** → 检查数据库记录
2. **间隔时间内** → 忽略消息
3. **超过间隔时间** → 重新创建活跃会话
4. **发送欢迎消息** → 继续服务流程

### 3. 会话超时处理
1. **5分钟无响应** → 自动失效会话
2. **记录到失效表** → 清理活跃会话
3. **发送超时提示** → 等待下次交互

## 配置和管理

### 1. 数据库文件位置
- **默认路径**: `data/sessions.db`
- **自动创建**: 程序首次运行时自动创建目录和数据库

### 2. 间隔时间设置
- **界面配置**: 在"私聊机器人配置"区域设置
- **实时生效**: 修改后立即应用
- **默认值**: 30分钟

### 3. 自动清理
- **启动清理**: 程序启动时清理30天前的记录
- **保留策略**: 保留最近30天的失效会话记录
- **性能优化**: 定期清理避免数据库过大

## 日志和监控

### 1. 详细日志记录
```
2025-07-23 23:34:56 - INFO - 向新联系人发送欢迎消息并创建会话: user123, 会话ID: user123
2025-07-23 23:35:30 - INFO - 会话已在数据库中标记为失效: user123
2025-07-23 23:40:15 - INFO - 用户 user123 不满足创建新会话条件，忽略文本消息
2025-07-24 00:10:20 - INFO - 用户 user123 可以创建新会话（无活跃会话且满足间隔条件）
```

### 2. 统计信息
```python
stats = processor.get_session_statistics()
# 返回: {
#     'active_sessions': 5,      # 当前活跃会话数
#     'expired_sessions': 150,   # 历史失效会话数
#     'today_sessions': 25       # 今日会话数
# }
```

## 优势特点

### 1. 数据可靠性
- **持久化存储**: 程序重启不丢失会话状态
- **事务安全**: 数据库操作保证一致性
- **备份恢复**: SQLite文件易于备份和恢复

### 2. 性能优化
- **索引优化**: 关键字段建立索引提高查询速度
- **批量操作**: 支持批量清理和统计
- **内存协调**: 数据库和内存状态同步

### 3. 功能完整
- **全生命周期**: 从创建到清理的完整管理
- **智能判断**: 基于历史记录的智能决策
- **灵活配置**: 支持多种配置和策略

### 4. 易于维护
- **清晰结构**: 模块化设计易于理解和维护
- **完整日志**: 详细的操作日志便于调试
- **统计监控**: 实时统计信息便于监控

## 扩展性

### 1. 支持多种会话类型
- 可以扩展支持群聊会话管理
- 支持不同类型的会话策略

### 2. 支持复杂的时间策略
- 可以为不同用户设置不同的间隔时间
- 支持基于时间段的动态策略

### 3. 支持数据分析
- 丰富的历史数据支持用户行为分析
- 支持会话质量和效率分析

这个SQLite3数据库会话管理系统为微信私聊机器人提供了企业级的会话管理能力，确保了数据的可靠性、系统的稳定性和功能的完整性。
