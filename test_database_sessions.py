# -*- coding: utf-8 -*-
"""
测试数据库会话管理功能
"""

import sys
import os
import time
import sqlite3

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_database_manager():
    """测试数据库管理器"""
    print("=== 测试数据库管理器 ===")
    
    try:
        from src.database.session_manager import SessionDatabaseManager
        
        # 使用测试数据库
        test_db_path = "test_sessions.db"
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
        
        db_manager = SessionDatabaseManager(test_db_path)
        
        print("✅ 数据库管理器创建成功")
        
        # 测试创建活跃会话
        session_id = "test_user_123"
        chat_id = "S:1234567890"
        
        success = db_manager.create_active_session(session_id, chat_id, "system_message")
        if success:
            print("✅ 创建活跃会话成功")
        else:
            print("❌ 创建活跃会话失败")
            return
        
        # 测试获取活跃会话
        session = db_manager.get_active_session(session_id)
        if session:
            print(f"✅ 获取活跃会话成功: {session['session_id']}")
            print(f"   会话来源: {session['source']}")
            print(f"   等待选项: {session['waiting_for_option']}")
        else:
            print("❌ 获取活跃会话失败")
            return
        
        # 测试会话失效
        completion_time = time.time()
        success = db_manager.expire_session(session_id, completion_time)
        if success:
            print("✅ 会话失效成功")
        else:
            print("❌ 会话失效失败")
            return
        
        # 测试获取失效会话
        expired_session = db_manager.get_expired_session(session_id)
        if expired_session:
            print(f"✅ 获取失效会话成功: {expired_session['session_id']}")
            print(f"   完成时间: {expired_session['completion_time']}")
            print(f"   持续时间: {expired_session['total_duration']:.2f}秒")
        else:
            print("❌ 获取失效会话失败")
        
        # 测试间隔时间检查
        print("\n--- 测试间隔时间检查 ---")
        
        # 立即检查（应该不能创建）
        can_create = db_manager.can_create_new_session(session_id, 10)  # 10秒间隔
        print(f"立即检查是否可以创建新会话: {can_create}")
        
        # 等待后检查
        print("等待2秒...")
        time.sleep(2)
        can_create = db_manager.can_create_new_session(session_id, 1)  # 1秒间隔
        print(f"等待后检查是否可以创建新会话: {can_create}")
        
        # 测试新用户（无记录）
        new_user = "new_user_456"
        can_create_new = db_manager.can_create_new_session(new_user, 10)
        print(f"新用户是否可以创建会话: {can_create_new}")
        
        # 测试统计信息
        stats = db_manager.get_session_statistics()
        print(f"\n会话统计: {stats}")
        
        # 清理测试数据库
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
        
        print("✅ 数据库管理器测试完成")
        
    except Exception as e:
        print(f"❌ 数据库管理器测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_message_processor_integration():
    """测试消息处理器集成"""
    print("\n=== 测试消息处理器集成 ===")
    
    try:
        from src.core.message_processor import MessageProcessor
        from src.core.wechat_handler import WeChatHandler
        from src.utils.mock_ntwork import WeWork
        
        # 清理可能存在的测试数据库
        test_db_path = "data/sessions.db"
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
        
        # 创建消息处理器实例
        wechat_handler = WeChatHandler()
        processor = MessageProcessor(wechat_handler, None)
        
        # 设置私聊配置
        processor.group_option_configs = {
            'private_chat': {
                'auto_reply': '请选择服务类型：\n1.咨询\n2.投诉',
                'option_replies': {
                    '1': '感谢您的咨询！',
                    '2': '我们会认真处理您的投诉。'
                },
                'tree_options': {
                    '1': {'text': '咨询', 'type': 'final', 'reply': '感谢您的咨询！'},
                    '2': {'text': '投诉', 'type': 'final', 'reply': '我们会认真处理您的投诉。'}
                }
            }
        }
        
        # 设置较短的间隔时间用于测试（3秒）
        processor.set_interval_seconds(3)
        
        # 模拟微信实例
        mock_wechat = WeWork()
        
        user_id = "test_user_789"
        room_id = "S:9876543210"
        
        print("--- 步骤1: 系统消息创建会话 ---")
        
        # 模拟系统消息
        sys_message = {
            "data": {
                "content": "你已添加了测试用户，现在可以开始聊天了。",
                "conversation_id": room_id,
                "sender": user_id
            }
        }
        
        processor.on_sys_message(mock_wechat, sys_message)
        
        # 检查数据库中的活跃会话
        db_session = processor.session_db.get_active_session(user_id)
        if db_session:
            print("✅ 数据库中成功创建活跃会话")
            print(f"   会话ID: {db_session['session_id']}")
            print(f"   来源: {db_session['source']}")
        else:
            print("❌ 数据库中未找到活跃会话")
            return
        
        print("\n--- 步骤2: 用户完成选择 ---")
        
        # 模拟用户选择
        text_message = {
            "data": {
                "room_wxid": room_id,
                "from_wxid": user_id,
                "from_name": "测试用户",
                "msg": "1",
                "content_type": 0
            }
        }
        
        processor.on_recv_message(mock_wechat, text_message)
        
        # 检查会话是否失效
        db_session = processor.session_db.get_active_session(user_id)
        expired_session = processor.session_db.get_expired_session(user_id)
        
        if not db_session and expired_session:
            print("✅ 会话成功失效并记录到数据库")
            print(f"   完成时间: {expired_session['completion_time']}")
        else:
            print("❌ 会话失效处理失败")
        
        print("\n--- 步骤3: 间隔时间内发送消息 ---")
        
        # 立即发送文本消息（间隔时间内）
        processor.on_recv_message(mock_wechat, text_message)
        
        new_db_session = processor.session_db.get_active_session(user_id)
        if not new_db_session:
            print("✅ 间隔时间内的消息被正确忽略")
        else:
            print("❌ 间隔时间内错误地创建了新会话")
        
        print("\n--- 步骤4: 等待间隔时间后发送消息 ---")
        
        print("等待4秒（超过3秒间隔时间）...")
        time.sleep(4)
        
        # 再次发送文本消息
        processor.on_recv_message(mock_wechat, text_message)
        
        new_db_session = processor.session_db.get_active_session(user_id)
        if new_db_session and new_db_session['source'] == 'text_message_recreate':
            print("✅ 超过间隔时间后成功重新创建会话")
            print(f"   新会话来源: {new_db_session['source']}")
        else:
            print("❌ 超过间隔时间后未能重新创建会话")
        
        # 获取统计信息
        stats = processor.get_session_statistics()
        print(f"\n会话统计信息: {stats}")
        
        print("✅ 消息处理器集成测试完成")
        
    except Exception as e:
        print(f"❌ 消息处理器集成测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_database_structure():
    """测试数据库结构"""
    print("\n=== 测试数据库结构 ===")
    
    try:
        test_db_path = "test_structure.db"
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
        
        from src.database.session_manager import SessionDatabaseManager
        db_manager = SessionDatabaseManager(test_db_path)
        
        # 检查表结构
        with sqlite3.connect(test_db_path) as conn:
            cursor = conn.cursor()
            
            # 检查活跃会话表
            cursor.execute("PRAGMA table_info(active_sessions)")
            active_columns = cursor.fetchall()
            print("活跃会话表结构:")
            for col in active_columns:
                print(f"  {col[1]} {col[2]}")
            
            # 检查失效会话表
            cursor.execute("PRAGMA table_info(expired_sessions)")
            expired_columns = cursor.fetchall()
            print("\n失效会话表结构:")
            for col in expired_columns:
                print(f"  {col[1]} {col[2]}")
            
            # 检查索引
            cursor.execute("SELECT name FROM sqlite_master WHERE type='index'")
            indexes = cursor.fetchall()
            print("\n数据库索引:")
            for idx in indexes:
                if not idx[0].startswith('sqlite_'):
                    print(f"  {idx[0]}")
        
        # 清理测试数据库
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
        
        print("✅ 数据库结构测试完成")
        
    except Exception as e:
        print(f"❌ 数据库结构测试失败: {e}")

def main():
    """主测试函数"""
    print("微信私聊机器人 - 数据库会话管理测试")
    print("=" * 60)
    
    test_database_manager()
    test_message_processor_integration()
    test_database_structure()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("\n数据库功能总结：")
    print("1. ✅ SQLite3数据库管理活跃会话")
    print("2. ✅ 失效会话持久化存储")
    print("3. ✅ 智能间隔时间检查")
    print("4. ✅ 新用户立即创建会话")
    print("5. ✅ 会话超时自动失效")
    print("6. ✅ 旧记录自动清理")
    print("7. ✅ 完整的统计信息")
    print("8. ✅ 与消息处理器无缝集成")

if __name__ == "__main__":
    main()
