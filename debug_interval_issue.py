#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试间隔时间问题的脚本
"""

import sys
import os
import time
import sqlite3

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.database.session_manager import SessionDatabaseManager


def debug_interval_issue():
    """调试间隔时间问题"""
    print("=== 调试间隔时间问题 ===")
    
    # 创建数据库管理器
    db_manager = SessionDatabaseManager()
    
    # 测试用户ID（从日志中获取）
    test_user_id = "7881299718050125"
    
    print(f"测试用户ID: {test_user_id}")
    
    # 1. 检查活跃会话
    print("\n--- 检查活跃会话 ---")
    active_session = db_manager.get_active_session(test_user_id)
    if active_session:
        print(f"活跃会话: {active_session}")
    else:
        print("无活跃会话")
    
    # 2. 检查失效会话
    print("\n--- 检查失效会话 ---")
    expired_session = db_manager.get_expired_session(test_user_id)
    if expired_session:
        print(f"失效会话: {expired_session}")
        
        completion_time = expired_session.get('completion_time')
        end_time = expired_session.get('end_time')
        
        print(f"完成时间: {completion_time}")
        print(f"结束时间: {end_time}")
        
        if completion_time:
            current_time = time.time()
            time_passed = current_time - completion_time
            print(f"当前时间: {current_time}")
            print(f"已过时间: {time_passed}秒 ({time_passed/60:.2f}分钟)")
        
    else:
        print("无失效会话")
    
    # 3. 测试间隔检查
    print("\n--- 测试间隔检查 ---")
    interval_seconds = 60  # 1分钟
    can_create = db_manager.can_create_new_session(test_user_id, interval_seconds)
    print(f"间隔时间: {interval_seconds}秒")
    print(f"可以创建新会话: {can_create}")
    
    # 4. 直接查询数据库
    print("\n--- 直接查询数据库 ---")
    try:
        with sqlite3.connect(db_manager.db_path) as conn:
            cursor = conn.cursor()
            
            # 查询活跃会话表
            cursor.execute('SELECT * FROM active_sessions WHERE session_id = ?', (test_user_id,))
            active_rows = cursor.fetchall()
            print(f"活跃会话表记录数: {len(active_rows)}")
            for row in active_rows:
                print(f"  {row}")
            
            # 查询失效会话表
            cursor.execute('SELECT * FROM expired_sessions WHERE session_id = ? ORDER BY expired_at DESC LIMIT 5', (test_user_id,))
            expired_rows = cursor.fetchall()
            print(f"失效会话表记录数: {len(expired_rows)}")
            for row in expired_rows:
                print(f"  {row}")
                
    except Exception as e:
        print(f"数据库查询失败: {e}")
    
    # 5. 检查数据库表结构
    print("\n--- 检查数据库表结构 ---")
    try:
        with sqlite3.connect(db_manager.db_path) as conn:
            cursor = conn.cursor()
            
            # 检查失效会话表结构
            cursor.execute("PRAGMA table_info(expired_sessions)")
            columns = cursor.fetchall()
            print("失效会话表结构:")
            for col in columns:
                print(f"  {col}")
                
    except Exception as e:
        print(f"检查表结构失败: {e}")


if __name__ == "__main__":
    debug_interval_issue()
