2025-07-24 00:00:12 - src.core.message_processor.2717866316032 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250724.log
2025-07-24 00:00:12 - src.core.message_processor.2717866316032 - INFO - 消息处理器日志系统初始化完成
2025-07-24 00:00:13 - src.core.message_processor.2717866316032 - INFO - 设置间隔时间为: 1800秒 (30分钟)
2025-07-24 00:00:13 - src.core.message_processor.2717866316032 - INFO - 设置间隔时间为: 300秒 (5分钟)
2025-07-24 00:00:46 - src.core.message_processor.2717866316032 - INFO - 收到原始消息: {"data": {"appinfo": "681425993995234779", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "2154", "receiver": "1688854710715177", "send_time": "1753286445", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1019533"}, "type": 11041}
2025-07-24 00:00:46 - src.core.message_processor.2717866316032 - INFO - 机器人名字: 邾俊勇
2025-07-24 00:00:46 - src.core.message_processor.2717866316032 - INFO - 消息类型: 2
2025-07-24 00:00:46 - src.core.message_processor.2717866316032 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 1
2025-07-24 00:00:46 - src.core.message_processor.2717866316032 - INFO - 消息类型: 私聊
2025-07-24 00:00:46 - src.core.message_processor.2717866316032 - INFO - 用户 7881299718050125 可以创建新会话（无活跃会话且满足间隔条件）
2025-07-24 00:00:46 - src.core.message_processor.2717866316032 - INFO - 数据库创建会话成功: 7881299718050125
2025-07-24 00:00:46 - src.core.message_processor.2717866316032 - INFO - 从文本消息重新创建会话并发送欢迎消息: 7881299718050125, 会话ID: 7881299718050125
2025-07-24 00:00:46 - src.core.message_processor.2717866316032 - INFO - 收到原始消息: {"data": {"appinfo": "CAEQro6ExAYYqYbwiJKAgAMgCA==", "at_list": [], "content": "您好！欢迎咨询，请问您需要以下哪项服务？请直接回复对应数字：\n1.查询快递物流信息\n2.投诉/反馈问题\n3.联系人工客服", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 1, "local_id": "2155", "receiver": "7881299718050125", "send_time": "1753286446", "sender": "1688854710715177", "sender_name": "邾俊勇", "server_id": "1019538"}, "type": 11041}
2025-07-24 00:00:46 - src.core.message_processor.2717866316032 - INFO - 机器人名字: 邾俊勇
2025-07-24 00:00:46 - src.core.message_processor.2717866316032 - INFO - 忽略自己发送的消息
2025-07-24 00:01:04 - src.core.message_processor.2717866316032 - INFO - 收到原始消息: {"data": {"appinfo": "2440801945097050696", "at_list": [], "content": "00", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "2156", "receiver": "1688854710715177", "send_time": "1753286463", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1019540"}, "type": 11041}
2025-07-24 00:01:04 - src.core.message_processor.2717866316032 - INFO - 机器人名字: 邾俊勇
2025-07-24 00:01:04 - src.core.message_processor.2717866316032 - INFO - 消息类型: 2
2025-07-24 00:01:04 - src.core.message_processor.2717866316032 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 00
2025-07-24 00:01:04 - src.core.message_processor.2717866316032 - INFO - 消息类型: 私聊
2025-07-24 00:01:04 - src.core.message_processor.2717866316032 - INFO - 用户输入无效选项: 00, 提示重新输入, 聊天ID: S:1688854710715177_7881299718050125
2025-07-24 00:01:04 - src.core.message_processor.2717866316032 - INFO - 收到原始消息: {"data": {"appinfo": "CAEQwI6ExAYYqYbwiJKAgAMgCg==", "at_list": [], "content": "请回复对应的数字序号（1-3）", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 1, "local_id": "2157", "receiver": "7881299718050125", "send_time": "1753286465", "sender": "1688854710715177", "sender_name": "邾俊勇", "server_id": "1019545"}, "type": 11041}
2025-07-24 00:01:04 - src.core.message_processor.2717866316032 - INFO - 机器人名字: 邾俊勇
2025-07-24 00:01:04 - src.core.message_processor.2717866316032 - INFO - 忽略自己发送的消息
2025-07-24 00:01:11 - src.core.message_processor.2717866316032 - INFO - 收到原始消息: {"data": {"appinfo": "2684491445309649542", "at_list": [], "content": "2", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "2158", "receiver": "1688854710715177", "send_time": "1753286471", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1019547"}, "type": 11041}
2025-07-24 00:01:11 - src.core.message_processor.2717866316032 - INFO - 机器人名字: 邾俊勇
2025-07-24 00:01:11 - src.core.message_processor.2717866316032 - INFO - 消息类型: 2
2025-07-24 00:01:12 - src.core.message_processor.2717866316032 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 2
2025-07-24 00:01:12 - src.core.message_processor.2717866316032 - INFO - 消息类型: 私聊
2025-07-24 00:01:12 - src.core.message_processor.2717866316032 - INFO - 开始处理特殊回复，内容长度: 20
2025-07-24 00:01:12 - src.core.message_processor.2717866316032 - INFO - 普通文本回复
2025-07-24 00:01:12 - src.core.message_processor.2717866316032 - INFO - 发送选项回复给私聊用户: 7881299718050125, 聊天ID: S:1688854710715177_7881299718050125
2025-07-24 00:01:12 - src.core.message_processor.2717866316032 - INFO - 会话已在数据库中标记为失效: 7881299718050125
2025-07-24 00:01:12 - src.core.message_processor.2717866316032 - INFO - 对话结束，清理会话: 7881299718050125, 记录完成时间: 1753286472.2118282
2025-07-24 00:01:12 - src.core.message_processor.2717866316032 - INFO - 收到原始消息: {"data": {"appinfo": "CAEQyI6ExAYYqYbwiJKAgAMgDA==", "at_list": [], "content": "很抱歉给您带来不便，请选择具体问题类型：", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 1, "local_id": "2159", "receiver": "7881299718050125", "send_time": "1753286472", "sender": "1688854710715177", "sender_name": "邾俊勇", "server_id": "1019552"}, "type": 11041}
2025-07-24 00:01:12 - src.core.message_processor.2717866316032 - INFO - 机器人名字: 邾俊勇
2025-07-24 00:01:12 - src.core.message_processor.2717866316032 - INFO - 忽略自己发送的消息
2025-07-24 00:01:25 - src.core.message_processor.2717866316032 - INFO - 收到原始消息: {"data": {"appinfo": "182347696924359331", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "2160", "receiver": "1688854710715177", "send_time": "1753286485", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1019554"}, "type": 11041}
2025-07-24 00:01:25 - src.core.message_processor.2717866316032 - INFO - 机器人名字: 邾俊勇
2025-07-24 00:01:25 - src.core.message_processor.2717866316032 - INFO - 消息类型: 2
2025-07-24 00:01:25 - src.core.message_processor.2717866316032 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 1
2025-07-24 00:01:26 - src.core.message_processor.2717866316032 - INFO - 消息类型: 私聊
2025-07-24 00:01:26 - src.core.message_processor.2717866316032 - INFO - 用户 7881299718050125 不满足创建新会话条件，忽略文本消息
2025-07-24 00:13:01 - src.core.message_processor.2717866316032 - INFO - 收到原始消息: {"data": {"appinfo": "2113585462090186913", "at_list": [], "content": "2", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "2161", "receiver": "1688854710715177", "send_time": "1753287180", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1019559"}, "type": 11041}
2025-07-24 00:13:01 - src.core.message_processor.2717866316032 - INFO - 机器人名字: 邾俊勇
2025-07-24 00:13:01 - src.core.message_processor.2717866316032 - INFO - 消息类型: 2
2025-07-24 00:13:01 - src.core.message_processor.2717866316032 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 2
2025-07-24 00:13:01 - src.core.message_processor.2717866316032 - INFO - 消息类型: 私聊
2025-07-24 00:13:01 - src.core.message_processor.2717866316032 - INFO - 用户 7881299718050125 可以创建新会话（无活跃会话且满足间隔条件）
2025-07-24 00:13:01 - src.core.message_processor.2717866316032 - INFO - 数据库创建会话成功: 7881299718050125
2025-07-24 00:13:01 - src.core.message_processor.2717866316032 - INFO - 从文本消息重新创建会话并发送欢迎消息: 7881299718050125, 会话ID: 7881299718050125
2025-07-24 00:13:01 - src.core.message_processor.2717866316032 - INFO - 收到原始消息: {"data": {"appinfo": "CAEQjZSExAYYqYbwiJKAgAMgDw==", "at_list": [], "content": "您好！欢迎咨询，请问您需要以下哪项服务？请直接回复对应数字：\n1.查询快递物流信息\n2.投诉/反馈问题\n3.联系人工客服", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 1, "local_id": "2162", "receiver": "7881299718050125", "send_time": "1753287182", "sender": "1688854710715177", "sender_name": "邾俊勇", "server_id": "1019564"}, "type": 11041}
2025-07-24 00:13:01 - src.core.message_processor.2717866316032 - INFO - 机器人名字: 邾俊勇
2025-07-24 00:13:01 - src.core.message_processor.2717866316032 - INFO - 忽略自己发送的消息
