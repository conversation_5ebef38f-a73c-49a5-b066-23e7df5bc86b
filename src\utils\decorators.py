# -*- coding: utf-8 -*-
"""
装饰器模块 - 提供各种装饰器功能
"""

import threading


def singleton_thread_safe(cls):
    """
    线程安全的单例装饰器
    确保在多线程环境下只创建一个实例
    """
    _instances = {}
    _lock = threading.Lock()  # 线程锁
    
    def wrapper(*args, **kwargs):
        with _lock:  # 加锁保证线程安全
            if cls not in _instances:
                _instances[cls] = cls(*args, **kwargs)
            return _instances[cls]
    
    return wrapper
