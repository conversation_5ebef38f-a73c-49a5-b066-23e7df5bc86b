2025-07-26 15:26:53 - src.core.message_processor.2852835021056 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250726.log
2025-07-26 15:26:53 - src.core.message_processor.2852835021056 - INFO - 消息处理器日志系统初始化完成
2025-07-26 15:26:53 - src.core.message_processor.2852835021056 - INFO - 设置间隔时间为: 1800秒 (30分钟)
2025-07-26 15:26:53 - src.core.message_processor.2852835021056 - INFO - 设置间隔时间为: 300秒 (5分钟)
2025-07-26 15:29:26 - src.core.message_processor.2852835021056 - INFO - 收到原始消息: {"data": {"appinfo": "1981531747783220562", "at_list": [], "content": "7", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "2297", "receiver": "1688854710715177", "send_time": "1753514966", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1020085"}, "type": 11041}
2025-07-26 15:29:26 - src.core.message_processor.2852835021056 - INFO - 机器人名字: 邾俊勇
2025-07-26 15:29:26 - src.core.message_processor.2852835021056 - INFO - 消息类型: 2
2025-07-26 15:29:26 - src.core.message_processor.2852835021056 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 7
2025-07-26 15:29:26 - src.core.message_processor.2852835021056 - INFO - 消息类型: 私聊
2025-07-26 15:29:26 - src.core.message_processor.2852835021056 - INFO - 用户会话超时，清理会话: 7881299718050125
2025-07-26 15:29:26 - src.core.message_processor.2852835021056 - INFO - 发送会话超时消息给用户: 7881299718050125
2025-07-26 15:29:26 - src.core.message_processor.2852835021056 - INFO - 用户 7881299718050125 不满足创建新会话条件，忽略文本消息
2025-07-26 15:29:27 - src.core.message_processor.2852835021056 - INFO - 收到原始消息: {"data": {"appinfo": "CAEQ1oeSxAYYqYbwiJKAgAMgAg==", "at_list": [], "content": "您的会话已超时，如需继续咨询请重新发送消息。", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 1, "local_id": "2298", "receiver": "7881299718050125", "send_time": "1753514967", "sender": "1688854710715177", "sender_name": "邾俊勇", "server_id": "1020088"}, "type": 11041}
2025-07-26 15:29:27 - src.core.message_processor.2852835021056 - INFO - 机器人名字: 邾俊勇
2025-07-26 15:29:27 - src.core.message_processor.2852835021056 - INFO - 忽略自己发送的消息
2025-07-26 15:29:38 - src.core.message_processor.2852835021056 - INFO - 收到原始消息: {"data": {"appinfo": "7177502813212505986", "at_list": [], "content": "7", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "2299", "receiver": "1688854710715177", "send_time": "1753514977", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1020090"}, "type": 11041}
2025-07-26 15:29:38 - src.core.message_processor.2852835021056 - INFO - 机器人名字: 邾俊勇
2025-07-26 15:29:38 - src.core.message_processor.2852835021056 - INFO - 消息类型: 2
2025-07-26 15:29:38 - src.core.message_processor.2852835021056 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 7
2025-07-26 15:29:38 - src.core.message_processor.2852835021056 - INFO - 消息类型: 私聊
2025-07-26 15:29:38 - src.core.message_processor.2852835021056 - INFO - 用户 7881299718050125 不满足创建新会话条件，忽略文本消息
2025-07-26 15:46:24 - src.core.message_processor.2852835021056 - INFO - 收到原始消息: {"data": {"appinfo": "5027038161642850340", "at_list": [], "content": "7", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "2300", "receiver": "1688854710715177", "send_time": "1753515983", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1020093"}, "type": 11041}
2025-07-26 15:46:24 - src.core.message_processor.2852835021056 - INFO - 机器人名字: 邾俊勇
2025-07-26 15:46:24 - src.core.message_processor.2852835021056 - INFO - 消息类型: 2
2025-07-26 15:46:24 - src.core.message_processor.2852835021056 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 7
2025-07-26 15:46:24 - src.core.message_processor.2852835021056 - INFO - 消息类型: 私聊
2025-07-26 15:46:24 - src.core.message_processor.2852835021056 - INFO - 用户 7881299718050125 可以创建新会话（无活跃会话且满足间隔条件）
2025-07-26 15:46:24 - src.core.message_processor.2852835021056 - INFO - 数据库创建会话成功: 7881299718050125
2025-07-26 15:46:24 - src.core.message_processor.2852835021056 - INFO - 从文本消息重新创建会话并发送欢迎消息: 7881299718050125, 会话ID: 7881299718050125
2025-07-26 15:46:24 - src.core.message_processor.2852835021056 - INFO - 收到原始消息: {"data": {"appinfo": "CAEQ0I+SxAYYqYbwiJKAgAMgAw==", "at_list": [], "content": "您好！欢迎咨询，请问您需要以下哪项服务？请直接回复对应数字：\n1.查询快递物流信息\n2.投诉/反馈问题\n3.联系人工客服", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 1, "local_id": "2301", "receiver": "7881299718050125", "send_time": "1753515985", "sender": "1688854710715177", "sender_name": "邾俊勇", "server_id": "1020096"}, "type": 11041}
2025-07-26 15:46:24 - src.core.message_processor.2852835021056 - INFO - 机器人名字: 邾俊勇
2025-07-26 15:46:24 - src.core.message_processor.2852835021056 - INFO - 忽略自己发送的消息
2025-07-26 15:46:33 - src.core.message_processor.2852835021056 - INFO - 收到原始消息: {"data": {"appinfo": "6137959811613898696", "at_list": [], "content": "3", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "2302", "receiver": "1688854710715177", "send_time": "1753515992", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1020098"}, "type": 11041}
2025-07-26 15:46:33 - src.core.message_processor.2852835021056 - INFO - 机器人名字: 邾俊勇
2025-07-26 15:46:33 - src.core.message_processor.2852835021056 - INFO - 消息类型: 2
2025-07-26 15:46:33 - src.core.message_processor.2852835021056 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 3
2025-07-26 15:46:33 - src.core.message_processor.2852835021056 - INFO - 消息类型: 私聊
2025-07-26 15:46:33 - src.core.message_processor.2852835021056 - INFO - 开始处理特殊回复，内容长度: 580
2025-07-26 15:46:33 - src.core.message_processor.2852835021056 - INFO - 检测到特殊回复格式
2025-07-26 15:46:33 - src.core.message_processor.2852835021056 - INFO - 解析到的参数: {'wait_reply': '正在为您转接人工客服，请稍候...', 'aes_key': '7e2b31c325234382bcaa1c74b2f3e7d6', 'appicon': 'http://mmbiz.qpic.cn/sz_mmbiz_png/iaCrlo1rtEDKdOOEYjeAibS9kRtkqXfZM3TRaGmG5aTnSs6QkZv92cPjRaXbZtTHGdncC8cXkztziaYtQNKaVwHhA/640?wx_fmt=png&wxfrom=200\\nappid=wxeb51fb5f546fefda', 'appname': '银探-示例小程序', 'file_id': '3069020102046230600201000204211c032902030f42420204f663607a02046870cb90042435373864393532332d346233642d343730332d383538652d333765616437366134303939020100020303cb00041031707d814f740055715c414000e2a9f30201010201000400', 'page_path': 'pages/index/index.html', 'size': '248573', 'title': '精彩内容分享', 'username': 'gh_310c3cad0c90@app'}
2025-07-26 15:46:33 - src.core.message_processor.2852835021056 - INFO - 发送等待回复给私聊用户: 7881299718050125, 聊天ID: S:1688854710715177_7881299718050125
2025-07-26 15:46:33 - src.core.message_processor.2852835021056 - INFO - 收到原始消息: {"data": {"appinfo": "CAEQ2Y+SxAYYqYbwiJKAgAMgBA==", "at_list": [], "content": "正在为您转接人工客服，请稍候...", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 1, "local_id": "2303", "receiver": "7881299718050125", "send_time": "1753515994", "sender": "1688854710715177", "sender_name": "邾俊勇", "server_id": "1020101"}, "type": 11041}
2025-07-26 15:46:33 - src.core.message_processor.2852835021056 - INFO - 机器人名字: 邾俊勇
2025-07-26 15:46:34 - src.core.message_processor.2852835021056 - INFO - 忽略自己发送的消息
2025-07-26 15:46:34 - src.core.message_processor.2852835021056 - INFO - 小程序缺失参数: ['appid']
2025-07-26 15:46:34 - src.core.message_processor.2852835021056 - WARNING - 链接卡片参数不完整，缺失: ['desc', 'url', 'img']
2025-07-26 15:46:35 - src.core.message_processor.2852835021056 - INFO - 发送原始内容给私聊用户: 7881299718050125, 聊天ID: S:1688854710715177_7881299718050125
2025-07-26 15:46:35 - src.core.message_processor.2852835021056 - INFO - 会话已在数据库中标记为失效: 7881299718050125
2025-07-26 15:46:35 - src.core.message_processor.2852835021056 - INFO - 对话结束，清理会话: 7881299718050125, 记录完成时间: 1753515995.138254
2025-07-26 15:46:35 - src.core.message_processor.2852835021056 - INFO - 收到原始消息: {"data": {"appinfo": "CAEQ24+SxAYYqYbwiJKAgAMgBQ==", "at_list": [], "content": "wait_reply=正在为您转接人工客服，请稍候...\naes_key=7e2b31c325234382bcaa1c74b2f3e7d6\nappicon=http://mmbiz.qpic.cn/sz_mmbiz_png/iaCrlo1rtEDKdOOEYjeAibS9kRtkqXfZM3TRaGmG5aTnSs6QkZv92cPjRaXbZtTHGdncC8cXkztziaYtQNKaVwHhA/640?wx_fmt=png&wxfrom=200\\nappid=wxeb51fb5f546fefda\nappname=银探-示例小程序\nfile_id=3069020102046230600201000204211c032902030f42420204f663607a02046870cb90042435373864393532332d346233642d343730332d383538652d333765616437366134303939020100020303cb00041031707d814f740055715c414000e2a9f30201010201000400\npage_path=pages/index/index.html\nsize=248573\ntitle=精彩内容分享\nusername=gh_310c3cad0c90@app", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 1, "local_id": "2304", "receiver": "7881299718050125", "send_time": "1753515996", "sender": "1688854710715177", "sender_name": "邾俊勇", "server_id": "1020103"}, "type": 11041}
2025-07-26 15:46:35 - src.core.message_processor.2852835021056 - INFO - 机器人名字: 邾俊勇
2025-07-26 15:46:35 - src.core.message_processor.2852835021056 - INFO - 忽略自己发送的消息
2025-07-26 15:51:07 - src.core.message_processor.2852835021056 - INFO - 设置间隔时间为: 60秒 (1分钟)
2025-07-26 15:51:07 - src.core.message_processor.2852835021056 - INFO - 设置间隔时间为: 60秒 (1分钟)
