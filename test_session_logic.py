# -*- coding: utf-8 -*-
"""
测试会话创建逻辑 - 验证只在系统消息中创建会话，文本消息中完成选择
"""

import sys
import os
import json
import time

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_session_creation_logic():
    """测试会话创建逻辑"""
    print("=== 测试会话创建逻辑 ===")
    
    try:
        from src.core.message_processor import MessageProcessor
        from src.core.wechat_handler import WeChatHandler
        from src.utils.mock_ntwork import WeWork
        
        # 创建消息处理器实例
        wechat_handler = WeChatHandler()
        processor = MessageProcessor(wechat_handler, None)
        
        # 设置私聊配置
        processor.group_option_configs = {
            'private_chat': {
                'auto_reply': '请选择服务类型：\n1.咨询\n2.投诉\n3.建议',
                'option_replies': {
                    '1': '请详细描述您的咨询内容',
                    '2': '请说明您要投诉的问题',
                    '3': '感谢您的建议'
                }
            }
        }
        
        # 模拟微信实例
        mock_wechat = WeWork()
        
        # 测试1: 文本消息不应该创建会话
        print("\n--- 测试1: 文本消息不创建会话 ---")
        text_message = {
            "data": {
                "room_wxid": "S:1234567890",
                "from_wxid": "user123",
                "from_name": "测试用户",
                "msg": "1",
                "content_type": 0
            }
        }
        
        # 处理文本消息
        processor.on_recv_message(mock_wechat, text_message)
        
        # 检查是否创建了会话
        if "user123" not in processor.user_sessions:
            print("✅ 文本消息正确地没有创建会话")
        else:
            print("❌ 文本消息错误地创建了会话")
        
        # 测试2: 系统消息应该创建会话
        print("\n--- 测试2: 系统消息创建会话 ---")
        sys_message = {
            "data": {
                "type": "friend_verify",
                "content": "用户通过了好友验证",
                "conversation_id": "S:1234567890",
                "from_wxid": "user456"
            }
        }
        
        # 处理系统消息
        processor.on_sys_message(mock_wechat, sys_message)
        
        # 检查是否创建了会话
        if "user456" in processor.user_sessions:
            session = processor.user_sessions["user456"]
            if session.get('source') == 'system_message':
                print("✅ 系统消息正确地创建了会话")
            else:
                print("❌ 系统消息创建的会话缺少来源标记")
        else:
            print("❌ 系统消息没有创建会话")
        
        # 测试3: 有会话后文本消息应该被处理
        print("\n--- 测试3: 有会话后文本消息处理 ---")
        
        # 现在发送文本消息给有会话的用户
        text_message_with_session = {
            "data": {
                "room_wxid": "S:1234567890",
                "from_wxid": "user456",
                "from_name": "测试用户",
                "msg": "1",
                "content_type": 0
            }
        }
        
        # 处理文本消息
        processor.on_recv_message(mock_wechat, text_message_with_session)
        
        # 检查会话状态是否更新
        if "user456" in processor.user_sessions:
            print("✅ 有会话的用户文本消息被正确处理")
        else:
            print("❌ 有会话的用户文本消息处理失败")
        
        # 测试4: 重复系统消息不应该重复创建会话
        print("\n--- 测试4: 重复系统消息不重复创建会话 ---")
        
        # 再次发送系统消息给同一用户
        processor.on_sys_message(mock_wechat, sys_message)
        
        # 检查会话数量
        user456_sessions = [k for k in processor.user_sessions.keys() if k == "user456"]
        if len(user456_sessions) == 1:
            print("✅ 重复系统消息正确地没有重复创建会话")
        else:
            print("❌ 重复系统消息错误地重复创建了会话")
            
    except Exception as e:
        print(f"❌ 会话创建逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_session_timeout():
    """测试会话超时逻辑"""
    print("\n=== 测试会话超时逻辑 ===")
    
    try:
        from src.core.message_processor import MessageProcessor
        from src.core.wechat_handler import WeChatHandler
        
        # 创建消息处理器实例
        wechat_handler = WeChatHandler()
        processor = MessageProcessor(wechat_handler, None)
        
        # 手动创建一个过期会话
        session_id = "expired_user"
        old_time = time.time() - 400  # 超过5分钟
        
        processor.user_sessions[session_id] = {
            'start_time': old_time,
            'waiting_for_option': True,
            'chat_id': "S:1234567890",
            'current_path': [],
            'source': 'system_message'
        }
        
        # 检查现有会话（应该清理过期会话）
        current_time = time.time()
        session = processor._get_existing_session(session_id, current_time)
        
        if session is None and session_id not in processor.user_sessions:
            print("✅ 过期会话被正确清理")
        else:
            print("❌ 过期会话清理失败")
            
        # 测试定期清理功能
        print("\n--- 测试定期清理功能 ---")
        
        # 创建多个过期会话
        for i in range(3):
            expired_session_id = f"expired_user_{i}"
            processor.user_sessions[expired_session_id] = {
                'start_time': time.time() - 400,
                'waiting_for_option': True,
                'chat_id': f"S:123456789{i}",
                'current_path': [],
                'source': 'system_message'
            }
        
        # 创建一个正常会话
        normal_session_id = "normal_user"
        processor.user_sessions[normal_session_id] = {
            'start_time': time.time(),
            'waiting_for_option': True,
            'chat_id': "S:1234567890",
            'current_path': [],
            'source': 'system_message'
        }
        
        # 执行清理
        processor.cleanup_expired_sessions()
        
        # 检查结果
        remaining_sessions = list(processor.user_sessions.keys())
        if len(remaining_sessions) == 1 and normal_session_id in remaining_sessions:
            print("✅ 定期清理功能正确地只保留了正常会话")
        else:
            print(f"❌ 定期清理功能失败，剩余会话: {remaining_sessions}")
            
    except Exception as e:
        print(f"❌ 会话超时测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("微信私聊选项选择机器人 - 会话创建逻辑测试")
    print("=" * 60)
    
    test_session_creation_logic()
    test_session_timeout()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("\n会话管理逻辑总结：")
    print("1. ✅ 只有系统消息可以创建新会话")
    print("2. ✅ 文本消息只能在现有会话中处理选项")
    print("3. ✅ 重复系统消息不会重复创建会话")
    print("4. ✅ 会话超时自动清理机制正常")
    print("5. ✅ 定期清理过期会话功能正常")

if __name__ == "__main__":
    main()
