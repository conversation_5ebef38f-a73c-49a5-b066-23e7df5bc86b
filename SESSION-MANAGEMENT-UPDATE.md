# 微信私聊机器人 - 会话管理逻辑更新

## 更新概述

根据用户需求，对微信私聊机器人的会话管理逻辑进行了重要更新：

1. **只在系统消息中创建会话** - 当用户通过好友验证或客服名片进入时
2. **文本消息只处理选项选择** - 不创建新会话，只在现有会话中处理
3. **完善的会话生命周期管理** - 包括超时清理和自动结束

## 详细修改内容

### 1. 系统消息监控 (`on_sys_message`)

#### 新增功能：
- **注册系统消息回调**: `self.wechat.msg_register(ntwork.MT_RECV_SYS_MSG)`
- **新联系人检测**: 识别好友验证通过、客服名片进入等系统消息
- **会话创建**: 只在系统消息中创建新的用户会话
- **欢迎消息发送**: 自动发送配置的选项菜单

#### 支持的系统消息类型：
```python
# 好友验证通过
"好友" in content or "验证" in content or "通过" in content

# 客服名片进入
"客服" in content or "名片" in content or "进入" in content

# 新用户对话
"新用户" in content or "开始对话" in content
```

#### 会话创建逻辑：
```python
# 创建新会话（只在系统消息中）
self.user_sessions[session_id] = {
    'start_time': current_time,
    'waiting_for_option': True,
    'chat_id': conversation_id,
    'current_path': [],
    'source': 'system_message'  # 标记来源
}
```

### 2. 文本消息处理 (`on_recv_message`)

#### 修改内容：
- **移除会话创建**: 不再在文本消息中创建新会话
- **现有会话检查**: 使用 `_get_existing_session()` 只检查现有会话
- **选项处理**: 只在有活跃会话时处理用户的选项选择

#### 处理流程：
```python
# 1. 检查现有会话（不创建）
session = self._get_existing_session(session_id, current_time)

# 2. 没有会话则忽略消息
if not session:
    self.logger.info(f"用户 {from_wxid} 没有活跃会话，忽略文本消息")
    return

# 3. 处理选项选择
if session.get('waiting_for_option', False):
    self._handle_option_selection(...)
```

### 3. 会话生命周期管理

#### 会话状态：
- **创建**: 只在系统消息中创建
- **活跃**: 用户进行选项选择时
- **完成**: 选择final类型选项后自动结束
- **超时**: 5分钟无活动自动清理

#### 超时处理：
```python
# 会话超时检查（5分钟）
if current_time - session['start_time'] > 300:
    # 发送超时消息
    self._send_session_timeout_message(session_id, room_wxid)
    # 清理会话
    del self.user_sessions[session_id]
```

#### 定期清理：
- **清理频率**: 每2分钟执行一次
- **清理范围**: 所有超过5分钟的会话
- **清理通知**: 向用户发送会话超时消息

### 4. 微信处理器更新 (`wechat_handler.py`)

#### 回调注册更新：
```python
def register_message_callback(self, text_callback, sys_callback=None):
    # 注册文本消息回调
    self.wechat.msg_register(ntwork.MT_RECV_TEXT_MSG)(text_callback)
    
    # 注册系统消息回调
    if sys_callback:
        self.wechat.msg_register(ntwork.MT_RECV_SYS_MSG)(sys_callback)
```

#### 回调取消更新：
```python
def unregister_message_callback(self):
    # 取消文本消息回调
    self.wechat.msg_register(ntwork.MT_RECV_TEXT_MSG)(lambda w, m: None)
    # 取消系统消息回调
    self.wechat.msg_register(ntwork.MT_RECV_SYS_MSG)(lambda w, m: None)
```

### 5. 主窗口更新 (`main_window.py`)

#### 定时器管理：
- **启动定时器**: 监控开始时启动会话清理定时器
- **停止定时器**: 监控停止时停止定时器
- **清理频率**: 每2分钟清理一次过期会话

#### 回调注册：
```python
# 同时注册文本消息和系统消息回调
self.wechat_handler.register_message_callback(
    self.message_processor.on_recv_message,
    self.message_processor.on_sys_message
)
```

## 使用场景示例

### 场景1: 新用户通过好友验证
1. **系统消息**: 用户通过好友验证
2. **自动处理**: 检测到新联系人，创建会话
3. **发送欢迎**: 自动发送选项菜单
4. **等待选择**: 用户会话进入等待状态

### 场景2: 用户进行选项选择
1. **文本消息**: 用户发送选项数字（如"1"）
2. **会话检查**: 检查用户是否有活跃会话
3. **处理选择**: 根据选项配置发送对应回复
4. **更新状态**: 更新会话路径或结束会话

### 场景3: 会话超时处理
1. **定期检查**: 每2分钟检查一次所有会话
2. **超时识别**: 发现超过5分钟的会话
3. **发送通知**: 向用户发送超时消息
4. **清理会话**: 从内存中删除过期会话

## 配置要求

### 私聊配置结构：
```json
{
    "group_option_configs": {
        "private_chat": {
            "auto_reply": "欢迎消息和选项菜单",
            "option_replies": {
                "1": "选项1回复",
                "2": "选项2回复"
            }
        }
    }
}
```

### 系统消息格式：
```json
{
    "data": {
        "type": "friend_verify",
        "content": "用户通过了好友验证",
        "conversation_id": "S:1234567890",
        "from_wxid": "user123"
    }
}
```

## 优势特点

### 1. 精确的会话控制
- 只有真正的新联系人才会创建会话
- 避免了无效文本消息创建无用会话
- 确保每个会话都有明确的起始点

### 2. 资源优化
- 定期清理过期会话，避免内存泄漏
- 超时机制防止僵尸会话
- 高效的会话状态管理

### 3. 用户体验
- 新用户自动收到欢迎消息
- 会话超时有友好提示
- 选项选择流程清晰

### 4. 系统稳定性
- 异常处理完善
- 日志记录详细
- 状态管理可靠

## 测试验证

所有功能都通过了完整的测试验证：

- ✅ 系统消息正确创建会话
- ✅ 文本消息不创建新会话
- ✅ 重复系统消息不重复创建会话
- ✅ 会话超时自动清理
- ✅ 定期清理功能正常
- ✅ 选项选择流程完整

## 版本信息

- **更新版本**: v2.2.0
- **更新日期**: 2025-07-21
- **主要特性**: 系统消息监控 + 精确会话管理
- **兼容性**: 完全向后兼容
