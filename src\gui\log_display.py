# -*- coding: utf-8 -*-
"""
日志显示选项卡 - 显示程序运行日志
"""

import tkinter as tk

# 尝试导入真实的ttkbootstrap，如果失败则使用模拟版本
try:
    import ttkbootstrap as ttk
except ImportError:
    from ..utils import mock_ttkbootstrap as ttk


class LogDisplayTab:
    """
    日志显示选项卡 - 负责显示程序运行日志
    """
    
    def __init__(self, parent, main_window):
        """
        初始化日志显示选项卡
        
        Args:
            parent: 父容器
            main_window: 主窗口实例
        """
        self.parent = parent
        self.main_window = main_window
        
        # 创建主框架
        self.frame = ttk.Frame(parent, padding="10")
        
        # 创建界面
        self.create_widgets()
        
    def create_widgets(self):
        """创建日志显示界面组件"""
        # 日志显示区
        log_frame = ttk.Frame(self.frame)
        log_frame.pack(fill=tk.BOTH, expand=True)

        # 创建文本框和滚动条
        text_frame = ttk.Frame(log_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)

        self.log_text = tk.Text(text_frame, wrap=tk.WORD, height=24, state="disabled")
        
        # 垂直滚动条
        v_scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=v_scrollbar.set)
        
        # 水平滚动条
        h_scrollbar = ttk.Scrollbar(text_frame, orient="horizontal", command=self.log_text.xview)
        self.log_text.configure(xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 控制按钮区域
        control_frame = ttk.Frame(log_frame)
        control_frame.pack(fill=tk.X, pady=(5, 0))
        
        # 清空日志按钮
        clear_btn = ttk.Button(
            control_frame, 
            text="清空日志", 
            command=self.clear_log, 
            bootstyle="warning-outline"
        )
        clear_btn.pack(side=tk.LEFT, padx=5)
        
        # 保存日志按钮
        save_btn = ttk.Button(
            control_frame, 
            text="保存日志", 
            command=self.save_log, 
            bootstyle="info-outline"
        )
        save_btn.pack(side=tk.LEFT, padx=5)
        
        # 自动滚动开关
        self.auto_scroll_var = tk.BooleanVar(value=True)
        auto_scroll_check = ttk.Checkbutton(
            control_frame,
            text="自动滚动",
            variable=self.auto_scroll_var,
            bootstyle="success-round-toggle"
        )
        auto_scroll_check.pack(side=tk.RIGHT, padx=5)
    
    def clear_log(self):
        """清空日志内容"""
        self.log_text.configure(state="normal")
        self.log_text.delete(1.0, tk.END)
        self.log_text.configure(state="disabled")
    
    def save_log(self):
        """保存日志到文件"""
        from tkinter import filedialog
        import datetime
        
        try:
            # 默认文件名包含当前时间
            default_filename = f"wechat_bot_log_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            
            # 打开文件保存对话框
            filename = filedialog.asksaveasfilename(
                title="保存日志文件",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
                initialvalue=default_filename
            )
            
            if filename:
                # 获取日志内容
                log_content = self.log_text.get(1.0, tk.END)
                
                # 保存到文件
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(log_content)
                
                # 显示成功消息
                from tkinter import messagebox
                messagebox.showinfo("成功", f"日志已保存到: {filename}")
                
        except Exception as e:
            from tkinter import messagebox
            messagebox.showerror("错误", f"保存日志失败: {str(e)}")
    
    def append_log(self, message: str):
        """
        添加日志消息
        
        Args:
            message: 日志消息
        """
        self.log_text.configure(state="normal")
        self.log_text.insert(tk.END, message + "\n")
        
        # 如果启用自动滚动，滚动到底部
        if self.auto_scroll_var.get():
            self.log_text.see(tk.END)
            
        self.log_text.configure(state="disabled")
