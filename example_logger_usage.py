# -*- coding: utf-8 -*-
"""
日志配置使用示例 - 展示如何配置和使用日志系统
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.core.message_processor import MessageProcessor
from src.core.wechat_handler import WeChatHandler
from src.utils.logger_config import LoggerConfig, get_logger_config, setup_logger


def example_1_basic_usage():
    """示例1: 基本使用 - 使用预设配置"""
    print("=== 示例1: 基本使用 ===")
    
    # 方法1: 使用预设配置创建MessageProcessor
    wechat_handler = WeChatHandler()
    
    # 使用开发环境预设配置
    log_config = get_logger_config('development')
    message_processor = MessageProcessor(wechat_handler, log_config=log_config)
    
    # 测试日志输出
    message_processor.logger.info("这是一条信息日志")
    message_processor.logger.warning("这是一条警告日志")
    message_processor.logger.error("这是一条错误日志")
    
    print(f"日志文件路径: {log_config.get('log_file_path')}")
    print()


def example_2_custom_config():
    """示例2: 自定义配置"""
    print("=== 示例2: 自定义配置 ===")
    
    # 自定义日志配置
    custom_config = {
        'log_file_path': 'logs/custom_message_processor.log',
        'console_output': True,
        'log_level': 'DEBUG',
        'max_file_size': 5,  # 5MB
        'backup_count': 3
    }
    
    wechat_handler = WeChatHandler()
    message_processor = MessageProcessor(wechat_handler, log_config=custom_config)
    
    # 测试不同级别的日志
    message_processor.logger.debug("这是调试信息")
    message_processor.logger.info("这是普通信息")
    message_processor.logger.warning("这是警告信息")
    message_processor.logger.error("这是错误信息")
    
    print(f"自定义日志文件: {custom_config['log_file_path']}")
    print()


def example_3_preset_with_override():
    """示例3: 使用预设配置并覆盖部分参数"""
    print("=== 示例3: 预设配置 + 参数覆盖 ===")
    
    # 使用生产环境预设，但启用控制台输出
    log_config = get_logger_config(
        'production',
        console_output=True,  # 覆盖预设中的console_output=False
        log_file_path='logs/production_override.log'  # 覆盖默认路径
    )
    
    wechat_handler = WeChatHandler()
    message_processor = MessageProcessor(wechat_handler, log_config=log_config)
    
    message_processor.logger.info("生产环境配置，但启用了控制台输出")
    message_processor.logger.warning("这条消息会同时输出到文件和控制台")
    
    print(f"配置详情: {log_config}")
    print()


def example_4_multiple_loggers():
    """示例4: 创建多个独立的logger"""
    print("=== 示例4: 多个独立logger ===")
    
    # 创建不同配置的logger
    logger1 = setup_logger(
        'processor1',
        'debug',
        log_file_path='logs/processor1.log'
    )
    
    logger2 = setup_logger(
        'processor2',
        'production',
        console_output=True,
        log_file_path='logs/processor2.log'
    )
    
    # 测试独立性
    logger1.debug("来自processor1的调试信息")
    logger1.info("来自processor1的普通信息")
    
    logger2.info("来自processor2的普通信息")
    logger2.warning("来自processor2的警告信息")
    
    print("两个独立的logger已创建并测试")
    print()


def example_5_no_file_logging():
    """示例5: 仅控制台输出，不保存文件"""
    print("=== 示例5: 仅控制台输出 ===")
    
    # 使用minimal预设（不保存文件）
    log_config = get_logger_config('minimal')
    
    wechat_handler = WeChatHandler()
    message_processor = MessageProcessor(wechat_handler, log_config=log_config)
    
    message_processor.logger.warning("这条消息只会显示在控制台")
    message_processor.logger.error("不会保存到文件中")
    
    print("仅控制台输出模式测试完成")
    print()


def example_6_preset_in_config():
    """示例6: 在配置中直接指定预设"""
    print("=== 示例6: 配置中指定预设 ===")
    
    # 在配置字典中直接指定预设
    log_config = {
        'preset': 'debug',
        'log_file_path': 'logs/preset_debug.log',  # 覆盖预设中的路径
        'console_output': True
    }
    
    wechat_handler = WeChatHandler()
    message_processor = MessageProcessor(wechat_handler, log_config=log_config)
    
    message_processor.logger.debug("使用debug预设的调试信息")
    message_processor.logger.info("预设配置测试")
    
    print("预设配置测试完成")
    print()


def show_available_presets():
    """显示所有可用的预设配置"""
    print("=== 可用的预设配置 ===")
    
    presets = LoggerConfig.get_preset_configs()
    for name, config in presets.items():
        print(f"{name}:")
        for key, value in config.items():
            print(f"  {key}: {value}")
        print()


def main():
    """主函数 - 运行所有示例"""
    print("微信机器人日志配置使用示例\n")
    
    # 显示可用预设
    show_available_presets()
    
    # 运行各种示例
    example_1_basic_usage()
    example_2_custom_config()
    example_3_preset_with_override()
    example_4_multiple_loggers()
    example_5_no_file_logging()
    example_6_preset_in_config()
    
    print("所有示例运行完成！")
    print("\n检查以下目录查看生成的日志文件:")
    print("- logs/")
    
    # 检查日志文件是否存在
    logs_dir = 'logs'
    if os.path.exists(logs_dir):
        log_files = [f for f in os.listdir(logs_dir) if f.endswith('.log')]
        if log_files:
            print("\n生成的日志文件:")
            for log_file in sorted(log_files):
                file_path = os.path.join(logs_dir, log_file)
                file_size = os.path.getsize(file_path)
                print(f"  {log_file} ({file_size} bytes)")


if __name__ == "__main__":
    main()
