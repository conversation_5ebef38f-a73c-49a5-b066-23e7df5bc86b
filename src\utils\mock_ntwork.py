# -*- coding: utf-8 -*-
"""
模拟ntwork模块 - 用于测试项目结构
"""

import time
import logging

# 模拟常量
MT_RECV_TEXT_MSG = "MT_RECV_TEXT_MSG"
MT_RECV_SYS_MSG = "MT_RECV_SYS_MSG"


class WeWork:
    """模拟企业微信类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.is_opened = False
        self.is_logged_in = False
        
    def open(self, smart=True):
        """模拟打开企业微信"""
        self.logger.info("模拟：打开企业微信")
        self.is_opened = True
        
    def wait_login(self, timeout=500):
        """模拟等待登录"""
        self.logger.info("模拟：等待登录")
        time.sleep(1)  # 模拟等待时间
        self.is_logged_in = True
        
    def get_login_info(self):
        """模拟获取登录信息"""
        if self.is_logged_in:
            return {
                "user_id": "mock_user_123",
                "username": "测试用户",
                "nickname": "Mock User"
            }
        return None
        
    def get_rooms(self, page_num=1, page_size=500):
        """模拟获取群列表"""
        if not self.is_logged_in:
            return None
            
        # 模拟群列表数据
        mock_rooms = []
        start_idx = (page_num - 1) * page_size
        
        # 生成一些模拟群数据
        for i in range(min(page_size, 10)):  # 最多返回10个群
            room_id = f"R:mock_room_{start_idx + i + 1}"
            mock_rooms.append({
                "conversation_id": room_id,
                "nickname": f"测试群{start_idx + i + 1}",
                "member_count": 50 + i
            })
            
        return {
            "room_list": mock_rooms,
            "total": 10
        }
        
    def send_text(self, conversation_id, content):
        """模拟发送文本消息"""
        self.logger.info(f"模拟发送文本消息到 {conversation_id}: {content}")
        return True
        
    def send_link_card(self, conversation_id, title, desc, url, image_url):
        """模拟发送链接卡片"""
        self.logger.info(f"模拟发送链接卡片到 {conversation_id}: {title}")
        return True
        
    def send_card(self, conversation_id, user_id):
        """模拟发送用户卡片"""
        self.logger.info(f"模拟发送用户卡片到 {conversation_id}: {user_id}")
        return True

    def send_image(self, conversation_id, file_path):
        """模拟发送图片/文件"""
        self.logger.info(f"模拟发送图片/文件到 {conversation_id}: {file_path}")
        return True

    def send_video(self, conversation_id, file_path):
        """模拟发送视频"""
        self.logger.info(f"模拟发送视频到 {conversation_id}: {file_path}")
        return True

    def send_miniapp(self, aes_key, file_id, size, appicon, appid, appname,
                    conversation_id, page_path, title, username):
        """模拟发送小程序"""
        self.logger.info(f"模拟发送小程序到 {conversation_id}: {title} (appid: {appid})")
        return True
        
    def msg_register(self, msg_type):
        """模拟消息注册"""
        def decorator(callback):
            self.logger.info(f"模拟注册消息回调: {msg_type}")
            return callback
        return decorator


def exit_():
    """模拟退出函数"""
    logging.getLogger(__name__).info("模拟：退出ntwork")
    pass
