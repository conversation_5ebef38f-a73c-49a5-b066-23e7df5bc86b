2025-07-17 22:07:02 - src.core.message_processor.2241435685408 - INFO - 日志文件已配置: D:\python\wechat\ntwork\选项选择\logs\wechat_bot_20250717.log
2025-07-17 22:07:02 - src.core.message_processor.2241435685408 - INFO - 消息处理器日志系统初始化完成
2025-07-17 22:09:33 - src.core.message_processor.2241435685408 - INFO - 收到原始消息: {"data": {"appinfo": "2358084805602428574", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "R:10881056632797218", "is_pc": 0, "local_id": "1437", "receiver": "1688854710715177", "send_time": "1752761372", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1016286"}, "type": 11041}
2025-07-17 22:09:33 - src.core.message_processor.2241435685408 - INFO - 机器人名字: 邾俊勇
2025-07-17 22:09:33 - src.core.message_processor.2241435685408 - INFO - 消息类型: 2
2025-07-17 22:09:33 - src.core.message_processor.2241435685408 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: R:10881056632797218 | 内容: 1
2025-07-17 22:09:33 - src.core.message_processor.2241435685408 - INFO - 消息类型: 群聊
2025-07-17 22:09:33 - src.core.message_processor.2241435685408 - INFO - 当前监控群列表: ['R:10881056632797218']
2025-07-17 22:09:33 - src.core.message_processor.2241435685408 - INFO - 群消息检查 | 群ID: R:10881056632797218 | 是否在监控列表: True
2025-07-17 22:09:33 - src.core.message_processor.2241435685408 - INFO - 发送自动回复给用户: 7881299718050125, 会话ID: R:10881056632797218_7881299718050125, 群ID: R:10881056632797218
2025-07-17 22:09:34 - src.core.message_processor.2241435685408 - INFO - 收到原始消息: {"data": {"appinfo": "CIGABBCdiOTDBhiphvCIkoCAAyAF", "at_list": [], "content": "请问您需要咨询以下哪项业务？请直接回复对应数字：\n1.查询快递物流信息\n2.投诉/反馈问题（丢件/破损/延误等）\n3.联系人工客服", "content_type": 2, "conversation_id": "R:10881056632797218", "is_pc": 1, "local_id": "1438", "receiver": "R:10881056632797218", "send_time": "1752761374", "sender": "1688854710715177", "sender_name": "邾俊勇", "server_id": "1016291"}, "type": 11041}
2025-07-17 22:09:34 - src.core.message_processor.2241435685408 - INFO - 机器人名字: 邾俊勇
2025-07-17 22:09:34 - src.core.message_processor.2241435685408 - INFO - 忽略自己发送的消息
2025-07-17 22:09:40 - src.core.message_processor.2241435685408 - INFO - 收到原始消息: {"data": {"appinfo": "212338510027018366", "at_list": [], "content": "3", "content_type": 2, "conversation_id": "R:10881056632797218", "is_pc": 0, "local_id": "1439", "receiver": "1688854710715177", "send_time": "1752761379", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1016293"}, "type": 11041}
2025-07-17 22:09:40 - src.core.message_processor.2241435685408 - INFO - 机器人名字: 邾俊勇
2025-07-17 22:09:40 - src.core.message_processor.2241435685408 - INFO - 消息类型: 2
2025-07-17 22:09:40 - src.core.message_processor.2241435685408 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: R:10881056632797218 | 内容: 3
2025-07-17 22:09:40 - src.core.message_processor.2241435685408 - INFO - 消息类型: 群聊
2025-07-17 22:09:40 - src.core.message_processor.2241435685408 - INFO - 当前监控群列表: ['R:10881056632797218']
2025-07-17 22:09:40 - src.core.message_processor.2241435685408 - INFO - 群消息检查 | 群ID: R:10881056632797218 | 是否在监控列表: True
2025-07-17 22:09:40 - src.core.message_processor.2241435685408 - INFO - 开始处理特殊回复，内容长度: 580
2025-07-17 22:09:40 - src.core.message_processor.2241435685408 - INFO - 检测到特殊回复格式
2025-07-17 22:09:40 - src.core.message_processor.2241435685408 - INFO - 解析到的参数: {'wait_reply': '正在为您转接人工客服，请稍候...', 'aes_key': '7e2b31c325234382bcaa1c74b2f3e7d6', 'appicon': 'http://mmbiz.qpic.cn/sz_mmbiz_png/iaCrlo1rtEDKdOOEYjeAibS9kRtkqXfZM3TRaGmG5aTnSs6QkZv92cPjRaXbZtTHGdncC8cXkztziaYtQNKaVwHhA/640?wx_fmt=png&wxfrom=200', 'appid': 'wxeb51fb5f546fefda', 'appname': '银探-示例小程序', 'file_id': '3069020102046230600201000204211c032902030f42420204f663607a02046870cb90042435373864393532332d346233642d343730332d383538652d333765616437366134303939020100020303cb00041031707d814f740055715c414000e2a9f30201010201000400', 'page_path': 'pages/index/index.html', 'size': '248573', 'title': '精彩内容分享', 'username': 'gh_310c3cad0c90@app'}
2025-07-17 22:09:40 - src.core.message_processor.2241435685408 - INFO - 发送等待回复给用户: 7881299718050125, 群ID: R:10881056632797218
2025-07-17 22:09:40 - src.core.message_processor.2241435685408 - INFO - 收到原始消息: {"data": {"appinfo": "CIGABBCkiOTDBhiphvCIkoCAAyAH", "at_list": [], "content": "正在为您转接人工客服，请稍候...", "content_type": 2, "conversation_id": "R:10881056632797218", "is_pc": 1, "local_id": "1440", "receiver": "R:10881056632797218", "send_time": "1752761380", "sender": "1688854710715177", "sender_name": "邾俊勇", "server_id": "1016298"}, "type": 11041}
2025-07-17 22:09:40 - src.core.message_processor.2241435685408 - INFO - 机器人名字: 邾俊勇
2025-07-17 22:09:40 - src.core.message_processor.2241435685408 - INFO - 忽略自己发送的消息
2025-07-17 22:09:41 - src.core.message_processor.2241435685408 - INFO - 小程序缺失参数: []
2025-07-17 22:09:41 - src.core.message_processor.2241435685408 - INFO - 检测到小程序格式，尝试发送小程序
2025-07-17 22:09:41 - src.core.message_processor.2241435685408 - INFO - 调用send_miniapp: conversation_id=R:10881056632797218
2025-07-17 22:09:41 - src.core.message_processor.2241435685408 - INFO - ✅ 成功发送小程序给用户: 7881299718050125, 群ID: R:10881056632797218
2025-07-17 22:09:41 - src.core.message_processor.2241435685408 - INFO - 对话结束，清理会话: R:10881056632797218_7881299718050125
2025-07-17 22:32:09 - src.core.message_processor.1419170936960 - INFO - 日志文件已配置: D:\python\wechat\ntwork\选项选择\logs\wechat_bot_20250717.log
2025-07-17 22:32:09 - src.core.message_processor.1419170936960 - INFO - 消息处理器日志系统初始化完成
2025-07-17 22:33:14 - src.core.message_processor.1419170936960 - INFO - 收到原始消息: {"data": {"appinfo": "2801854491265510012", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "R:10881056632797218", "is_pc": 0, "local_id": "1441", "receiver": "1688854710715177", "send_time": "1752762793", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1016304"}, "type": 11041}
2025-07-17 22:33:14 - src.core.message_processor.1419170936960 - INFO - 机器人名字: 邾俊勇
2025-07-17 22:33:14 - src.core.message_processor.1419170936960 - INFO - 消息类型: 2
2025-07-17 22:33:14 - src.core.message_processor.1419170936960 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: R:10881056632797218 | 内容: 1
2025-07-17 22:33:14 - src.core.message_processor.1419170936960 - INFO - 消息类型: 群聊
2025-07-17 22:33:14 - src.core.message_processor.1419170936960 - INFO - 当前监控群列表: ['R:10881056632797218']
2025-07-17 22:33:14 - src.core.message_processor.1419170936960 - INFO - 群消息检查 | 群ID: R:10881056632797218 | 是否在监控列表: True
2025-07-17 22:33:14 - src.core.message_processor.1419170936960 - INFO - 发送自动回复给用户: 7881299718050125, 会话ID: R:10881056632797218_7881299718050125, 群ID: R:10881056632797218
2025-07-17 22:33:14 - src.core.message_processor.1419170936960 - INFO - 收到原始消息: {"data": {"appinfo": "CIGABBCqk+TDBhiphvCIkoCAAyAK", "at_list": [], "content": "请问您需要咨询以下哪项业务？请直接回复对应数字：\n1.查询快递物流信息\n2.投诉/反馈问题（丢件/破损/延误等）\n3.联系人工客服", "content_type": 2, "conversation_id": "R:10881056632797218", "is_pc": 1, "local_id": "1442", "receiver": "R:10881056632797218", "send_time": "1752762794", "sender": "1688854710715177", "sender_name": "邾俊勇", "server_id": "1016307"}, "type": 11041}
2025-07-17 22:33:14 - src.core.message_processor.1419170936960 - INFO - 机器人名字: 邾俊勇
2025-07-17 22:33:14 - src.core.message_processor.1419170936960 - INFO - 忽略自己发送的消息
2025-07-17 22:33:21 - src.core.message_processor.1419170936960 - INFO - 收到原始消息: {"data": {"appinfo": "7004966696148416852", "at_list": [], "content": "3", "content_type": 2, "conversation_id": "R:10881056632797218", "is_pc": 0, "local_id": "1443", "receiver": "1688854710715177", "send_time": "1752762799", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1016309"}, "type": 11041}
2025-07-17 22:33:21 - src.core.message_processor.1419170936960 - INFO - 机器人名字: 邾俊勇
2025-07-17 22:33:21 - src.core.message_processor.1419170936960 - INFO - 消息类型: 2
2025-07-17 22:33:21 - src.core.message_processor.1419170936960 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: R:10881056632797218 | 内容: 3
2025-07-17 22:33:21 - src.core.message_processor.1419170936960 - INFO - 消息类型: 群聊
2025-07-17 22:33:21 - src.core.message_processor.1419170936960 - INFO - 当前监控群列表: ['R:10881056632797218']
2025-07-17 22:33:21 - src.core.message_processor.1419170936960 - INFO - 群消息检查 | 群ID: R:10881056632797218 | 是否在监控列表: True
2025-07-17 22:33:21 - src.core.message_processor.1419170936960 - INFO - 开始处理特殊回复，内容长度: 580
2025-07-17 22:33:21 - src.core.message_processor.1419170936960 - INFO - 检测到特殊回复格式
2025-07-17 22:33:21 - src.core.message_processor.1419170936960 - INFO - 解析到的参数: {'wait_reply': '正在为您转接人工客服，请稍候...', 'aes_key': '7e2b31c325234382bcaa1c74b2f3e7d6', 'appicon': 'http://mmbiz.qpic.cn/sz_mmbiz_png/iaCrlo1rtEDKdOOEYjeAibS9kRtkqXfZM3TRaGmG5aTnSs6QkZv92cPjRaXbZtTHGdncC8cXkztziaYtQNKaVwHhA/640?wx_fmt=png&wxfrom=200', 'appid': 'wxeb51fb5f546fefda', 'appname': '银探-示例小程序', 'file_id': '3069020102046230600201000204211c032902030f42420204f663607a02046870cb90042435373864393532332d346233642d343730332d383538652d333765616437366134303939020100020303cb00041031707d814f740055715c414000e2a9f30201010201000400', 'page_path': 'pages/index/index.html', 'size': '248573', 'title': '精彩内容分享', 'username': 'gh_310c3cad0c90@app'}
2025-07-17 22:33:21 - src.core.message_processor.1419170936960 - INFO - 发送等待回复给用户: 7881299718050125, 群ID: R:10881056632797218
2025-07-17 22:33:21 - src.core.message_processor.1419170936960 - INFO - 收到原始消息: {"data": {"appinfo": "CIGABBCxk+TDBhiphvCIkoCAAyAL", "at_list": [], "content": "正在为您转接人工客服，请稍候...", "content_type": 2, "conversation_id": "R:10881056632797218", "is_pc": 1, "local_id": "1444", "receiver": "R:10881056632797218", "send_time": "1752762801", "sender": "1688854710715177", "sender_name": "邾俊勇", "server_id": "1016312"}, "type": 11041}
2025-07-17 22:33:21 - src.core.message_processor.1419170936960 - INFO - 机器人名字: 邾俊勇
2025-07-17 22:33:21 - src.core.message_processor.1419170936960 - INFO - 忽略自己发送的消息
2025-07-17 22:33:22 - src.core.message_processor.1419170936960 - INFO - 小程序缺失参数: []
2025-07-17 22:33:22 - src.core.message_processor.1419170936960 - INFO - 检测到小程序格式，尝试发送小程序
2025-07-17 22:33:22 - src.core.message_processor.1419170936960 - INFO - 调用send_miniapp: conversation_id=R:10881056632797218
2025-07-17 22:33:22 - src.core.message_processor.1419170936960 - INFO - send_miniapp返回: True
2025-07-17 22:33:22 - src.core.message_processor.1419170936960 - INFO - ✅ 成功发送小程序给用户: 7881299718050125, 群ID: R:10881056632797218
2025-07-17 22:33:22 - src.core.message_processor.1419170936960 - INFO - 对话结束，清理会话: R:10881056632797218_7881299718050125
2025-07-17 22:35:46 - src.core.message_processor.2621826922624 - INFO - 日志文件已配置: D:\python\wechat\ntwork\选项选择\logs\wechat_bot_20250717.log
2025-07-17 22:35:46 - src.core.message_processor.2621826922624 - INFO - 消息处理器日志系统初始化完成
2025-07-17 22:39:46 - src.core.message_processor.2621826922624 - INFO - 收到原始消息: {"data": {"appinfo": "8002724996883960139", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "R:10881056632797218", "is_pc": 0, "local_id": "1445", "receiver": "1688854710715177", "send_time": "1752763184", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1016314"}, "type": 11041}
2025-07-17 22:39:46 - src.core.message_processor.2621826922624 - INFO - 机器人名字: 邾俊勇
2025-07-17 22:39:46 - src.core.message_processor.2621826922624 - INFO - 消息类型: 2
2025-07-17 22:39:46 - src.core.message_processor.2621826922624 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: R:10881056632797218 | 内容: 1
2025-07-17 22:39:46 - src.core.message_processor.2621826922624 - INFO - 消息类型: 群聊
2025-07-17 22:39:46 - src.core.message_processor.2621826922624 - INFO - 当前监控群列表: ['R:10881056632797218']
2025-07-17 22:39:46 - src.core.message_processor.2621826922624 - INFO - 群消息检查 | 群ID: R:10881056632797218 | 是否在监控列表: True
2025-07-17 22:39:46 - src.core.message_processor.2621826922624 - INFO - 发送自动回复给用户: 7881299718050125, 会话ID: R:10881056632797218_7881299718050125, 群ID: R:10881056632797218
2025-07-17 22:39:46 - src.core.message_processor.2621826922624 - INFO - 收到原始消息: {"data": {"appinfo": "CIGABBCyluTDBhiphvCIkoCAAyAM", "at_list": [], "content": "请问您需要咨询以下哪项业务？请直接回复对应数字：\n1.查询快递物流信息\n2.投诉/反馈问题（丢件/破损/延误等）\n3.联系人工客服", "content_type": 2, "conversation_id": "R:10881056632797218", "is_pc": 1, "local_id": "1446", "receiver": "R:10881056632797218", "send_time": "1752763186", "sender": "1688854710715177", "sender_name": "邾俊勇", "server_id": "1016317"}, "type": 11041}
2025-07-17 22:39:46 - src.core.message_processor.2621826922624 - INFO - 机器人名字: 邾俊勇
2025-07-17 22:39:46 - src.core.message_processor.2621826922624 - INFO - 忽略自己发送的消息
2025-07-17 22:39:51 - src.core.message_processor.2621826922624 - INFO - 收到原始消息: {"data": {"appinfo": "2295000211211351714", "at_list": [], "content": "3", "content_type": 2, "conversation_id": "R:10881056632797218", "is_pc": 0, "local_id": "1447", "receiver": "1688854710715177", "send_time": "1752763190", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1016319"}, "type": 11041}
2025-07-17 22:39:51 - src.core.message_processor.2621826922624 - INFO - 机器人名字: 邾俊勇
2025-07-17 22:39:51 - src.core.message_processor.2621826922624 - INFO - 消息类型: 2
2025-07-17 22:39:51 - src.core.message_processor.2621826922624 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: R:10881056632797218 | 内容: 3
2025-07-17 22:39:51 - src.core.message_processor.2621826922624 - INFO - 消息类型: 群聊
2025-07-17 22:39:51 - src.core.message_processor.2621826922624 - INFO - 当前监控群列表: ['R:10881056632797218']
2025-07-17 22:39:51 - src.core.message_processor.2621826922624 - INFO - 群消息检查 | 群ID: R:10881056632797218 | 是否在监控列表: True
2025-07-17 22:39:51 - src.core.message_processor.2621826922624 - INFO - 开始处理特殊回复，内容长度: 580
2025-07-17 22:39:51 - src.core.message_processor.2621826922624 - INFO - 检测到特殊回复格式
2025-07-17 22:39:51 - src.core.message_processor.2621826922624 - INFO - 解析到的参数: {'wait_reply': '正在为您转接人工客服，请稍候...', 'aes_key': '7e2b31c325234382bcaa1c74b2f3e7d6', 'appicon': 'http://mmbiz.qpic.cn/sz_mmbiz_png/iaCrlo1rtEDKdOOEYjeAibS9kRtkqXfZM3TRaGmG5aTnSs6QkZv92cPjRaXbZtTHGdncC8cXkztziaYtQNKaVwHhA/640?wx_fmt=png&wxfrom=200', 'appid': 'wxeb51fb5f546fefda', 'appname': '银探-示例小程序', 'file_id': '3069020102046230600201000204211c032902030f42420204f663607a02046870cb90042435373864393532332d346233642d343730332d383538652d333765616437366134303939020100020303cb00041031707d814f740055715c414000e2a9f30201010201000400', 'page_path': 'pages/index/index.html', 'size': '248573', 'title': '精彩内容分享', 'username': 'gh_310c3cad0c90@app'}
2025-07-17 22:39:51 - src.core.message_processor.2621826922624 - INFO - 发送等待回复给用户: 7881299718050125, 群ID: R:10881056632797218
2025-07-17 22:39:51 - src.core.message_processor.2621826922624 - INFO - 收到原始消息: {"data": {"appinfo": "CIGABBC3luTDBhiphvCIkoCAAyAN", "at_list": [], "content": "正在为您转接人工客服，请稍候...", "content_type": 2, "conversation_id": "R:10881056632797218", "is_pc": 1, "local_id": "1448", "receiver": "R:10881056632797218", "send_time": "1752763191", "sender": "1688854710715177", "sender_name": "邾俊勇", "server_id": "1016322"}, "type": 11041}
2025-07-17 22:39:51 - src.core.message_processor.2621826922624 - INFO - 机器人名字: 邾俊勇
2025-07-17 22:39:52 - src.core.message_processor.2621826922624 - INFO - 忽略自己发送的消息
2025-07-17 22:39:53 - src.core.message_processor.2621826922624 - INFO - 小程序缺失参数: []
2025-07-17 22:39:53 - src.core.message_processor.2621826922624 - INFO - 检测到小程序格式，尝试发送小程序
2025-07-17 22:39:53 - src.core.message_processor.2621826922624 - INFO - 调用send_miniapp: conversation_id=R:10881056632797218
2025-07-17 22:39:53 - src.core.message_processor.2621826922624 - INFO - send_miniapp返回: True
2025-07-17 22:39:53 - src.core.message_processor.2621826922624 - INFO - ✅ 成功发送小程序给用户: 7881299718050125, 群ID: R:10881056632797218
2025-07-17 22:39:53 - src.core.message_processor.2621826922624 - INFO - 对话结束，清理会话: R:10881056632797218_7881299718050125
2025-07-17 22:48:35 - src.core.message_processor.2352309205120 - INFO - 日志文件已配置: D:\python\wechat\ntwork\选项选择\logs\wechat_bot_20250717.log
2025-07-17 22:48:35 - src.core.message_processor.2352309205120 - INFO - 消息处理器日志系统初始化完成
2025-07-17 22:50:02 - src.core.message_processor.2352309205120 - INFO - 收到原始消息: {"data": {"appinfo": "5798533847278835100", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "R:10881056632797218", "is_pc": 0, "local_id": "1449", "receiver": "1688854710715177", "send_time": "1752763800", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1016326"}, "type": 11041}
2025-07-17 22:50:02 - src.core.message_processor.2352309205120 - INFO - 机器人名字: 邾俊勇
2025-07-17 22:50:02 - src.core.message_processor.2352309205120 - INFO - 消息类型: 2
2025-07-17 22:50:02 - src.core.message_processor.2352309205120 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: R:10881056632797218 | 内容: 1
2025-07-17 22:50:02 - src.core.message_processor.2352309205120 - INFO - 消息类型: 群聊
2025-07-17 22:50:02 - src.core.message_processor.2352309205120 - INFO - 当前监控群列表: ['R:10881056632797218']
2025-07-17 22:50:02 - src.core.message_processor.2352309205120 - INFO - 群消息检查 | 群ID: R:10881056632797218 | 是否在监控列表: True
2025-07-17 22:50:02 - src.core.message_processor.2352309205120 - INFO - 发送自动回复给用户: 7881299718050125, 会话ID: R:10881056632797218_7881299718050125, 群ID: R:10881056632797218
2025-07-17 22:50:02 - src.core.message_processor.2352309205120 - INFO - 收到原始消息: {"data": {"appinfo": "CIGABBCam+TDBhiphvCIkoCAAyAP", "at_list": [], "content": "请问您需要咨询以下哪项业务？请直接回复对应数字：\n1.查询快递物流信息\n2.投诉/反馈问题（丢件/破损/延误等）\n3.联系人工客服", "content_type": 2, "conversation_id": "R:10881056632797218", "is_pc": 1, "local_id": "1450", "receiver": "R:10881056632797218", "send_time": "1752763802", "sender": "1688854710715177", "sender_name": "邾俊勇", "server_id": "1016331"}, "type": 11041}
2025-07-17 22:50:02 - src.core.message_processor.2352309205120 - INFO - 机器人名字: 邾俊勇
2025-07-17 22:50:02 - src.core.message_processor.2352309205120 - INFO - 忽略自己发送的消息
2025-07-17 22:50:09 - src.core.message_processor.2352309205120 - INFO - 收到原始消息: {"data": {"appinfo": "3752411233839853104", "at_list": [], "content": "3", "content_type": 2, "conversation_id": "R:10881056632797218", "is_pc": 0, "local_id": "1451", "receiver": "1688854710715177", "send_time": "1752763808", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1016333"}, "type": 11041}
2025-07-17 22:50:09 - src.core.message_processor.2352309205120 - INFO - 机器人名字: 邾俊勇
2025-07-17 22:50:09 - src.core.message_processor.2352309205120 - INFO - 消息类型: 2
2025-07-17 22:50:09 - src.core.message_processor.2352309205120 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: R:10881056632797218 | 内容: 3
2025-07-17 22:50:09 - src.core.message_processor.2352309205120 - INFO - 消息类型: 群聊
2025-07-17 22:50:09 - src.core.message_processor.2352309205120 - INFO - 当前监控群列表: ['R:10881056632797218']
2025-07-17 22:50:09 - src.core.message_processor.2352309205120 - INFO - 群消息检查 | 群ID: R:10881056632797218 | 是否在监控列表: True
2025-07-17 22:50:09 - src.core.message_processor.2352309205120 - INFO - 开始处理特殊回复，内容长度: 580
2025-07-17 22:50:09 - src.core.message_processor.2352309205120 - INFO - 检测到特殊回复格式
2025-07-17 22:50:09 - src.core.message_processor.2352309205120 - INFO - 解析到的参数: {'wait_reply': '正在为您转接人工客服，请稍候...', 'aes_key': '7e2b31c325234382bcaa1c74b2f3e7d6', 'appicon': 'http://mmbiz.qpic.cn/sz_mmbiz_png/iaCrlo1rtEDKdOOEYjeAibS9kRtkqXfZM3TRaGmG5aTnSs6QkZv92cPjRaXbZtTHGdncC8cXkztziaYtQNKaVwHhA/640?wx_fmt=png&wxfrom=200', 'appid': 'wxeb51fb5f546fefda', 'appname': '银探-示例小程序', 'file_id': '3069020102046230600201000204211c032902030f42420204f663607a02046870cb90042435373864393532332d346233642d343730332d383538652d333765616437366134303939020100020303cb00041031707d814f740055715c414000e2a9f30201010201000400', 'page_path': 'pages/index/index.html', 'size': '248573', 'title': '精彩内容分享', 'username': 'gh_310c3cad0c90@app'}
2025-07-17 22:50:09 - src.core.message_processor.2352309205120 - INFO - 发送等待回复给用户: 7881299718050125, 群ID: R:10881056632797218
2025-07-17 22:50:09 - src.core.message_processor.2352309205120 - INFO - 收到原始消息: {"data": {"appinfo": "CIGABBChm+TDBhiphvCIkoCAAyAR", "at_list": [], "content": "正在为您转接人工客服，请稍候...", "content_type": 2, "conversation_id": "R:10881056632797218", "is_pc": 1, "local_id": "1452", "receiver": "R:10881056632797218", "send_time": "1752763809", "sender": "1688854710715177", "sender_name": "邾俊勇", "server_id": "1016338"}, "type": 11041}
2025-07-17 22:50:09 - src.core.message_processor.2352309205120 - INFO - 机器人名字: 邾俊勇
2025-07-17 22:50:09 - src.core.message_processor.2352309205120 - INFO - 忽略自己发送的消息
2025-07-17 22:50:10 - src.core.message_processor.2352309205120 - INFO - 小程序缺失参数: []
2025-07-17 22:50:10 - src.core.message_processor.2352309205120 - INFO - 检测到小程序格式，尝试发送小程序
2025-07-17 22:50:10 - src.core.message_processor.2352309205120 - INFO - 调用send_miniapp: conversation_id=R:10881056632797218
2025-07-17 22:50:10 - src.core.message_processor.2352309205120 - INFO - send_miniapp返回: True
2025-07-17 22:50:10 - src.core.message_processor.2352309205120 - INFO - ✅ 成功发送小程序给用户: 7881299718050125, 群ID: R:10881056632797218
2025-07-17 22:50:10 - src.core.message_processor.2352309205120 - INFO - 对话结束，清理会话: R:10881056632797218_7881299718050125
2025-07-17 23:56:50 - src.core.message_processor.2520791767408 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250717.log
2025-07-17 23:56:50 - src.core.message_processor.2520791767408 - INFO - 消息处理器日志系统初始化完成
2025-07-17 23:58:05 - src.core.message_processor.1780915015744 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250717.log
2025-07-17 23:58:05 - src.core.message_processor.1780915015744 - INFO - 消息处理器日志系统初始化完成
