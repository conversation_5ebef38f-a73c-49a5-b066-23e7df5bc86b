# -*- coding: utf-8 -*-
"""
测试数据库修复 - 验证安全检查和回退机制
"""

import sys
import os
import time

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_database_initialization():
    """测试数据库初始化"""
    print("=== 测试数据库初始化 ===")
    
    try:
        from src.core.message_processor import MessageProcessor
        from src.core.wechat_handler import WeChatHandler
        
        # 创建消息处理器实例
        wechat_handler = WeChatHandler()
        processor = MessageProcessor(wechat_handler, None)
        
        # 检查数据库管理器是否正确初始化
        if processor.session_db is not None:
            print("✅ 数据库管理器初始化成功")
            print(f"   数据库类型: {type(processor.session_db).__name__}")
        else:
            print("❌ 数据库管理器初始化失败")
            return False
        
        # 测试数据库基本功能
        test_session_id = "test_init_user"
        test_chat_id = "S:test_init_chat"
        
        # 创建会话
        success = processor.session_db.create_active_session(test_session_id, test_chat_id, "test")
        if success:
            print("✅ 数据库创建会话功能正常")
        else:
            print("❌ 数据库创建会话功能异常")
        
        # 获取会话
        session = processor.session_db.get_active_session(test_session_id)
        if session:
            print("✅ 数据库获取会话功能正常")
        else:
            print("❌ 数据库获取会话功能异常")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库初始化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_message_processing_with_database():
    """测试带数据库的消息处理"""
    print("\n=== 测试带数据库的消息处理 ===")
    
    try:
        from src.core.message_processor import MessageProcessor
        from src.core.wechat_handler import WeChatHandler
        from src.utils.mock_ntwork import WeWork
        
        # 创建消息处理器实例
        wechat_handler = WeChatHandler()
        processor = MessageProcessor(wechat_handler, None)
        
        # 设置私聊配置
        processor.group_option_configs = {
            'private_chat': {
                'auto_reply': '请选择服务类型：\n1.咨询\n2.投诉',
                'option_replies': {
                    '1': '感谢您的咨询！',
                    '2': '我们会认真处理您的投诉。'
                },
                'tree_options': {
                    '1': {'text': '咨询', 'type': 'final', 'reply': '感谢您的咨询！'},
                    '2': {'text': '投诉', 'type': 'final', 'reply': '我们会认真处理您的投诉。'}
                }
            }
        }
        
        # 设置较短的间隔时间用于测试（3秒）
        processor.set_interval_seconds(3)
        
        # 模拟微信实例
        mock_wechat = WeWork()
        
        user_id = "test_db_user"
        room_id = "S:test_db_room"
        
        print("--- 步骤1: 系统消息创建会话 ---")
        
        # 模拟系统消息
        sys_message = {
            "data": {
                "content": "你已添加了测试用户，现在可以开始聊天了。",
                "conversation_id": room_id,
                "sender": user_id
            }
        }
        
        processor.on_sys_message(mock_wechat, sys_message)
        
        # 检查内存和数据库中的会话
        memory_session = processor.user_sessions.get(user_id)
        db_session = processor.session_db.get_active_session(user_id) if processor.session_db else None
        
        if memory_session:
            print("✅ 内存中成功创建会话")
        else:
            print("❌ 内存中未创建会话")
        
        if db_session:
            print("✅ 数据库中成功创建会话")
        else:
            print("❌ 数据库中未创建会话")
        
        print("\n--- 步骤2: 用户完成选择 ---")
        
        # 模拟用户选择
        text_message = {
            "data": {
                "room_wxid": room_id,
                "from_wxid": user_id,
                "from_name": "测试用户",
                "msg": "1",
                "content_type": 0
            }
        }
        
        processor.on_recv_message(mock_wechat, text_message)
        
        # 检查会话是否失效
        memory_session_after = processor.user_sessions.get(user_id)
        db_session_after = processor.session_db.get_active_session(user_id) if processor.session_db else None
        expired_session = processor.session_db.get_expired_session(user_id) if processor.session_db else None
        
        if not memory_session_after:
            print("✅ 内存会话成功失效")
        else:
            print("❌ 内存会话未失效")
        
        if not db_session_after and expired_session:
            print("✅ 数据库会话成功失效并记录")
        else:
            print("❌ 数据库会话失效处理异常")
        
        print("\n--- 步骤3: 测试间隔时间检查 ---")
        
        # 立即发送消息（应该被忽略）
        processor.on_recv_message(mock_wechat, text_message)
        
        new_memory_session = processor.user_sessions.get(user_id)
        if not new_memory_session:
            print("✅ 间隔时间内消息被正确忽略")
        else:
            print("❌ 间隔时间内错误地创建了新会话")
        
        # 等待间隔时间后发送消息
        print("等待4秒（超过3秒间隔时间）...")
        time.sleep(4)
        
        processor.on_recv_message(mock_wechat, text_message)
        
        final_memory_session = processor.user_sessions.get(user_id)
        final_db_session = processor.session_db.get_active_session(user_id) if processor.session_db else None
        
        if final_memory_session and final_db_session:
            print("✅ 超过间隔时间后成功重新创建会话")
        else:
            print("❌ 超过间隔时间后未能重新创建会话")
        
        # 获取统计信息
        stats = processor.get_session_statistics()
        print(f"\n会话统计信息: {stats}")
        
        print("✅ 带数据库的消息处理测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 带数据库的消息处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fallback_mechanism():
    """测试回退机制（模拟数据库不可用）"""
    print("\n=== 测试回退机制 ===")
    
    try:
        from src.core.message_processor import MessageProcessor
        from src.core.wechat_handler import WeChatHandler
        from src.utils.mock_ntwork import WeWork
        
        # 创建消息处理器实例
        wechat_handler = WeChatHandler()
        processor = MessageProcessor(wechat_handler, None)
        
        # 模拟数据库不可用
        processor.session_db = None
        
        # 设置私聊配置
        processor.group_option_configs = {
            'private_chat': {
                'auto_reply': '请选择服务类型：\n1.咨询',
                'option_replies': {
                    '1': '感谢您的咨询！'
                },
                'tree_options': {
                    '1': {'text': '咨询', 'type': 'final', 'reply': '感谢您的咨询！'}
                }
            }
        }
        
        # 模拟微信实例
        mock_wechat = WeWork()
        
        user_id = "test_fallback_user"
        room_id = "S:test_fallback_room"
        
        print("--- 测试系统消息处理（无数据库）---")
        
        # 模拟系统消息
        sys_message = {
            "data": {
                "content": "你已添加了测试用户，现在可以开始聊天了。",
                "conversation_id": room_id,
                "sender": user_id
            }
        }
        
        processor.on_sys_message(mock_wechat, sys_message)
        
        # 检查内存会话
        memory_session = processor.user_sessions.get(user_id)
        if memory_session:
            print("✅ 无数据库时内存会话创建成功")
        else:
            print("❌ 无数据库时内存会话创建失败")
        
        print("--- 测试文本消息处理（无数据库）---")
        
        # 模拟用户选择
        text_message = {
            "data": {
                "room_wxid": room_id,
                "from_wxid": user_id,
                "from_name": "测试用户",
                "msg": "1",
                "content_type": 0
            }
        }
        
        processor.on_recv_message(mock_wechat, text_message)
        
        # 检查会话处理
        memory_session_after = processor.user_sessions.get(user_id)
        completion_record = processor.user_last_completion.get(user_id)
        
        if not memory_session_after and completion_record:
            print("✅ 无数据库时会话完成处理正常")
        else:
            print("❌ 无数据库时会话完成处理异常")
        
        # 测试统计信息
        stats = processor.get_session_statistics()
        if stats['active_sessions'] == 0:
            print("✅ 无数据库时统计信息正常")
        else:
            print("❌ 无数据库时统计信息异常")
        
        print("✅ 回退机制测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 回退机制测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("微信私聊机器人 - 数据库修复验证测试")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    if test_database_initialization():
        success_count += 1
    
    if test_message_processing_with_database():
        success_count += 1
    
    if test_fallback_mechanism():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"测试完成！成功: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("\n✅ 所有测试通过！数据库修复成功")
        print("\n修复总结：")
        print("1. ✅ 修复了数据库管理器初始化顺序问题")
        print("2. ✅ 添加了所有数据库调用的安全检查")
        print("3. ✅ 实现了完整的回退机制")
        print("4. ✅ 保证了系统在数据库不可用时仍能正常工作")
    else:
        print("\n❌ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
