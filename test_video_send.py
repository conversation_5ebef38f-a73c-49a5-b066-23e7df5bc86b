# -*- coding: utf-8 -*-
"""
测试视频发送特殊格式功能
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.core.wechat_handler import WeChatHandler
from src.core.message_processor import MessageProcessor


def test_video_send_format():
    """测试视频发送特殊格式"""
    print("测试视频发送特殊格式功能...")
    
    try:
        # 创建微信处理器和消息处理器
        wechat_handler = WeChatHandler()
        message_processor = MessageProcessor(wechat_handler)
        
        # 测试视频发送格式解析
        test_reply_content = """wait_reply=正在为您发送视频...
video_path=C:/videos/example.mp4"""
        
        print(f"测试回复内容:\n{test_reply_content}")
        
        # 解析特殊格式参数
        params = message_processor._parse_special_reply_params(test_reply_content)
        print(f"解析到的参数: {params}")
        
        # 验证参数
        expected_params = {
            'wait_reply': '正在为您发送视频...',
            'video_path': 'C:/videos/example.mp4'
        }
        
        if params == expected_params:
            print("✅ 视频发送参数解析正确")
        else:
            print(f"❌ 视频发送参数解析错误，期望: {expected_params}, 实际: {params}")
            return False
        
        # 测试不同视频格式
        test_cases = [
            {
                'content': """wait_reply=发送教学视频中...
video_path=/path/to/tutorial.avi""",
                'expected': {
                    'wait_reply': '发送教学视频中...',
                    'video_path': '/path/to/tutorial.avi'
                }
            },
            {
                'content': """wait_reply=发送宣传视频中...
video_path=D:\\videos\\promotion.mov""",
                'expected': {
                    'wait_reply': '发送宣传视频中...',
                    'video_path': 'D:\\videos\\promotion.mov'
                }
            },
            {
                'content': """video_path=./local/demo.mkv""",
                'expected': {
                    'video_path': './local/demo.mkv'
                }
            },
            {
                'content': """wait_reply=正在上传高清视频...
video_path=/uploads/hd_video.webm""",
                'expected': {
                    'wait_reply': '正在上传高清视频...',
                    'video_path': '/uploads/hd_video.webm'
                }
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n测试用例 {i}:")
            print(f"内容: {test_case['content']}")
            
            params = message_processor._parse_special_reply_params(test_case['content'])
            print(f"解析结果: {params}")
            
            if params == test_case['expected']:
                print(f"✅ 测试用例 {i} 通过")
            else:
                print(f"❌ 测试用例 {i} 失败")
                return False
        
        # 测试混合格式（包含普通文本和视频发送）
        test_mixed_content = """这是一个精彩的视频分享：
wait_reply=视频加载中，请稍候...
video_path=/shared/videos/awesome_video.mp4
希望您喜欢这个内容！"""
        
        print(f"\n测试混合内容:\n{test_mixed_content}")
        
        mixed_params = message_processor._parse_special_reply_params(test_mixed_content)
        normal_text = message_processor._extract_normal_text_from_special_reply(test_mixed_content)
        
        print(f"解析到的混合参数: {mixed_params}")
        print(f"提取的普通文本: {normal_text}")
        
        expected_mixed_params = {
            'wait_reply': '视频加载中，请稍候...',
            'video_path': '/shared/videos/awesome_video.mp4'
        }
        expected_normal_text = "这是一个精彩的视频分享：\n希望您喜欢这个内容！"
        
        if mixed_params == expected_mixed_params and normal_text == expected_normal_text:
            print("✅ 混合内容解析正确")
        else:
            print(f"❌ 混合内容解析错误")
            print(f"期望参数: {expected_mixed_params}, 实际: {mixed_params}")
            print(f"期望文本: {expected_normal_text}, 实际: {normal_text}")
            return False
        
        print("\n🎉 所有视频发送格式测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_wechat_handler_send_video():
    """测试微信处理器的send_video方法"""
    print("\n测试微信处理器的send_video方法...")
    
    try:
        wechat_handler = WeChatHandler()
        
        # 测试发送不同格式的视频
        test_videos = [
            "C:/videos/test.mp4",
            "/path/to/video.avi",
            "./local/video.mov",
            "D:\\videos\\demo.mkv",
            "/uploads/presentation.webm"
        ]
        
        for video_path in test_videos:
            print(f"测试发送视频: {video_path}")
            result = wechat_handler.send_video("test_conversation", video_path)
            
            if result:
                print(f"✅ 发送 {video_path} 成功")
            else:
                print(f"❌ 发送 {video_path} 失败")
                return False
        
        print("✅ send_video方法测试通过")
        return True
            
    except Exception as e:
        print(f"❌ send_video方法测试失败: {e}")
        return False


def test_video_format_compatibility():
    """测试视频格式与其他特殊格式的兼容性"""
    print("\n测试视频格式与其他特殊格式的兼容性...")
    
    try:
        wechat_handler = WeChatHandler()
        message_processor = MessageProcessor(wechat_handler)
        
        # 测试视频格式不会干扰其他格式
        other_formats = [
            {
                'name': '链接卡片格式',
                'content': """wait_reply=查询中...
title=测试链接
desc=这是测试描述
url=https://example.com
img=https://example.com/image.jpg""",
                'expected_key': 'title'
            },
            {
                'name': '文件发送格式',
                'content': """wait_reply=文件准备中...
file_path=/path/to/document.pdf""",
                'expected_key': 'file_path'
            },
            {
                'name': '用户卡片格式',
                'content': """wait_reply=推荐客服中...
user_id=service_001""",
                'expected_key': 'user_id'
            }
        ]
        
        for format_test in other_formats:
            print(f"测试 {format_test['name']} 兼容性...")
            params = message_processor._parse_special_reply_params(format_test['content'])
            
            if format_test['expected_key'] in params:
                print(f"✅ {format_test['name']} 兼容性正常")
            else:
                print(f"❌ {format_test['name']} 兼容性异常")
                return False
        
        print("✅ 所有格式兼容性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 兼容性测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("=" * 70)
    print("视频发送特殊格式功能测试")
    print("=" * 70)
    
    # 测试格式解析
    format_test_ok = test_video_send_format()
    
    # 测试发送方法
    send_test_ok = test_wechat_handler_send_video()
    
    # 测试兼容性
    compatibility_test_ok = test_video_format_compatibility()
    
    # 总结
    print("\n" + "=" * 70)
    print("测试结果总结:")
    print(f"格式解析测试: {'✅ 通过' if format_test_ok else '❌ 失败'}")
    print(f"发送方法测试: {'✅ 通过' if send_test_ok else '❌ 失败'}")
    print(f"兼容性测试: {'✅ 通过' if compatibility_test_ok else '❌ 失败'}")
    
    if all([format_test_ok, send_test_ok, compatibility_test_ok]):
        print("\n🎉 所有测试通过！视频发送功能已成功添加！")
        print("\n支持的特殊格式:")
        print("1. 📎 链接卡片: wait_reply= + title= + desc= + url= + img=")
        print("2. 👤 用户卡片: wait_reply= + user_id=")
        print("3. 📁 文件发送: wait_reply= + file_path=")
        print("4. 🎬 视频发送: wait_reply= + video_path=")
        print("5. 📱 小程序: wait_reply= + aes_key= + file_id= + size= + appicon= + appid= + appname= + conversation_id= + page_path= + title= + username=")
        print("\n视频格式使用方法:")
        print("wait_reply=等待消息（可选）")
        print("video_path=视频文件路径")
        print("\n支持的视频格式: .mp4, .avi, .mov, .mkv, .webm 等")
        return True
    else:
        print("\n❌ 部分测试失败，请检查问题。")
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        input("\n按回车键退出...")
    sys.exit(0 if success else 1)
