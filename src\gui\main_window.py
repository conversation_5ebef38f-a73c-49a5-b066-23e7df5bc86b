# -*- coding: utf-8 -*-
"""
主窗口模块 - 微信选项选择机器人的主界面
"""

import sys
import time
import logging
import threading
import tkinter as tk
from tkinter import messagebox
from typing import Optional

# 尝试导入真实的ttkbootstrap，如果失败则使用模拟版本
try:
    import ttkbootstrap as ttk
    from ttkbootstrap import Style
except ImportError:
    from ..utils import mock_ttkbootstrap as ttk
    from ..utils.mock_ttkbootstrap import Style

from ..utils.decorators import singleton_thread_safe
from ..utils.logging_handler import G<PERSON><PERSON><PERSON><PERSON>, NullWriter
from ..core.wechat_handler import WeChatHandler
from ..core.message_processor import MessageProcessor
from ..config.config_manager import ConfigManager

from .option_configuration import OptionConfigurationTab
from .log_display import LogDisplayTab

# 重定向标准错误输出来隐藏libpng警告
old_stderr = sys.stderr
sys.stderr = NullWriter()

# 全局实例跟踪
_instance = None


@singleton_thread_safe
class WeChatGUI:
    """
    微信选项选择机器人主窗口
    """
    
    def __init__(self):
        """初始化主窗口"""
        global _instance
        if _instance is not None:
            raise RuntimeError("WeChatGUI 已经在运行")
        _instance = self
        
        # 使用 ttkbootstrap 的样式
        self.style = Style(theme='cyborg')  # 使用深色科技风主题
        self.root = self.style.master
        self.root.title("微信私聊选项选择机器人")
        self.root.geometry("1350x900")
        
        # 设置窗口在屏幕中央
        self.center_window()
        
        # 初始化组件
        self.wechat_handler: Optional[WeChatHandler] = None
        self.message_processor: Optional[MessageProcessor] = None
        self.config_manager = ConfigManager()
        
        # 初始化数据
        self.paused_rooms = []
        self.group_option_configs = {}
        self.current_selected_groups = []
        self.user_sessions = {}

        # 定时器相关
        self.session_cleanup_timer = None
        
        # 创建GUI组件
        self.create_widgets()
        self.setup_logging()
        
        # 加载配置并初始化微信
        self.load_config()
        self.init_wechat()

    def __del__(self):
        """析构函数"""
        global _instance
        _instance = None

    def center_window(self):
        """将窗口居中显示"""
        # 获取屏幕的宽度和高度
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        # 修改窗口尺寸
        window_width = 1350
        window_height = 900
        
        # 计算窗口居中的坐标
        center_x = int((screen_width - window_width) / 2)
        center_y = int((screen_height - window_height) / 2)
        
        # 设置窗口位置
        self.root.geometry(f"{window_width}x{window_height}+{center_x}+{center_y}")

    def setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=logging.INFO, 
            format="%(asctime)s - %(levelname)s - %(message)s"
        )
        self.logger = logging.getLogger()

        # 添加GUI处理器
        gui_handler = GuiHandler(self.log_tab.log_text)
        gui_handler.setFormatter(
            logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
        )
        self.logger.addHandler(gui_handler)

    def create_widgets(self):
        """创建主界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建选项卡控件
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建各个选项卡
        self.option_tab = OptionConfigurationTab(self.notebook, self)
        self.log_tab = LogDisplayTab(self.notebook, self)

        # 添加选项卡到notebook
        self.notebook.add(self.option_tab.frame, text="选项配置")
        self.notebook.add(self.log_tab.frame, text="运行日志")
        
        # 控制按钮
        control_frame = ttk.Frame(main_frame, padding="5")
        control_frame.pack(fill=tk.X, pady=5)

        self.start_btn = ttk.Button(
            control_frame, 
            text="启动监控", 
            command=self.start_monitoring, 
            bootstyle="success"
        )
        self.start_btn.pack(side=tk.LEFT, padx=5)

        self.stop_btn = ttk.Button(
            control_frame, 
            text="停止监控", 
            command=self.stop_monitoring, 
            state="disabled", 
            bootstyle="danger"
        )
        self.stop_btn.pack(side=tk.LEFT, padx=5)

        # 添加保存配置按钮
        save_btn = ttk.Button(
            control_frame,
            text="保存配置",
            command=self.save_config,
            bootstyle="info-outline"
        )
        save_btn.pack(side=tk.LEFT, padx=5)

    def init_wechat(self):
        """初始化微信连接"""
        def run():
            try:
                # 清理之前的实例
                if self.wechat_handler:
                    self.wechat_handler.exit_program()
                    self.wechat_handler = None
                    time.sleep(1)  # 等待资源释放
                
                # 初始化新实例
                self.wechat_handler = WeChatHandler()
                self.message_processor = MessageProcessor(self.wechat_handler, self)
                
                # 初始化微信连接
                if self.wechat_handler.initialize():
                    self.logger.info("微信初始化成功")
                    # 私聊机器人无需获取群列表
                else:
                    self.logger.error("微信初始化失败")
                    self._reset_button_state()
                
            except Exception as e:
                self.logger.error(f"初始化微信失败: {str(e)}")
                self.logger.error(f"错误详情: ", exc_info=True)
                if self.wechat_handler:
                    self.wechat_handler.exit_program()
                    self.wechat_handler = None
                # 重置按钮状态
                self.root.after(0, self._reset_button_state)

        # 在新线程中初始化微信
        threading.Thread(target=run, daemon=True).start()

    def start_monitoring(self):
        """启动监控"""
        global _instance
        if _instance is not self:
            self.logger.error("检测到多个程序实例，请关闭其他窗口")
            return
            
        # 检查是否已登录
        if not self.wechat_handler or not self.wechat_handler.is_connected:
            self.logger.error("微信未初始化，请重启程序")
            return
            
        try:
            login_info = self.wechat_handler.get_login_info()
            if not login_info:
                self.logger.error("微信未登录，请先登录")
                return
        except:
            self.logger.error("获取登录状态失败")
            return
            
        self.save_config()  # 启动前保存当前配置

        # 设置选项配置参数
        try:
            # 更新消息处理器配置
            self.message_processor.update_config(self.group_option_configs, self.paused_rooms)

            # 注册消息回调
            self.wechat_handler.register_message_callback(
                self.message_processor.on_recv_message,
                self.message_processor.on_sys_message
            )
            self.logger.info("文本消息和系统消息回调注册成功")

            # 更新按钮状态
            self.start_btn.configure(state="disabled")
            self.stop_btn.configure(state="normal")

            # 启动定期清理过期会话的定时器
            self._start_session_cleanup_timer()

            self.logger.info("监控已启动")

        except Exception as e:
            self.logger.error(f"启动监控失败: {e}")
            self.logger.error("错误详情: ", exc_info=True)

    def stop_monitoring(self):
        """停止监控"""
        if self.wechat_handler:
            # 取消消息回调
            self.wechat_handler.unregister_message_callback()
            self.logger.info("消息回调已取消")

            self.start_btn.configure(state="normal")
            self.stop_btn.configure(state="disabled")

            # 停止定时器
            self._stop_session_cleanup_timer()

            self.logger.info("监控已停止")

    def load_config(self):
        """加载配置"""
        try:
            config = self.config_manager.load_config()
            
            # 加载按群分组的选项配置
            self.group_option_configs = self.config_manager.get_group_option_configs()
            
            # 私聊机器人无需加载监控群列表
            
            # 加载暂停群列表
            self.paused_rooms = self.config_manager.get_paused_rooms()
            
            self.logger.info("配置加载成功")
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            # 初始化暂停群列表，确保默认存在
            self.paused_rooms = []

    def save_config(self):
        """保存配置"""
        try:
            # 更新配置管理器中的数据
            self.config_manager.set_group_option_configs(self.group_option_configs)
            self.config_manager.set_paused_rooms(self.paused_rooms)
            
            # 私聊机器人无需保存监控群列表
            self.config_manager.set_monitored_rooms([])
            
            # 保存配置
            self.config_manager.save_config()
            self.logger.info("配置保存成功")
            
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")

    def _reset_button_state(self):
        """重置按钮状态"""
        self.start_btn.configure(state="normal")
        self.stop_btn.configure(state="disabled")

    def _start_session_cleanup_timer(self):
        """启动会话清理定时器"""
        try:
            # 每2分钟清理一次过期会话
            self._cleanup_sessions()
            self.session_cleanup_timer = self.root.after(120000, self._start_session_cleanup_timer)  # 120秒 = 2分钟
        except Exception as e:
            self.logger.error(f"启动会话清理定时器失败: {e}")

    def _stop_session_cleanup_timer(self):
        """停止会话清理定时器"""
        try:
            if self.session_cleanup_timer:
                self.root.after_cancel(self.session_cleanup_timer)
                self.session_cleanup_timer = None
                self.logger.info("会话清理定时器已停止")
        except Exception as e:
            self.logger.error(f"停止会话清理定时器失败: {e}")

    def _cleanup_sessions(self):
        """清理过期会话"""
        try:
            if self.message_processor:
                self.message_processor.cleanup_expired_sessions()
        except Exception as e:
            self.logger.error(f"清理会话失败: {e}")

    def run(self):
        """运行主程序"""
        try:
            # 恢复标准错误输出
            sys.stderr = old_stderr
            self.root.mainloop()
        finally:
            # 清理资源
            self._stop_session_cleanup_timer()
            global _instance
            _instance = None
            if self.wechat_handler:
                self.wechat_handler.exit_program()
