# -*- coding: utf-8 -*-
"""
测试所有特殊格式功能的综合测试
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.core.wechat_handler import WeChatHandler
from src.core.message_processor import MessageProcessor


def test_all_special_formats():
    """测试所有特殊格式功能"""
    print("=" * 70)
    print("微信选项选择机器人 - 特殊格式功能综合测试")
    print("=" * 70)
    
    try:
        # 创建微信处理器和消息处理器
        wechat_handler = WeChatHandler()
        message_processor = MessageProcessor(wechat_handler)
        
        # 测试用例
        test_cases = [
            {
                'name': '链接卡片格式',
                'content': """wait_reply=正在为您查询相关信息...
title=查询结果
desc=这是您要查询的详细信息
url=https://example.com/result
img=https://example.com/images/result.jpg""",
                'expected_params': {
                    'wait_reply': '正在为您查询相关信息...',
                    'title': '查询结果',
                    'desc': '这是您要查询的详细信息',
                    'url': 'https://example.com/result',
                    'img': 'https://example.com/images/result.jpg'
                },
                'format_type': 'link_card'
            },
            {
                'name': '用户卡片格式',
                'content': """wait_reply=正在为您推荐专业客服...
user_id=customer_service_001""",
                'expected_params': {
                    'wait_reply': '正在为您推荐专业客服...',
                    'user_id': 'customer_service_001'
                },
                'format_type': 'user_card'
            },
            {
                'name': '文件发送格式',
                'content': """wait_reply=正在为您准备文件...
file_path=C:/documents/manual.pdf""",
                'expected_params': {
                    'wait_reply': '正在为您准备文件...',
                    'file_path': 'C:/documents/manual.pdf'
                },
                'format_type': 'file_send'
            },
            {
                'name': '视频发送格式',
                'content': """wait_reply=正在为您发送视频...
video_path=C:/videos/example.mp4""",
                'expected_params': {
                    'wait_reply': '正在为您发送视频...',
                    'video_path': 'C:/videos/example.mp4'
                },
                'format_type': 'video_send'
            },
            {
                'name': '混合格式（普通文本+文件）',
                'content': """请查看以下文档：
wait_reply=文档准备中...
file_path=/uploads/guide.pdf
如有疑问请联系客服""",
                'expected_params': {
                    'wait_reply': '文档准备中...',
                    'file_path': '/uploads/guide.pdf'
                },
                'expected_text': '请查看以下文档：\n如有疑问请联系客服',
                'format_type': 'mixed'
            },
            {
                'name': '仅等待回复',
                'content': """wait_reply=正在处理您的请求，请稍候...""",
                'expected_params': {
                    'wait_reply': '正在处理您的请求，请稍候...'
                },
                'format_type': 'wait_only'
            },
            {
                'name': '小程序格式',
                'content': """wait_reply=正在为您打开小程序...
aes_key=miniapp_key_123
file_id=miniapp_file_456
size=2048
appicon=https://miniapp.com/icon.png
appid=wx_miniapp_123456
appname=测试小程序
conversation_id=miniapp_conv_789
page_path=pages/miniapp/index
title=小程序标题
username=miniappuser""",
                'expected_params': {
                    'wait_reply': '正在为您打开小程序...',
                    'aes_key': 'miniapp_key_123',
                    'file_id': 'miniapp_file_456',
                    'size': '2048',
                    'appicon': 'https://miniapp.com/icon.png',
                    'appid': 'wx_miniapp_123456',
                    'appname': '测试小程序',
                    'conversation_id': 'miniapp_conv_789',
                    'page_path': 'pages/miniapp/index',
                    'title': '小程序标题',
                    'username': 'miniappuser'
                },
                'format_type': 'miniapp'
            },
            {
                'name': '普通文本（无特殊格式）',
                'content': """这是普通的回复内容，没有特殊格式。""",
                'expected_params': {},
                'format_type': 'normal'
            }
        ]
        
        passed_tests = 0
        total_tests = len(test_cases)
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n测试 {i}/{total_tests}: {test_case['name']}")
            print("-" * 50)
            print(f"内容:\n{test_case['content']}")
            
            # 解析参数
            params = message_processor._parse_special_reply_params(test_case['content'])
            print(f"解析到的参数: {params}")
            
            # 验证参数
            if params == test_case['expected_params']:
                print("✅ 参数解析正确")
                
                # 如果是混合格式，还要测试文本提取
                if test_case['format_type'] == 'mixed':
                    normal_text = message_processor._extract_normal_text_from_special_reply(test_case['content'])
                    print(f"提取的普通文本: {normal_text}")
                    if normal_text == test_case['expected_text']:
                        print("✅ 文本提取正确")
                        passed_tests += 1
                    else:
                        print(f"❌ 文本提取错误，期望: {test_case['expected_text']}")
                else:
                    passed_tests += 1
            else:
                print(f"❌ 参数解析错误")
                print(f"期望: {test_case['expected_params']}")
                print(f"实际: {params}")
        
        # 测试微信处理器方法
        print(f"\n测试 {total_tests + 1}: 微信处理器方法测试")
        print("-" * 50)
        
        method_tests = [
            ('send_text', lambda: wechat_handler.send_text('test_conv', 'test message')),
            ('send_link_card', lambda: wechat_handler.send_link_card(
                'test_conv', 'title', 'desc', 'url', 'img')),
            ('send_card', lambda: wechat_handler.send_card('test_conv', 'user123')),
            ('send_image', lambda: wechat_handler.send_image('test_conv', '/path/to/file.jpg')),
            ('send_video', lambda: wechat_handler.send_video('test_conv', '/path/to/video.mp4')),
            ('send_miniapp', lambda: wechat_handler.send_miniapp(
                'test_key', 'test_file', '1024', 'test_icon', 'test_appid',
                'test_appname', 'test_conv', 'test_path', 'test_title', 'test_user'))
        ]
        
        method_passed = 0
        for method_name, method_call in method_tests:
            try:
                result = method_call()
                if result:
                    print(f"✅ {method_name} 方法测试通过")
                    method_passed += 1
                else:
                    print(f"❌ {method_name} 方法返回False")
            except Exception as e:
                print(f"❌ {method_name} 方法测试失败: {e}")
        
        if method_passed == len(method_tests):
            passed_tests += 1
            total_tests += 1
        
        # 总结
        print("\n" + "=" * 70)
        print("测试结果总结")
        print("=" * 70)
        print(f"通过测试: {passed_tests}/{total_tests}")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        if passed_tests == total_tests:
            print("\n🎉 所有测试通过！特殊格式功能完全正常！")
            print("\n支持的特殊格式总览:")
            print("1. 📎 链接卡片: wait_reply= + title= + desc= + url= + img=")
            print("2. 👤 用户卡片: wait_reply= + user_id=")
            print("3. 📁 文件发送: wait_reply= + file_path=")
            print("4. 🎬 视频发送: wait_reply= + video_path=")
            print("5. 📱 小程序: wait_reply= + aes_key= + file_id= + size= + appicon= + appid= + appname= + conversation_id= + page_path= + title= + username=")
            print("6. ⏳ 等待回复: wait_reply=")
            print("7. 📝 普通文本: 直接输入文本内容")
            print("8. 🔀 混合格式: 普通文本 + 特殊参数")
            
            print("\n使用说明:")
            print("• wait_reply= 参数是可选的，用于在发送主要内容前先发送等待消息")
            print("• 特殊参数必须单独占一行，格式为 参数名=参数值")
            print("• 可以在特殊参数前后添加普通文本说明")
            print("• 系统会自动识别格式类型并调用相应的发送方法")
            
            return True
        else:
            print(f"\n❌ {total_tests - passed_tests} 个测试失败，请检查问题。")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    success = test_all_special_formats()
    
    if not success:
        input("\n按回车键退出...")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
