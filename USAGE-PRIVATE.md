# 微信私聊选项选择机器人使用指南

## 快速开始

### 1. 启动程序
```bash
python app.py
```

### 2. 界面说明
程序启动后会显示三个选项卡：
- **联系人管理**: 保留用于兼容性，私聊机器人无需配置
- **选项配置**: 配置自动回复内容和选项菜单
- **运行日志**: 查看程序运行状态和消息处理日志

### 3. 配置选项
在"选项配置"选项卡中：
1. 设置自动回复内容（欢迎消息）
2. 配置选项菜单（数字选项对应的回复）
3. 支持特殊格式回复（见下方说明）

### 4. 开始监控
1. 点击"开始监控"按钮
2. 程序会自动处理所有私聊消息
3. 在"运行日志"中查看处理状态

## 配置详解

### 基础配置
```json
{
    "group_option_configs": {
        "private_chat": {
            "auto_reply": "您好！请选择服务类型：\n1.咨询\n2.投诉\n3.建议",
            "option_replies": {
                "1": "请详细描述您的咨询内容",
                "2": "请说明您要投诉的问题",
                "3": "感谢您的建议，请详细说明"
            }
        }
    }
}
```

### 特殊格式回复

#### 1. 链接卡片
```
title=卡片标题
desc=卡片描述
url=https://example.com
img=https://example.com/image.jpg
```

#### 2. 发送图片/文件
```
file_path=C:\path\to\image.jpg
```

#### 3. 发送视频
```
video_path=C:\path\to\video.mp4
```

#### 4. 发送用户卡片
```
user_id=用户的微信ID
```

#### 5. 等待回复 + 其他格式
```
wait_reply=正在处理，请稍候...
file_path=C:\path\to\result.jpg
```

#### 6. 小程序（完整参数）
```
aes_key=加密密钥
file_id=文件ID
size=文件大小
appicon=应用图标URL
appid=小程序ID
appname=小程序名称
page_path=页面路径
title=小程序标题
username=用户名
```

## 使用场景示例

### 客服机器人
```json
{
    "auto_reply": "您好！欢迎咨询客服，请选择服务类型：\n1.产品咨询\n2.技术支持\n3.售后服务\n4.投诉建议\n5.人工客服",
    "option_replies": {
        "1": "请详细描述您想了解的产品信息，我们会为您详细介绍。",
        "2": "请描述您遇到的技术问题，我们会尽快为您解决。",
        "3": "请提供您的订单号，我们会为您处理售后问题。",
        "4": "您的意见很重要，请详细说明问题。",
        "5": "wait_reply=正在为您转接人工客服...\nuser_id=customer_service_001"
    }
}
```

### 信息查询机器人
```json
{
    "auto_reply": "请选择查询类型：\n1.天气查询\n2.快递查询\n3.股票查询\n4.新闻资讯",
    "option_replies": {
        "1": "请发送城市名称，如：北京",
        "2": "请发送快递单号",
        "3": "请发送股票代码",
        "4": "title=今日头条\ndesc=最新新闻资讯\nurl=https://news.example.com\nimg=https://news.example.com/logo.jpg"
    }
}
```

## 注意事项

### 1. 消息类型限制
- 只处理私聊消息（消息ID以 `S:` 开头）
- 忽略所有群聊消息（消息ID以 `R:` 开头）
- 只处理文本消息类型

### 2. 会话管理
- 每个用户有独立的会话状态
- 会话有超时机制，超时后重新发送欢迎消息
- 用户可以随时重新开始对话

### 3. 特殊格式注意
- 文件路径必须是绝对路径
- 图片支持常见格式（jpg, png, gif等）
- 视频文件不宜过大
- 链接卡片需要有效的URL

### 4. 错误处理
- 特殊格式发送失败时会自动降级为文本消息
- 所有错误都会记录在日志中
- 程序具有自动恢复能力

## 故障排除

### 常见问题
1. **程序无法启动**: 检查Python环境和依赖包
2. **无法连接微信**: 确保企业微信已登录且有权限
3. **消息无响应**: 检查配置文件格式和内容
4. **特殊格式失效**: 检查文件路径和参数格式

### 日志查看
在"运行日志"选项卡中可以看到：
- 消息接收情况
- 处理结果
- 错误信息
- 发送状态

### 配置备份
建议定期备份配置文件：
- `config-private.json`: 主配置文件
- `config.json`: 兼容性配置文件

## 技术支持

如遇到问题，请查看：
1. 运行日志中的错误信息
2. 配置文件格式是否正确
3. 企业微信连接状态
4. 文件路径和权限设置
