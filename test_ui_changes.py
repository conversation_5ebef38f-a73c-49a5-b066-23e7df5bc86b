# -*- coding: utf-8 -*-
"""
测试界面修改 - 验证私聊机器人界面
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_ui_structure():
    """测试界面结构"""
    print("=== 测试界面结构 ===")
    
    try:
        from src.gui.main_window import WeChatGUI
        from src.gui.option_configuration import OptionConfigurationTab
        
        print("✅ 主窗口类导入成功")
        print("✅ 选项配置类导入成功")
        
        # 检查主窗口是否移除了room_tab相关代码
        import inspect
        main_window_source = inspect.getsource(WeChatGUI.__init__)
        
        if 'room_tab' not in main_window_source:
            print("✅ 主窗口已移除room_tab相关代码")
        else:
            print("❌ 主窗口仍包含room_tab相关代码")
            
        # 检查选项配置是否有自动保存功能
        option_config_source = inspect.getsource(OptionConfigurationTab)
        
        if 'on_text_changed' in option_config_source and 'auto_save_config' in option_config_source:
            print("✅ 选项配置已添加自动保存功能")
        else:
            print("❌ 选项配置缺少自动保存功能")
            
        if 'load_private_config' in option_config_source:
            print("✅ 选项配置已添加私聊配置加载功能")
        else:
            print("❌ 选项配置缺少私聊配置加载功能")
            
    except Exception as e:
        print(f"❌ 界面结构测试失败: {e}")

def test_config_structure():
    """测试配置结构"""
    print("\n=== 测试配置结构 ===")
    
    try:
        from src.config.config_manager import ConfigManager
        
        # 测试默认配置
        config_manager = ConfigManager()
        default_config = config_manager._get_default_config()
        
        if 'private_chat' in default_config.get('group_option_configs', {}):
            print("✅ 默认配置包含私聊配置")
            private_config = default_config['group_option_configs']['private_chat']
            
            if 'auto_reply' in private_config and 'option_replies' in private_config:
                print("✅ 私聊配置结构正确")
            else:
                print("❌ 私聊配置结构不完整")
        else:
            print("❌ 默认配置缺少私聊配置")
            
    except Exception as e:
        print(f"❌ 配置结构测试失败: {e}")

def test_message_processor():
    """测试消息处理器"""
    print("\n=== 测试消息处理器 ===")
    
    try:
        from src.core.message_processor import MessageProcessor
        
        # 检查消息过滤逻辑
        import inspect
        processor_source = inspect.getsource(MessageProcessor.on_recv_message)
        
        if 'is_private' in processor_source and 'is_group' in processor_source:
            print("✅ 消息处理器包含私聊/群聊判断逻辑")
        else:
            print("❌ 消息处理器缺少消息类型判断")
            
        if 'private_chat' in processor_source or 'private_config' in processor_source:
            print("✅ 消息处理器已适配私聊配置")
        else:
            print("❌ 消息处理器未适配私聊配置")
            
    except Exception as e:
        print(f"❌ 消息处理器测试失败: {e}")

def main():
    """主测试函数"""
    print("微信私聊选项选择机器人 - 界面修改测试")
    print("=" * 50)
    
    test_ui_structure()
    test_config_structure()
    test_message_processor()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("\n主要修改总结：")
    print("1. ✅ 移除了联系人管理选项卡")
    print("2. ✅ 简化了选项配置界面，去掉群选择")
    print("3. ✅ 移除了保存配置按钮，改为自动保存")
    print("4. ✅ 添加了私聊配置支持")
    print("5. ✅ 更新了消息处理逻辑")

if __name__ == "__main__":
    main()
