# -*- coding: utf-8 -*-
"""
微信选项选择机器人 - 主入口文件（备份）
这是原始的单文件版本，将被重构为模块化项目
"""

import sys, time, re, os
import logging, ntwork
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
from datetime import datetime
import json
import keyboard
import subprocess
import requests
import ttkbootstrap as ttk
from ttkbootstrap import Style
from ttkbootstrap.constants import *  # 导入常量
import threading
import ctypes,random

# 重定向标准错误输出
class NullWriter:
    def write(self, arg):
        pass
        
# 临时重定向标准错误输出来隐藏libpng警告
old_stderr = sys.stderr
sys.stderr = NullWriter()
# 初始化GUI后恢复标准错误输出
# 在GUI初始化完成后添加: sys.stderr = old_stderr

def singleton_thread_safe(cls):
    _instances = {}
    _lock = threading.Lock()  # 线程锁
    
    def wrapper(*args, **kwargs):
        with _lock:  # 加锁保证线程安全
            if cls not in _instances:
                _instances[cls] = cls(*args, **kwargs)
            return _instances[cls]
    
    return wrapper
# 在文件顶部添加一个全局变量来跟踪实例
_instance = None

# 自定义日志处理器，将日志输出到GUI
class GuiHandler(logging.Handler):
    def __init__(self, text_widget):
        logging.Handler.__init__(self)
        self.text_widget = text_widget

    def emit(self, record):
        msg = self.format(record)

        def append():
            self.text_widget.configure(state="normal")
            self.text_widget.insert(tk.END, msg + "\n")
            self.text_widget.see(tk.END)
            self.text_widget.configure(state="disabled")

        self.text_widget.after(0, append)

@singleton_thread_safe
class WeChatGUI:
    def __init__(self):
        global _instance
        if _instance is not None:
            raise RuntimeError("WeChatGUI 已经在运行")
        _instance = self
        
        # 使用 ttkbootstrap 的样式
        self.style = Style(theme='cyborg')  # 使用深色科技风主题
        self.root = self.style.master
        self.root.title("微信选项选择机器人")
        self.root.geometry("1350x900")
        
        # 设置窗口在屏幕中央
        self.center_window()
        
        # 设置窗口图标（如果有的话）
        # self.root.iconbitmap('path/to/icon.ico')
        
        self.startup = None
        
        # 初始化暂停群列表
        self.paused_rooms = []
        
        # 创建主框架
        self.create_widgets()
        self.setup_logging()
        
        # 初始化选项配置（按群分组）
        self.group_option_configs = {}  # 存储每个群的选项配置
        self.current_selected_groups = []  # 当前选中的群列表

        # 初始化用户会话状态
        self.user_sessions = {}  # 存储用户会话状态
        
        self.load_config()
        self.init_wechat()

    def __del__(self):
        global _instance
        _instance = None

    def center_window(self):
        """将窗口居中显示"""
        # 获取屏幕的宽度和高度
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        # 修改窗口尺寸
        window_width = 1350
        window_height = 900
        
        # 计算窗口居中的坐标
        center_x = int((screen_width - window_width) / 2)
        center_y = int((screen_height - window_height) / 2)
        
        # 设置窗口位置
        self.root.geometry(f"{window_width}x{window_height}+{center_x}+{center_y}")

    def setup_logging(self):
        logging.basicConfig(
            level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
        )
        self.logger = logging.getLogger()

        # 添加GUI处理器
        gui_handler = GuiHandler(self.log_text)
        gui_handler.setFormatter(
            logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
        )
        self.logger.addHandler(gui_handler)

    def create_widgets(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建选项卡控件
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建群管理选项卡
        self.room_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.room_tab, text="群管理")

        # 创建选项配置选项卡
        self.option_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.option_tab, text="选项配置")

        # 创建日志选项卡
        self.log_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.log_tab, text="运行日志")
        
        # 设置群管理选项卡内容
        self.setup_room_tab()

        # 设置选项配置选项卡内容
        self.setup_option_tab()

        # 设置日志选项卡内容
        self.setup_log_tab()
        
        # 控制按钮
        control_frame = ttk.Frame(main_frame, padding="5")
        control_frame.pack(fill=tk.X, pady=5)

        self.start_btn = ttk.Button(control_frame, text="启动监控", command=self.start_monitoring, bootstyle="success")
        self.start_btn.pack(side=tk.LEFT, padx=5)

        self.stop_btn = ttk.Button(control_frame, text="停止监控", command=self.stop_monitoring, state="disabled", bootstyle="danger")
        self.stop_btn.pack(side=tk.LEFT, padx=5)

        # 添加保存配置按钮
        save_btn = ttk.Button(control_frame, text="保存配置", command=self.save_config, bootstyle="info-outline")
        save_btn.pack(side=tk.LEFT, padx=5)

    def setup_room_tab(self):
        # 群列表管理区域
        room_frame = ttk.Frame(self.room_tab, padding="10")
        room_frame.pack(fill=tk.BOTH, expand=True)

        # 左侧群列表
        left_frame = ttk.LabelFrame(room_frame, text="所有群列表", padding="10", bootstyle="primary")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 顶部控制区域
        control_frame = ttk.Frame(left_frame)
        control_frame.pack(fill=tk.X, pady=(0, 5))
        
        # 搜索框
        search_frame = ttk.Frame(control_frame)
        search_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Label(search_frame, text="搜索:").pack(side=tk.LEFT, padx=5)
        self.room_search_entry = ttk.Entry(search_frame)
        self.room_search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        self.room_search_button = ttk.Button(search_frame, text="搜索", command=self.search_rooms, bootstyle="primary-outline")
        self.room_search_button.pack(side=tk.LEFT, padx=5)
        
        # 刷新按钮
        refresh_btn = ttk.Button(
            control_frame, 
            text="刷新群列表", 
            command=self.refresh_room_list,
            bootstyle="primary-outline"
        )
        refresh_btn.pack(side=tk.RIGHT, padx=5)
        
        # 分页控件
        pagination_frame = ttk.Frame(left_frame)
        pagination_frame.pack(fill=tk.X, pady=5)
        
        self.room_prev_page_btn = ttk.Button(pagination_frame, text="上一页", command=self.prev_room_page, bootstyle="primary-outline")
        self.room_prev_page_btn.pack(side=tk.LEFT, padx=5)
        
        self.room_page_label = ttk.Label(pagination_frame, text="第 1 页")
        self.room_page_label.pack(side=tk.LEFT, padx=5)
        
        self.room_next_page_btn = ttk.Button(pagination_frame, text="下一页", command=self.next_room_page, bootstyle="primary-outline")
        self.room_next_page_btn.pack(side=tk.LEFT, padx=5)
        
        # 创建滚动条和树形视图的框架
        tree_frame = ttk.Frame(left_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        self.all_rooms_tree = ttk.Treeview(
            tree_frame, 
            columns=('room_id',),
            height=10,
            bootstyle="primary"
        )
        scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.all_rooms_tree.yview, bootstyle="primary-round")
        self.all_rooms_tree.configure(yscrollcommand=scrollbar.set)
        
        self.all_rooms_tree['show'] = 'tree headings'
        self.all_rooms_tree.heading('#0', text='群名称')
        self.all_rooms_tree.heading('room_id', text='群ID')
        self.all_rooms_tree.column('#0', width=200, stretch=True)
        self.all_rooms_tree.column('room_id', width=150, stretch=True)
        
        self.all_rooms_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 设置群分页相关变量
        self.room_current_page = 1
        self.room_page_size = 500
        self.room_total_pages = 1
        self.room_all_rooms = []
        self.room_search_keyword = ""

        # 中间操作按钮
        btn_frame = ttk.Frame(room_frame)
        btn_frame.pack(side=tk.LEFT, padx=10, fill=tk.Y)
        ttk.Button(btn_frame, text="添加 >>", command=self.add_selected_room, bootstyle="primary-outline").pack(pady=5)
        ttk.Button(btn_frame, text="<< 移除", command=self.remove_selected_room, bootstyle="danger-outline").pack(pady=5)
        ttk.Button(btn_frame, text="全选添加 >>", command=self.add_all_rooms, bootstyle="success-outline").pack(pady=5)

        # 右侧监控群列表
        right_frame = ttk.LabelFrame(room_frame, text="监控群列表", padding="10", bootstyle="primary")
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # 监控群树形视图
        monitor_frame = ttk.Frame(right_frame)
        monitor_frame.pack(fill=tk.BOTH, expand=True)
        
        self.monitored_rooms_tree = ttk.Treeview(
            monitor_frame,
            columns=('room_id',),
            height=10,
            bootstyle="primary"
        )
        monitor_scrollbar = ttk.Scrollbar(monitor_frame, orient="vertical", command=self.monitored_rooms_tree.yview, bootstyle="primary-round")
        self.monitored_rooms_tree.configure(yscrollcommand=monitor_scrollbar.set)
        
        self.monitored_rooms_tree['show'] = 'tree headings'
        self.monitored_rooms_tree.heading('#0', text='群名称')
        self.monitored_rooms_tree.heading('room_id', text='群ID')
        self.monitored_rooms_tree.column('#0', width=200, stretch=True)
        self.monitored_rooms_tree.column('room_id', width=150, stretch=True)
        
        self.monitored_rooms_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        monitor_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def add_all_rooms(self):
        """添加所有群到监控列表"""
        added_count = 0
        for room in self.room_all_rooms:
            try:
                nickname = room.get('nickname', '未知群名')
                conversation_id = room.get('conversation_id', '')
                if not conversation_id:
                    continue
                    
                # 检查是否已经存在
                exists = False
                for existing in self.monitored_rooms_tree.get_children():
                    if self.monitored_rooms_tree.item(existing)['values'][0] == conversation_id:
                        exists = True
                        break
                
                if not exists:
                    # 使用新的ID以避免冲突
                    new_id = f"monitored_{conversation_id}"
                    self.monitored_rooms_tree.insert('', 'end', iid=new_id,
                        text=nickname,  # 显示群名称
                        values=(conversation_id,))  # 保存群ID
                    added_count += 1
            except Exception as e:
                self.logger.error(f"处理群信息时出错: {e}, 群数据: {room}")
                continue
        
        self.logger.info(f"已添加 {added_count} 个群到监控列表")
        messagebox.showinfo("成功", f"已添加 {added_count} 个群到监控列表")

    def setup_option_tab(self):
        # 选项配置区域
        option_frame = ttk.Frame(self.option_tab, padding="10")
        option_frame.pack(fill=tk.BOTH, expand=True)

        # 群选择区域
        group_select_frame = ttk.LabelFrame(option_frame, text="选择配置群聊", padding="10", bootstyle="primary")
        group_select_frame.pack(fill=tk.X, padx=5, pady=(0, 5))

        # 群选择下拉框
        select_frame = ttk.Frame(group_select_frame)
        select_frame.pack(fill=tk.X, pady=5)

        ttk.Label(select_frame, text="选择群聊:", bootstyle="inverse-primary").pack(side=tk.LEFT, padx=(0, 5))

        # 创建Combobox用于群选择
        self.group_select_var = tk.StringVar()
        self.group_select_combo = ttk.Combobox(
            select_frame,
            textvariable=self.group_select_var,
            state="readonly",
            width=50
        )
        self.group_select_combo.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.group_select_combo.bind('<<ComboboxSelected>>', self.on_group_selection_changed)

        # 刷新群列表按钮
        ttk.Button(
            select_frame,
            text="刷新群列表",
            command=self.refresh_group_options,
            bootstyle="primary-outline"
        ).pack(side=tk.RIGHT, padx=5)

        # 多选支持说明
        info_frame = ttk.Frame(group_select_frame)
        info_frame.pack(fill=tk.X, pady=(5, 0))
        ttk.Label(
            info_frame,
            text="提示：选择群聊后，下方配置将应用到该群。同一群的新配置会覆盖旧配置。",
            bootstyle="info",
            font=("", 9)
        ).pack(side=tk.LEFT)

        # 自动回复内容配置
        auto_reply_frame = ttk.LabelFrame(option_frame, text="自动回复内容", padding="10", bootstyle="info")
        auto_reply_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        ttk.Label(auto_reply_frame, text="自动回复内容（多行文本）:", bootstyle="inverse-info").pack(anchor=tk.W, pady=(0, 5))

        # 创建滚动文本框
        auto_reply_text_frame = ttk.Frame(auto_reply_frame)
        auto_reply_text_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.auto_reply_text = tk.Text(auto_reply_text_frame, height=8, wrap=tk.WORD)
        auto_reply_scrollbar = ttk.Scrollbar(auto_reply_text_frame, orient="vertical", command=self.auto_reply_text.yview)
        self.auto_reply_text.configure(yscrollcommand=auto_reply_scrollbar.set)

        self.auto_reply_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        auto_reply_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 设置默认内容
        default_auto_reply = """请问您需要咨询以下哪项业务？请直接回复对应数字：
1.查询快递物流信息
2.投诉/反馈问题（丢件/破损/延误等）
3.联系人工客服
4.网点/快递员联系方式查询
5.修改收件地址或时间"""
        self.auto_reply_text.insert('1.0', default_auto_reply)

        # 选项回复配置（树状结构）
        options_frame = ttk.LabelFrame(option_frame, text="选项回复配置（树状结构）", padding="10", bootstyle="success")
        options_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 添加/删除按钮区域
        button_frame = ttk.Frame(options_frame)
        button_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(button_frame, text="添加根选项", command=self.add_root_option, bootstyle="success-outline").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="添加子选项", command=self.add_sub_option, bootstyle="info-outline").pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="删除选项", command=self.remove_selected_option, bootstyle="danger-outline").pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="展开全部", command=self.expand_all_options, bootstyle="secondary-outline").pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="折叠全部", command=self.collapse_all_options, bootstyle="secondary-outline").pack(side=tk.LEFT, padx=5)

        # 创建树状视图
        tree_frame = ttk.Frame(options_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True)

        # 创建Treeview控件
        self.options_tree = ttk.Treeview(
            tree_frame,
            columns=('reply_content', 'option_type'),
            height=15,
            bootstyle="success"
        )

        # 配置列
        self.options_tree.heading('#0', text='选项路径')
        self.options_tree.heading('reply_content', text='回复内容')
        self.options_tree.heading('option_type', text='类型')

        self.options_tree.column('#0', width=200, stretch=True)
        self.options_tree.column('reply_content', width=400, stretch=True)
        self.options_tree.column('option_type', width=100, stretch=False)

        # 添加滚动条
        tree_scrollbar_v = ttk.Scrollbar(tree_frame, orient="vertical", command=self.options_tree.yview)
        tree_scrollbar_h = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.options_tree.xview)
        self.options_tree.configure(yscrollcommand=tree_scrollbar_v.set, xscrollcommand=tree_scrollbar_h.set)

        # 布局
        self.options_tree.pack(side="left", fill="both", expand=True)
        tree_scrollbar_v.pack(side="right", fill="y")
        tree_scrollbar_h.pack(side="bottom", fill="x")

        # 绑定双击事件编辑
        self.options_tree.bind("<Double-1>", self.edit_option_item)

        # 初始化树状选项数据
        self.tree_option_data = {}  # 存储树状选项数据

        # 创建初始的根选项
        self.create_default_tree_options()

        # 保存按钮
        save_btn = ttk.Button(option_frame, text="保存配置", command=self.save_option_config, bootstyle="primary")
        save_btn.pack(pady=10)

    def create_default_tree_options(self):
        """创建默认的树状选项"""
        default_tree_data = {
            '1': {
                'text': '查询快递物流信息',
                'reply': '您好！请提供您的快递单号，我们将为您查询物流信息。',
                'type': 'final',
                'children': {}
            },
            '2': {
                'text': '投诉/反馈问题',
                'reply': '很抱歉给您带来不便，请选择具体问题类型：',
                'type': 'menu',
                'children': {
                    '2.1': {
                        'text': '丢件问题',
                        'reply': '请提供您的快递单号和详细情况，我们将立即为您处理丢件问题。',
                        'type': 'final',
                        'children': {}
                    },
                    '2.2': {
                        'text': '破损问题',
                        'reply': '请拍照上传破损情况并提供快递单号，我们将为您申请赔偿。',
                        'type': 'final',
                        'children': {}
                    },
                    '2.3': {
                        'text': '延误问题',
                        'reply': '请提供快递单号，我们将查询延误原因并为您跟进处理。',
                        'type': 'final',
                        'children': {}
                    }
                }
            },
            '3': {
                'text': '联系人工客服',
                'reply': '正在为您转接人工客服，请稍候...',
                'type': 'final',
                'children': {}
            }
        }

        self.tree_option_data = default_tree_data
        self.refresh_options_tree()

    def refresh_options_tree(self):
        """刷新选项树显示"""
        # 清空现有项目
        for item in self.options_tree.get_children():
            self.options_tree.delete(item)

        # 递归添加树项目
        def add_tree_items(parent_id, data, parent_item=''):
            for key, value in data.items():
                item_text = f"{key}. {value['text']}"
                item_id = self.options_tree.insert(
                    parent_item,
                    'end',
                    text=item_text,
                    values=(value['reply'], value['type']),
                    iid=key if not parent_item else f"{parent_item}_{key}"
                )

                # 递归添加子项目
                if value['children']:
                    add_tree_items(key, value['children'], item_id)

        add_tree_items('', self.tree_option_data)

        # 展开所有项目
        self.expand_all_options()

    def add_root_option(self):
        """添加根选项"""
        # 找到下一个可用的根选项编号
        existing_roots = [int(k) for k in self.tree_option_data.keys()]
        next_num = max(existing_roots) + 1 if existing_roots else 1

        # 创建新的根选项
        new_key = str(next_num)
        self.tree_option_data[new_key] = {
            'text': f'新选项{next_num}',
            'reply': f'这是选项{next_num}的回复内容',
            'type': 'final',
            'children': {}
        }

        self.refresh_options_tree()
        self.logger.info(f"添加了根选项{next_num}")

    def add_sub_option(self):
        """添加子选项"""
        selected = self.options_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个父选项！")
            return

        parent_item = selected[0]

        # 获取父选项的数据路径
        parent_path = self.get_option_path(parent_item)
        parent_data = self.get_option_data_by_path(parent_path)

        if not parent_data:
            messagebox.showerror("错误", "无法找到父选项数据！")
            return

        # 修改父选项类型为菜单类型
        parent_data['type'] = 'menu'

        # 找到下一个可用的子选项编号
        existing_children = list(parent_data['children'].keys())
        if existing_children:
            # 提取数字部分并找到最大值
            child_nums = []
            for child_key in existing_children:
                if '.' in child_key:
                    try:
                        child_num = int(child_key.split('.')[-1])
                        child_nums.append(child_num)
                    except ValueError:
                        pass
            next_child_num = max(child_nums) + 1 if child_nums else 1
        else:
            next_child_num = 1

        # 创建新的子选项键
        parent_key = parent_path[-1] if parent_path else '1'
        new_child_key = f"{parent_key}.{next_child_num}"

        # 添加子选项
        parent_data['children'][new_child_key] = {
            'text': f'子选项{next_child_num}',
            'reply': f'这是{parent_key}的子选项{next_child_num}的回复内容',
            'type': 'final',
            'children': {}
        }

        self.refresh_options_tree()
        self.logger.info(f"为选项{parent_key}添加了子选项{next_child_num}")

    def remove_selected_option(self):
        """删除选中的选项"""
        selected = self.options_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择要删除的选项！")
            return

        item = selected[0]
        option_path = self.get_option_path(item)

        if len(option_path) == 1:
            # 删除根选项
            if len(self.tree_option_data) <= 1:
                messagebox.showwarning("警告", "至少需要保留一个根选项！")
                return
            del self.tree_option_data[option_path[0]]
        else:
            # 删除子选项
            parent_data = self.get_option_data_by_path(option_path[:-1])
            if parent_data and 'children' in parent_data:
                del parent_data['children'][option_path[-1]]

                # 如果父选项没有子选项了，改为final类型
                if not parent_data['children']:
                    parent_data['type'] = 'final'

        self.refresh_options_tree()
        self.logger.info(f"删除了选项: {' -> '.join(option_path)}")

    def get_option_path(self, item_id):
        """获取选项的路径"""
        if '_' in item_id:
            return item_id.split('_')
        else:
            return [item_id]

    def get_option_data_by_path(self, path):
        """根据路径获取选项数据"""
        data = self.tree_option_data
        for key in path:
            if key in data:
                if len(path) == 1 or key == path[-1]:
                    return data[key]
                else:
                    data = data[key]['children']
            else:
                return None
        return None

    def expand_all_options(self):
        """展开所有选项"""
        def expand_item(item):
            self.options_tree.item(item, open=True)
            for child in self.options_tree.get_children(item):
                expand_item(child)

        for item in self.options_tree.get_children():
            expand_item(item)

    def collapse_all_options(self):
        """折叠所有选项"""
        def collapse_item(item):
            self.options_tree.item(item, open=False)
            for child in self.options_tree.get_children(item):
                collapse_item(child)

        for item in self.options_tree.get_children():
            collapse_item(item)

    def edit_option_item(self, event):
        """双击编辑选项项目"""
        item = self.options_tree.selection()[0]
        option_path = self.get_option_path(item)
        option_data = self.get_option_data_by_path(option_path)

        if not option_data:
            return

        # 创建编辑对话框
        self.show_option_edit_dialog(option_path, option_data)

    def show_option_edit_dialog(self, option_path, option_data):
        """显示选项编辑对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title(f"编辑选项: {' -> '.join(option_path)}")
        dialog.geometry("600x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # 选项文本
        ttk.Label(dialog, text="选项文本:", font=("", 10, "bold")).pack(anchor=tk.W, padx=10, pady=(10, 5))
        text_entry = ttk.Entry(dialog, width=70)
        text_entry.pack(padx=10, pady=(0, 10), fill=tk.X)
        text_entry.insert(0, option_data['text'])

        # 回复内容
        ttk.Label(dialog, text="回复内容:", font=("", 10, "bold")).pack(anchor=tk.W, padx=10, pady=(10, 5))

        reply_frame = ttk.Frame(dialog)
        reply_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        reply_text = tk.Text(reply_frame, height=8, wrap=tk.WORD)
        reply_scrollbar = ttk.Scrollbar(reply_frame, orient="vertical", command=reply_text.yview)
        reply_text.configure(yscrollcommand=reply_scrollbar.set)

        reply_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        reply_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        reply_text.insert('1.0', option_data['reply'])

        # 选项类型
        ttk.Label(dialog, text="选项类型:", font=("", 10, "bold")).pack(anchor=tk.W, padx=10, pady=(10, 5))

        type_frame = ttk.Frame(dialog)
        type_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        type_var = tk.StringVar(value=option_data['type'])
        ttk.Radiobutton(type_frame, text="最终选项（直接回复）", variable=type_var, value="final").pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(type_frame, text="菜单选项（显示子选项）", variable=type_var, value="menu").pack(side=tk.LEFT)

        # 按钮区域
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        def save_changes():
            option_data['text'] = text_entry.get().strip()
            option_data['reply'] = reply_text.get('1.0', tk.END).strip()
            option_data['type'] = type_var.get()

            self.refresh_options_tree()
            dialog.destroy()
            self.logger.info(f"更新了选项: {' -> '.join(option_path)}")

        def cancel_changes():
            dialog.destroy()

        ttk.Button(button_frame, text="保存", command=save_changes, bootstyle="success").pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=cancel_changes, bootstyle="secondary").pack(side=tk.RIGHT)

    def refresh_group_options(self):
        """刷新群选择下拉框的选项"""
        try:
            # 获取监控群列表
            group_options = []
            for item in self.monitored_rooms_tree.get_children():
                group_name = self.monitored_rooms_tree.item(item)['text']
                group_id = self.monitored_rooms_tree.item(item)['values'][0]
                group_options.append(f"{group_name} ({group_id})")

            # 更新下拉框选项
            self.group_select_combo['values'] = group_options

            if group_options:
                if not self.group_select_var.get():
                    self.group_select_combo.current(0)  # 默认选择第一个
                    self.on_group_selection_changed()
            else:
                self.group_select_var.set("")
                messagebox.showwarning("提示", "请先在群管理中添加监控群！")

        except Exception as e:
            self.logger.error(f"刷新群选项失败: {str(e)}")

    def on_group_selection_changed(self, event=None):
        """群选择改变时的处理"""
        try:
            selected = self.group_select_var.get()
            if not selected:
                return

            # 解析群ID
            if " (" in selected and selected.endswith(")"):
                group_id = selected.split(" (")[-1][:-1]
                self.current_selected_groups = [group_id]

                # 加载该群的配置
                self.load_group_config(group_id)

                self.logger.info(f"选择了群: {selected}")

        except Exception as e:
            self.logger.error(f"群选择处理失败: {str(e)}")

    def load_group_config(self, group_id):
        """加载指定群的配置"""
        try:
            if group_id in self.group_option_configs:
                config = self.group_option_configs[group_id]

                # 加载自动回复内容
                self.auto_reply_text.delete('1.0', tk.END)
                self.auto_reply_text.insert('1.0', config.get('auto_reply', ''))

                # 加载树状选项数据
                tree_data = config.get('tree_options', {})
                if tree_data:
                    self.tree_option_data = tree_data
                else:
                    # 兼容旧版本的平面选项配置
                    old_options = config.get('option_replies', {})
                    if old_options:
                        self.tree_option_data = self.convert_flat_to_tree(old_options)
                    else:
                        self.create_default_tree_options()
                        return

                self.refresh_options_tree()
            else:
                # 新群，使用默认配置
                self.auto_reply_text.delete('1.0', tk.END)
                default_auto_reply = """请问您需要咨询以下哪项业务？请直接回复对应数字：
1.查询快递物流信息
2.投诉/反馈问题（丢件/破损/延误等）
3.联系人工客服"""
                self.auto_reply_text.insert('1.0', default_auto_reply)

                # 创建默认树状选项
                self.create_default_tree_options()

        except Exception as e:
            self.logger.error(f"加载群配置失败: {str(e)}")

    def convert_flat_to_tree(self, flat_options):
        """将平面选项配置转换为树状结构"""
        tree_data = {}
        for key, reply in flat_options.items():
            tree_data[key] = {
                'text': f'选项{key}',
                'reply': reply,
                'type': 'final',
                'children': {}
            }
        return tree_data

    def save_option_config(self):
        """保存选项配置"""
        try:
            if not self.current_selected_groups:
                messagebox.showwarning("警告", "请先选择要配置的群！")
                return

            # 获取当前配置
            current_config = {
                'auto_reply': self.auto_reply_text.get('1.0', tk.END).strip(),
                'tree_options': self.tree_option_data.copy()
            }

            # 为选中的群保存配置
            for group_id in self.current_selected_groups:
                self.group_option_configs[group_id] = current_config.copy()
                self.logger.info(f"保存群 {group_id} 的树状选项配置")

            # 保存到配置文件
            self.save_config()

            # 更新 startup 中的选项配置
            if self.startup:
                self.startup.group_option_configs = self.group_option_configs

            group_names = []
            for group_id in self.current_selected_groups:
                for item in self.monitored_rooms_tree.get_children():
                    if self.monitored_rooms_tree.item(item)['values'][0] == group_id:
                        group_names.append(self.monitored_rooms_tree.item(item)['text'])
                        break

            messagebox.showinfo("成功", f"已保存群 {', '.join(group_names)} 的树状选项配置！")

        except Exception as e:
            messagebox.showerror("错误", f"保存选项配置失败: {str(e)}")

    def setup_log_tab(self):
        # 日志显示区
        log_frame = ttk.Frame(self.log_tab, padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)

        self.log_text = ttk.Text(log_frame, wrap=tk.WORD, height=24)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        self.log_text.configure(state="disabled")
        
        # 添加滚动条
        log_scrollbar = ttk.Scrollbar(self.log_text, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)





    def init_wechat(self):
        def run():
            try:
                # 确保之前的实例被清理
                if self.startup:
                    try:
                        # 注册空回调替代注销
                        self.startup.wechat.msg_register(ntwork.MT_RECV_TEXT_MSG)(lambda x, y: None)
                    except:
                        pass
                    self.startup = None
                    time.sleep(1)  # 等待资源释放
                
                # 初始化新实例
                self.startup = StartUp(self)
                
                try:
                    self.startup.wechat.open(smart=True)
                except Exception as e:
                    self.logger.error(f"打开企业微信失败: {e}")
                    # 尝试重新初始化 ntwork
                    ntwork.exit_()
                    time.sleep(2)
                    self.startup.wechat = ntwork.WeWork()
                    self.startup.wechat.open(smart=True)
                
                self.logger.info("等待登录......")
                self.startup.wechat.wait_login(timeout=500)
                self.logger.info("登录成功，等待数据同步...")
                time.sleep(20)  # 增加等待时间到20秒，让数据更充分同步
                
                # 添加重试机制获取登录信息
                max_retries = 3
                for i in range(max_retries):
                    try:
                        login_info = self.startup.wechat.get_login_info()
                        if login_info and 'user_id' in login_info:
                            self.startup.user_id = login_info["user_id"]
                            if login_info.get("nickname") == '':
                                self.startup.name = login_info.get("username")
                            else:
                                self.startup.name = login_info.get("nickname")
                            self.logger.info(f"登录信息: user_id:{self.startup.user_id}, name:{self.startup.name}")
                            break
                        else:
                            if i < max_retries - 1:
                                self.logger.warning(f"获取登录信息不完整，正在重试... ({i + 1}/{max_retries})")
                                time.sleep(2)  # 等待2秒后重试
                            else:
                                raise RuntimeError("无法获取完整的登录信息")
                    except Exception as e:
                        if i < max_retries - 1:
                            self.logger.warning(f"获取登录信息失败，正在重试... ({i + 1}/{max_retries})")
                            time.sleep(2)
                        else:
                            raise

               #获取群列表
                self.refresh_room_list()
                
            except Exception as e:
                self.logger.error(f"初始化微信失败: {str(e)}")
                self.logger.error(f"错误详情: ", exc_info=True)
                if self.startup:
                    self.startup.exit_program()
                    self.startup = None
                # 重置按钮状态
                self.root.after(0, self._reset_button_state)

        # 在新线程中初始化微信
        threading.Thread(target=run, daemon=True).start()

    def start_monitoring(self):
        global _instance
        if _instance is not self:
            self.logger.error("检测到多个程序实例，请关闭其他窗口")
            return
            
        # 检查是否已登录
        if not self.startup:
            self.logger.error("微信未初始化，请重启程序")
            return
            
        try:
            self.startup.exit_flag = False
            login_info = self.startup.wechat.get_login_info()
            if not login_info:
                self.logger.error("微信未登录，请先登录")
                return
        except:
            self.logger.error("获取登录状态失败")
            return
            
        self.save_config()  # 启动前保存当前配置

        # 设置选项配置参数
        try:
            # 更新按群分组的选项配置
            self.startup.group_option_configs = self.group_option_configs

            # 更新暂停群列表
            self.startup.paused_rooms = self.paused_rooms

            # 注册消息回调
            self.startup.wechat.msg_register(ntwork.MT_RECV_TEXT_MSG)(self.startup.on_recv_message)
            self.logger.info("消息回调注册成功")

            # 更新按钮状态
            self.start_btn.configure(state="disabled")
            self.stop_btn.configure(state="normal")
            self.logger.info("监控已启动")

        except Exception as e:
            self.logger.error(f"启动监控失败: {e}")
            self.logger.error("错误详情: ", exc_info=True)

    def stop_monitoring(self):
        if self.startup:
            # 取消消息回调
            self.startup.wechat.msg_register(ntwork.MT_RECV_TEXT_MSG)(lambda x, y: None)
            # 设置退出标志，确保消息处理循环会检查这个标志
            self.startup.exit_flag = True

            self.logger.info("消息回调已取消")

            self.start_btn.configure(state="normal")
            self.stop_btn.configure(state="disabled")
            self.logger.info("监控已停止")

    def run(self):
        try:
            self.root.mainloop()
        finally:
            global _instance
            _instance = None
            if self.startup:
                self.startup.exit_program()

    def load_config(self):
        try:
            if os.path.exists("config.json"):
                with open("config.json", "r", encoding="utf-8") as f:
                    config = json.load(f)

                    # 加载按群分组的选项配置
                    group_option_configs = config.get('group_option_configs', {})
                    if group_option_configs:
                        self.group_option_configs = group_option_configs
                    else:
                        # 兼容旧版本配置
                        old_option_config = config.get('option_config', {})
                        if old_option_config:
                            # 将旧配置应用到所有监控群
                            monitored_rooms = config.get('monitored_rooms', [])
                            for room in monitored_rooms:
                                self.group_option_configs[room['id']] = old_option_config

                    # 刷新群选择下拉框
                    if hasattr(self, 'group_select_combo'):
                        self.refresh_group_options()

                    # 加载监控群列表
                    monitored_rooms = config.get('monitored_rooms', [])
                    for room in monitored_rooms:
                        new_id = f"monitored_{room['id']}"
                        self.monitored_rooms_tree.insert('', 'end', iid=new_id,
                            text=room['name'],
                            values=(room['id'],))

                    # 加载暂停群列表
                    self.paused_rooms = config.get('paused_rooms', [])
                    
                    self.logger.info("配置加载成功")
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            # 初始化暂停群列表，确保默认存在
            self.paused_rooms = []

    def save_config(self):
        # 确保 paused_rooms 属性存在
        if not hasattr(self, 'paused_rooms'):
            self.paused_rooms = []

        config = {
            'group_option_configs': self.group_option_configs,
            'paused_rooms': self.paused_rooms,
            'monitored_rooms': [
                {
                    'name': self.monitored_rooms_tree.item(item)['text'],
                    'id': self.monitored_rooms_tree.item(item)['values'][0]
                }
                for item in self.monitored_rooms_tree.get_children()
            ]
        }
        try:
            with open('config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            self.logger.info("配置保存成功")
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")

    def add_selected_room(self):
        selected = self.all_rooms_tree.selection()
        for item in selected:
            name = self.all_rooms_tree.item(item, 'text')
            room_id = self.all_rooms_tree.item(item, 'values')[0]
            # 检查是否已经存在
            exists = False
            for existing in self.monitored_rooms_tree.get_children():
                if self.monitored_rooms_tree.item(existing)['values'][0] == room_id:
                    exists = True
                    break
            
            if not exists:
                # 使用新的ID以避免冲突
                new_id = f"monitored_{room_id}"
                self.monitored_rooms_tree.insert('', 'end', iid=new_id,
                    text=name,  # 显示群名称
                    values=(room_id,))  # 保存群ID
                self.logger.info(f"添加监控群：{name}")

    def remove_selected_room(self):
        selected = self.monitored_rooms_tree.selection()
        for item in selected:
            self.monitored_rooms_tree.delete(item)

    def search_rooms(self):
        """搜索群列表"""
        self.room_search_keyword = self.room_search_entry.get().strip()
        self.room_current_page = 1
        self.update_rooms_display()

    def prev_room_page(self):
        """上一页群列表"""
        if self.room_current_page > 1:
            self.room_current_page -= 1
            self.update_rooms_display()

    def next_room_page(self):
        """下一页群列表"""
        if self.room_current_page < self.room_total_pages:
            self.room_current_page += 1
            self.update_rooms_display()

    def update_rooms_display(self):
        """更新群列表显示"""
        # 清空当前树形视图中的所有项
        for item in self.all_rooms_tree.get_children():
            self.all_rooms_tree.delete(item)
        
        # 计算分页显示
        filtered_rooms = self.room_all_rooms
        if self.room_search_keyword:
            filtered_rooms = [
                room for room in self.room_all_rooms
                if self.room_search_keyword.lower() in room.get('nickname', '').lower()
            ]
        
        # 计算总页数
        self.room_total_pages = max(1, (len(filtered_rooms) + self.room_page_size - 1) // self.room_page_size)
        
        # 确保当前页在有效范围内
        if self.room_current_page > self.room_total_pages:
            self.room_current_page = self.room_total_pages
        
        # 更新页码显示
        self.room_page_label.config(text=f"第 {self.room_current_page}/{self.room_total_pages} 页")
        
        # 计算当前页应显示的群列表
        start_idx = (self.room_current_page - 1) * self.room_page_size
        end_idx = min(start_idx + self.room_page_size, len(filtered_rooms))
        current_page_rooms = filtered_rooms[start_idx:end_idx]
        
        # 显示当前页的群
        for room in current_page_rooms:
            try:
                nickname = room.get('nickname', '未知群名')
                conversation_id = room.get('conversation_id', '')
                if not conversation_id:
                    continue
                
                self.all_rooms_tree.insert('', 'end', 
                    text=nickname,
                    values=(conversation_id,),
                    tags=('room',))
            except Exception as e:
                self.logger.error(f"处理群信息时出错: {e}, 群数据: {room}")
                continue
        
        self.logger.info(f"当前显示第 {self.room_current_page}/{self.room_total_pages} 页，共 {len(filtered_rooms)} 个群（显示 {len(current_page_rooms)} 个）")

    def refresh_room_list(self):
        if not self.startup or not self.startup.wechat:
            self.logger.error("微信未初始化，请重启程序")
            return
        
        try:
            self.logger.info("正在刷新群列表...")
            
            # 添加重试机制
            max_retries = 3
            self.room_all_rooms = []
            total_rooms = 0
            current_page = 1
            
            while True:
                success = False
                for i in range(max_retries):
                    try:
                        # 带分页参数获取群列表
                        rooms = self.startup.wechat.get_rooms(page_num=current_page, page_size=self.room_page_size)
                        if rooms and 'room_list' in rooms and rooms['room_list']:
                            room_count = len(rooms['room_list'])
                            self.logger.info(f"成功获取第 {current_page} 页群列表，有 {room_count} 个群")
                            self.room_all_rooms.extend(rooms['room_list'])
                            total_rooms += room_count
                            success = True
                            break
                        else:
                            # 如果返回空列表，可能已经获取完所有群
                            if current_page > 1:
                                self.logger.info(f"第 {current_page} 页没有更多群，获取完成")
                                success = True
                                break
                            else:
                                self.logger.warning(f"第 {current_page} 页获取群列表为空，尝试重试... ({i + 1}/{max_retries})")
                                time.sleep(2)
                    except Exception as e:
                        self.logger.warning(f"获取群列表第 {current_page} 页失败: {e}，尝试重试... ({i + 1}/{max_retries})")
                        time.sleep(2)
                
                # 如果当前页获取失败且是第一页，则无法继续
                if not success and current_page == 1:
                    self.logger.error("无法获取群列表，请检查企业微信状态")
                    return
                
                # 如果当前页获取的群数量少于页大小，说明已经没有更多群了
                if success and (len(rooms.get('room_list', [])) < self.room_page_size):
                    self.logger.info(f"已获取所有群，总共 {total_rooms} 个群")
                    break
                
                # 继续获取下一页
                current_page += 1
            
            # 显示第一页
            self.room_current_page = 1
            self.room_search_keyword = ""
            if hasattr(self, 'room_search_entry'):
                self.room_search_entry.delete(0, tk.END)
            self.update_rooms_display()
            
        except Exception as e:
            self.logger.error(f"刷新群列表失败: {str(e)}")
            self.logger.error("错误详情: ", exc_info=True)




    # 添加重置按钮状态的方法
    def _reset_button_state(self):
        self.start_btn.configure(state="normal")
        self.stop_btn.configure(state="disabled")



@singleton_thread_safe
# 修改StartUp类
class StartUp:
    def __init__(self, gui_instance):
        self.logger = logging.getLogger()
        self.wechat = ntwork.WeWork()
        self.exit_flag = False
        self.user_id = None
        self.name = None
        self.gui = gui_instance
        # 获取GUI实例中的暂停群列表
        self.paused_rooms = gui_instance.paused_rooms
        # 获取按群分组的选项配置
        self.group_option_configs = gui_instance.group_option_configs
        # 用户会话状态
        self.user_sessions = gui_instance.user_sessions

    def on_recv_message(self, wechat_instance: ntwork.WeWork, message):
        # 使用消息ID来防止重复处理
        message_id = message["data"].get("local_id") or message["data"].get("server_id")
        current_time = time.time()
        
        # 创建一个类属性来记录已处理的消息
        if not hasattr(self, 'processed_messages'):
            self.processed_messages = {}
        
        # 如果该消息已处理过且时间间隔小于30秒，则忽略
        if message_id in self.processed_messages:
            if current_time - self.processed_messages[message_id] < 30:
                self.logger.info(f"忽略重复接收的消息: {message_id}")
                return
        
        # 记录此消息的处理时间
        self.processed_messages[message_id] = current_time
        
        # 清理过期的消息记录（超过1分钟的）
        self.processed_messages = {k: v for k, v in self.processed_messages.items() 
                                  if current_time - v < 60}
        
        # 以下是原有的消息处理逻辑
        # 在方法开始就检查退出标志
        if self.exit_flag:
            return
        # 添加消息接收日志
        self.logger.info(f"收到原始消息: {json.dumps(message, ensure_ascii=False)}")
        self.logger.info(f"机器人名字: {self.name}")

        # 检查按群分组的选项配置是否存在
        if not hasattr(self, 'group_option_configs'):
            self.logger.error("群选项配置未初始化")
            return
            
        data = message["data"]
        content = data.get("content", "").strip()
        if not content:
            return
        room_wxid = data.get("conversation_id")
        from_wxid = data.get("sender")
        sender_name = data.get("sender_name", "")
        # 检查是否是自己发送的消息
        if from_wxid == self.user_id:
            self.logger.info("忽略自己发送的消息")
            return

        if room_wxid=="FILEASSIST":
            return
        self.logger.info(f"消息类型: {data.get('content_type')}")
        # 添加消息类型检查日志
        if data.get("content_type") not in [0,2]:
            self.logger.info(f"忽略非文本消息，类型: {data.get('content_type')}")
            return
        # 添加消息基础信息日志
        self.logger.info(f"消息处理开始 | 发送者: {sender_name}({from_wxid}) | 会话ID: {room_wxid} | 内容: {content}")
        
        # 根据room_wxid前缀判断是群聊还是私聊
        is_private = room_wxid and room_wxid.startswith('S:')
        is_group = room_wxid and room_wxid.startswith('R:')
        
        self.logger.info(f"消息类型: {'私聊' if is_private else '群聊' if is_group else '未知类型'}")
        


        # 获取监控群列表
        monitored_rooms = [
            self.gui.monitored_rooms_tree.item(item)['values'][0]
            for item in self.gui.monitored_rooms_tree.get_children()
        ]
        self.logger.info(f"当前监控群列表: {monitored_rooms}")

        # 群聊消息处理
        if is_group:
            self.logger.info(f"群消息检查 | 群ID: {room_wxid} | 是否在监控列表: {room_wxid in monitored_rooms}")

            # 检查群消息是否在监控列表中
            if room_wxid not in monitored_rooms:
                self.logger.info(f"群 {room_wxid} 不在监控列表中，忽略消息")
                return
        

        try:
            # 获取当前群的选项配置
            current_group_config = self.group_option_configs.get(room_wxid, {})
            if not current_group_config:
                self.logger.warning(f"群 {room_wxid} 没有配置选项，忽略消息")
                return

            # 生成用户会话ID
            session_id = f"{room_wxid}_{from_wxid}" if is_group else from_wxid
            current_time = time.time()

            # 检查用户会话状态
            if session_id in self.user_sessions:
                session = self.user_sessions[session_id]
                # 检查会话是否超时（5分钟）
                if current_time - session['start_time'] > 300:  # 5分钟 = 300秒
                    self.logger.info(f"用户会话超时，清理会话: {session_id}")
                    del self.user_sessions[session_id]
                    session = None
                else:
                    session = self.user_sessions[session_id]
            else:
                session = None

            # 确定发送目标
            target_id = room_wxid if is_group else room_wxid

            # 获取树状选项数据
            tree_options = current_group_config.get('tree_options', {})
            if not tree_options:
                # 兼容旧版本配置
                old_options = current_group_config.get('option_replies', {})
                if old_options:
                    tree_options = self.convert_flat_to_tree_for_processing(old_options)
                else:
                    self.logger.warning(f"群 {room_wxid} 没有配置选项数据")
                    return

            # 如果用户没有会话或会话已超时，发送自动回复内容
            if not session:
                auto_reply = current_group_config.get('auto_reply', '')
                if auto_reply:
                    # 创建新会话
                    self.user_sessions[session_id] = {
                        'start_time': current_time,
                        'waiting_for_option': True,
                        'group_id': room_wxid,
                        'current_path': []  # 当前对话路径
                    }

                    wechat_instance.send_text(target_id, auto_reply)
                    self.logger.info(f"发送自动回复给用户: {from_wxid}, 会话ID: {session_id}, 群ID: {room_wxid}")
                else:
                    self.logger.warning(f"群 {room_wxid} 的自动回复内容为空")
                return

            # 如果用户在等待选项回复
            if session.get('waiting_for_option', False):
                content_stripped = content.strip()
                current_path = session.get('current_path', [])

                # 获取当前层级的选项数据
                current_level_options = self.get_current_level_options(tree_options, current_path)

                if content_stripped in current_level_options:
                    option_data = current_level_options[content_stripped]
                    option_reply = option_data.get('reply', '')
                    option_type = option_data.get('type', 'final')

                    if option_reply:
                        # 如果是菜单选项，需要在回复内容后添加子选项列表
                        if option_type == 'menu':
                            # 获取子选项并生成选项列表
                            children = option_data.get('children', {})
                            if children:
                                # 构建子选项列表
                                sub_options = []
                                # 改进排序逻辑，正确处理小数点序号
                                def sort_key(x):
                                    try:
                                        if '.' in x:
                                            parts = x.split('.')
                                            return (int(parts[0]), int(parts[1]))
                                        else:
                                            return (int(x), 0)
                                    except ValueError:
                                        return (999, 999)  # 无法解析的放到最后

                                for sub_key in sorted(children.keys(), key=sort_key):
                                    sub_text = children[sub_key]['text']
                                    sub_options.append(f"{sub_key}. {sub_text}")

                                # 将子选项添加到回复内容中
                                if sub_options:
                                    enhanced_reply = option_reply + "\n" + "\n".join(sub_options)
                                else:
                                    enhanced_reply = option_reply
                            else:
                                enhanced_reply = option_reply
                        else:
                            enhanced_reply = option_reply

                        # 处理特殊格式的回复内容
                        self.process_special_reply(wechat_instance, target_id, enhanced_reply, from_wxid, room_wxid)

                        if option_type == 'final':
                            # 最终选项，结束对话
                            del self.user_sessions[session_id]
                            self.logger.info(f"对话结束，清理会话: {session_id}")
                        elif option_type == 'menu':
                            # 菜单选项，更新路径并继续对话
                            new_path = current_path + [content_stripped]
                            session['current_path'] = new_path
                            self.logger.info(f"进入子菜单: {' -> '.join(new_path)}")
                    else:
                        wechat_instance.send_text(target_id, f"选项{content_stripped}的回复内容未配置")
                        self.logger.warning(f"群 {room_wxid} 选项{content_stripped}的回复内容未配置")
                else:
                    # 用户输入的不是有效选项序号，提示重新输入
                    available_keys = list(current_level_options.keys())
                    if available_keys:
                        # 提取数字部分进行排序
                        try:
                            numeric_keys = []
                            for key in available_keys:
                                if '.' in key:
                                    numeric_keys.append(float(key))
                                else:
                                    numeric_keys.append(int(key))
                            numeric_keys.sort()
                            options_range = f"（{numeric_keys[0]}-{numeric_keys[-1]}）"
                        except:
                            options_range = f"（{', '.join(sorted(available_keys))}）"
                    else:
                        options_range = ""
                    wechat_instance.send_text(target_id, f"请回复对应的数字序号{options_range}")
                    self.logger.info(f"用户输入无效选项: {content_stripped}, 提示重新输入, 群ID: {room_wxid}")
                return

        except Exception as e:
            self.logger.error(f"消息处理异常: {str(e)}", exc_info=True)

    def convert_flat_to_tree_for_processing(self, flat_options):
        """将平面选项配置转换为树状结构（用于消息处理）"""
        tree_data = {}
        for key, reply in flat_options.items():
            tree_data[key] = {
                'text': f'选项{key}',
                'reply': reply,
                'type': 'final',
                'children': {}
            }
        return tree_data

    def get_current_level_options(self, tree_options, current_path):
        """获取当前层级的选项"""
        current_data = tree_options

        # 根据路径导航到当前层级
        for path_item in current_path:
            if path_item in current_data and 'children' in current_data[path_item]:
                current_data = current_data[path_item]['children']
            else:
                return {}

        return current_data

    def process_special_reply(self, wechat_instance, target_id, reply_content, from_wxid, room_wxid):
        """处理特殊格式的回复内容"""
        try:
            self.logger.info(f"开始处理特殊回复，内容长度: {len(reply_content)}")

            # 检查是否包含特殊参数
            if '\n' in reply_content and any(param in reply_content for param in ['wait_reply=', 'title=', 'desc=', 'url=', 'img=']):
                self.logger.info("检测到特殊回复格式")

                # 解析特殊格式
                params = self.parse_special_reply_params(reply_content)
                self.logger.info(f"解析到的参数: {params}")

                # 先发送等待回复（如果有）
                if 'wait_reply' in params:
                    wechat_instance.send_text(target_id, params['wait_reply'])
                    self.logger.info(f"发送等待回复给用户: {from_wxid}, 群ID: {room_wxid}")
                    time.sleep(random.uniform(1, 3))  # 等待1-3秒

                # 检查是否有链接卡片参数
                required_params = ['title', 'desc', 'url', 'img']
                missing_params = [p for p in required_params if p not in params]

                if not missing_params:
                    self.logger.info("所有链接卡片参数都存在，尝试发送链接卡片")
                    # 发送链接卡片
                    try:
                        self.logger.info(f"调用send_link_card: conversation_id={target_id}, title={params['title']}, desc={params['desc']}, url={params['url']}, image_url={params['img']}")

                        wechat_instance.send_link_card(
                            conversation_id=target_id,
                            title=params['title'],
                            desc=params['desc'],
                            url=params['url'],
                            image_url=params['img']
                        )
                        self.logger.info(f"✅ 成功发送链接卡片给用户: {from_wxid}, 群ID: {room_wxid}")
                    except Exception as e:
                        self.logger.error(f"❌ 发送链接卡片失败: {str(e)}")
                        self.logger.error(f"异常类型: {type(e).__name__}")
                        # 如果链接卡片发送失败，发送文本形式
                        fallback_text = f"标题: {params['title']}\n描述: {params['desc']}\n链接: {params['url']}"
                        wechat_instance.send_text(target_id, fallback_text)
                        self.logger.info("发送了fallback文本")
                else:
                    self.logger.warning(f"链接卡片参数不完整，缺失: {missing_params}")
                    # 如果没有完整的链接卡片参数，发送普通文本
                    normal_text = self.extract_normal_text_from_special_reply(reply_content)
                    if normal_text:
                        wechat_instance.send_text(target_id, normal_text)
                        self.logger.info(f"发送普通文本回复给用户: {from_wxid}, 群ID: {room_wxid}")
                    else:
                        # 如果没有普通文本，发送原始内容
                        wechat_instance.send_text(target_id, reply_content)
                        self.logger.info(f"发送原始内容给用户: {from_wxid}, 群ID: {room_wxid}")
            else:
                self.logger.info("普通文本回复")
                # 普通文本回复
                wechat_instance.send_text(target_id, reply_content)
                self.logger.info(f"发送选项回复给用户: {from_wxid}, 群ID: {room_wxid}")

        except Exception as e:
            self.logger.error(f"处理特殊回复格式失败: {str(e)}")
            self.logger.error(f"异常类型: {type(e).__name__}")
            # 发送原始内容作为备用
            wechat_instance.send_text(target_id, reply_content)

    def parse_special_reply_params(self, reply_content):
        """解析特殊回复格式的参数"""
        params = {}
        lines = reply_content.split('\n')

        for line in lines:
            line = line.strip()
            if '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip()

                if key in ['wait_reply', 'title', 'desc', 'url', 'img']:
                    params[key] = value

        return params

    def extract_normal_text_from_special_reply(self, reply_content):
        """从特殊回复格式中提取普通文本"""
        lines = reply_content.split('\n')
        normal_lines = []

        for line in lines:
            line = line.strip()
            # 如果不是特殊参数行，则作为普通文本
            if not any(line.startswith(param + '=') for param in ['wait_reply', 'title', 'desc', 'url', 'img']):
                normal_lines.append(line)

        return '\n'.join(normal_lines).strip()

    def exit_program(self):
        self.logger.info("正在退出程序...")
        self.exit_flag = True
        keyboard.unhook_all()
        ntwork.exit_()


 

def main():
    try:
        app = WeChatGUI()
        app.run()
    except Exception as e:
        import traceback
        print(f"程序启动失败: {e}")
        print(traceback.format_exc())
        input("按回车键退出...")


if __name__ == "__main__":
    main() 