2025-08-04 07:06:28 - src.core.message_processor.2813349122304 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250804.log
2025-08-04 07:06:28 - src.core.message_processor.2813349122304 - INFO - 消息处理器日志系统初始化完成
2025-08-04 07:06:28 - src.core.message_processor.2813349122304 - INFO - 设置间隔时间为: 1800秒 (30分钟)
2025-08-04 07:06:28 - src.core.message_processor.2813349122304 - INFO - 设置间隔时间为: 60秒 (1分钟)
2025-08-04 07:10:13 - src.core.message_processor.3264526343136 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250804.log
2025-08-04 07:10:13 - src.core.message_processor.3264526343136 - INFO - 消息处理器日志系统初始化完成
2025-08-04 07:10:13 - src.core.message_processor.3264526343136 - INFO - 设置间隔时间为: 1800秒 (30分钟)
2025-08-04 07:10:13 - src.core.message_processor.3264526343136 - INFO - 设置间隔时间为: 60秒 (1分钟)
2025-08-04 07:10:42 - src.core.message_processor.2276788102768 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250804.log
2025-08-04 07:10:42 - src.core.message_processor.2276788102768 - INFO - 消息处理器日志系统初始化完成
2025-08-04 07:10:42 - src.core.message_processor.2276788102768 - INFO - 设置间隔时间为: 1800秒 (30分钟)
2025-08-04 07:10:42 - src.core.message_processor.2276788102768 - INFO - 设置间隔时间为: 60秒 (1分钟)
2025-08-04 07:34:16 - src.core.message_processor.3011741079488 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250804.log
2025-08-04 07:34:16 - src.core.message_processor.3011741079488 - INFO - 消息处理器日志系统初始化完成
2025-08-04 07:34:16 - src.core.message_processor.3011741079488 - INFO - 设置间隔时间为: 1800秒 (30分钟)
2025-08-04 07:34:16 - src.core.message_processor.3011741079488 - INFO - 设置间隔时间为: 60秒 (1分钟)
2025-08-04 07:41:05 - src.core.message_processor.2016483400768 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250804.log
2025-08-04 07:41:05 - src.core.message_processor.2016483400768 - INFO - 消息处理器日志系统初始化完成
2025-08-04 07:41:05 - src.core.message_processor.2016483400768 - INFO - 设置间隔时间为: 1800秒 (30分钟)
2025-08-04 07:41:05 - src.core.message_processor.2016483400768 - INFO - 设置间隔时间为: 60秒 (1分钟)
2025-08-04 07:44:36 - src.core.message_processor.1850397459984 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250804.log
2025-08-04 07:44:36 - src.core.message_processor.1850397459984 - INFO - 消息处理器日志系统初始化完成
2025-08-04 07:44:36 - src.core.message_processor.1850397459984 - INFO - 设置间隔时间为: 1800秒 (30分钟)
2025-08-04 07:44:36 - src.core.message_processor.1850397459984 - INFO - 设置间隔时间为: 60秒 (1分钟)
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 日志文件已配置: F:\soft\python\wechat\新建文件夹\客服选项选择\logs\wechat_bot_20250804.log
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 消息处理器日志系统初始化完成
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 收到原始消息: {"data": {"content": "查询", "conversation_id": "S:test_user_123", "sender": "S:test_user_123", "sender_name": "测试用户", "content_type": 0, "local_id": "msg_1754291934558", "server_id": "server_1754291934558"}}
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 机器人名字: 测试机器人
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 消息类型: 0
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 消息处理开始 | 发送者: 测试用户(S:test_user_123) | 会话ID: S:test_user_123 | 内容: 查询
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 消息类型: 私聊
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 收到查询消息，开始处理新会话创建: 用户=测试用户(S:test_user_123), 会话ID=S:test_user_123
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 数据库创建查询会话成功: S:test_user_123
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 查询会话创建成功并发送欢迎消息: S:test_user_123
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 收到原始消息: {"data": {"content": "1", "conversation_id": "S:test_user_123", "sender": "S:test_user_123", "sender_name": "测试用户", "content_type": 0, "local_id": "msg_1754291934566", "server_id": "server_1754291934566"}}
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 机器人名字: 测试机器人
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 消息类型: 0
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 消息处理开始 | 发送者: 测试用户(S:test_user_123) | 会话ID: S:test_user_123 | 内容: 1
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 消息类型: 私聊
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 开始处理特殊回复，内容长度: 22
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 普通文本回复
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 发送选项回复给私聊用户: S:test_user_123, 聊天ID: S:test_user_123
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 会话已在数据库中标记为失效: S:test_user_123
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 对话结束，清理会话: S:test_user_123, 记录完成时间: 1754291934.569359
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 收到原始消息: {"data": {"content": "查询", "conversation_id": "S:test_user_123", "sender": "S:test_user_123", "sender_name": "测试用户", "content_type": 0, "local_id": "msg_1754291934575", "server_id": "server_1754291934575"}}
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 机器人名字: 测试机器人
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 消息类型: 0
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 消息处理开始 | 发送者: 测试用户(S:test_user_123) | 会话ID: S:test_user_123 | 内容: 查询
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 消息类型: 私聊
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 收到查询消息，开始处理新会话创建: 用户=测试用户(S:test_user_123), 会话ID=S:test_user_123
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 用户完成时间记录已清除: S:test_user_123
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 数据库创建查询会话成功: S:test_user_123
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 查询会话创建成功并发送欢迎消息: S:test_user_123
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 收到原始消息: {"data": {"content": "2", "conversation_id": "S:test_user_123", "sender": "S:test_user_123", "sender_name": "测试用户", "content_type": 0, "local_id": "msg_1754291934586", "server_id": "server_1754291934586"}}
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 机器人名字: 测试机器人
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 消息类型: 0
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 消息处理开始 | 发送者: 测试用户(S:test_user_123) | 会话ID: S:test_user_123 | 内容: 2
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 消息类型: 私聊
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 开始处理特殊回复，内容长度: 21
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 普通文本回复
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 发送选项回复给私聊用户: S:test_user_123, 聊天ID: S:test_user_123
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 会话已在数据库中标记为失效: S:test_user_123
2025-08-04 15:18:54 - src.core.message_processor.1595874888448 - INFO - 对话结束，清理会话: S:test_user_123, 记录完成时间: 1754291934.589358
2025-08-04 15:20:11 - src.core.message_processor.2346184132448 - INFO - 日志文件已配置: F:\soft\python\wechat\新建文件夹\客服选项选择\logs\wechat_bot_20250804.log
2025-08-04 15:20:11 - src.core.message_processor.2346184132448 - INFO - 消息处理器日志系统初始化完成
2025-08-04 15:20:11 - src.core.message_processor.2346184132448 - INFO - 设置间隔时间为: 1800秒 (30分钟)
2025-08-04 15:20:11 - src.core.message_processor.2346184132448 - INFO - 设置间隔时间为: 60秒 (1分钟)
2025-08-04 15:22:01 - src.core.message_processor.1847486200752 - INFO - 日志文件已配置: F:\soft\python\wechat\新建文件夹\客服选项选择\logs\wechat_bot_20250804.log
2025-08-04 15:22:01 - src.core.message_processor.1847486200752 - INFO - 消息处理器日志系统初始化完成
2025-08-04 15:22:01 - src.core.message_processor.1847486200752 - INFO - 设置间隔时间为: 1800秒 (30分钟)
2025-08-04 15:22:01 - src.core.message_processor.1847486200752 - INFO - 设置间隔时间为: 60秒 (1分钟)
2025-08-04 15:23:49 - src.core.message_processor.1847486200752 - INFO - 收到原始消息: {"data": {"appinfo": "8286069012916145256", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "4347", "receiver": "1688854710715177", "send_time": "1754292226", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1021613"}, "type": 11041}
2025-08-04 15:23:49 - src.core.message_processor.1847486200752 - INFO - 机器人名字: 邾俊勇
2025-08-04 15:23:49 - src.core.message_processor.1847486200752 - INFO - 消息类型: 2
2025-08-04 15:23:49 - src.core.message_processor.1847486200752 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 1
2025-08-04 15:23:49 - src.core.message_processor.1847486200752 - INFO - 消息类型: 私聊
2025-08-04 15:23:49 - src.core.message_processor.1847486200752 - INFO - 用户 7881299718050125 可以创建新会话（无活跃会话且满足间隔条件）
2025-08-04 15:23:49 - src.core.message_processor.1847486200752 - INFO - 数据库创建会话成功: 7881299718050125
2025-08-04 15:23:49 - src.core.message_processor.1847486200752 - INFO - 从文本消息重新创建会话并发送欢迎消息: 7881299718050125, 会话ID: 7881299718050125
2025-08-04 15:23:49 - src.core.message_processor.1847486200752 - INFO - 收到原始消息: {"data": {"appinfo": "CAEQhcDBxAYYqYbwiJKAgAMgAQ==", "at_list": [], "content": "您好！欢迎咨询，请问您需要以下哪项服务？请直接回复对应数字：\n1.查询快递物流信息\n2.投诉/反馈问题\n3.联系人工客服", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 1, "local_id": "4348", "receiver": "7881299718050125", "send_time": "1754292228", "sender": "1688854710715177", "sender_name": "邾俊勇", "server_id": "1021616"}, "type": 11041}
2025-08-04 15:23:49 - src.core.message_processor.1847486200752 - INFO - 机器人名字: 邾俊勇
2025-08-04 15:23:49 - src.core.message_processor.1847486200752 - INFO - 忽略自己发送的消息
2025-08-04 15:24:00 - src.core.message_processor.1847486200752 - INFO - 收到原始消息: {"data": {"appinfo": "5283099754231604746", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "4349", "receiver": "1688854710715177", "send_time": "1754292237", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1021618"}, "type": 11041}
2025-08-04 15:24:00 - src.core.message_processor.1847486200752 - INFO - 机器人名字: 邾俊勇
2025-08-04 15:24:00 - src.core.message_processor.1847486200752 - INFO - 消息类型: 2
2025-08-04 15:24:00 - src.core.message_processor.1847486200752 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 1
2025-08-04 15:24:00 - src.core.message_processor.1847486200752 - INFO - 消息类型: 私聊
2025-08-04 15:24:00 - src.core.message_processor.1847486200752 - INFO - 开始处理特殊回复，内容长度: 25
2025-08-04 15:24:00 - src.core.message_processor.1847486200752 - INFO - 普通文本回复
2025-08-04 15:24:00 - src.core.message_processor.1847486200752 - INFO - 发送选项回复给私聊用户: 7881299718050125, 聊天ID: S:1688854710715177_7881299718050125
2025-08-04 15:24:00 - src.core.message_processor.1847486200752 - INFO - 会话已在数据库中标记为失效: 7881299718050125
2025-08-04 15:24:00 - src.core.message_processor.1847486200752 - INFO - 对话结束，清理会话: 7881299718050125, 记录完成时间: 1754292240.54666
2025-08-04 15:24:00 - src.core.message_processor.1847486200752 - INFO - 收到原始消息: {"data": {"appinfo": "CAEQkMDBxAYYqYbwiJKAgAMgAg==", "at_list": [], "content": "您好！请提供您的快递单号，我们将为您查询物流信息。", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 1, "local_id": "4350", "receiver": "7881299718050125", "send_time": "1754292239", "sender": "1688854710715177", "sender_name": "邾俊勇", "server_id": "1021621"}, "type": 11041}
2025-08-04 15:24:00 - src.core.message_processor.1847486200752 - INFO - 机器人名字: 邾俊勇
2025-08-04 15:24:00 - src.core.message_processor.1847486200752 - INFO - 忽略自己发送的消息
2025-08-04 15:24:42 - src.core.message_processor.1847486200752 - INFO - 收到原始消息: {"data": {"appinfo": "4091445265338442394", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "4351", "receiver": "1688854710715177", "send_time": "1754292280", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1021623"}, "type": 11041}
2025-08-04 15:24:42 - src.core.message_processor.1847486200752 - INFO - 机器人名字: 邾俊勇
2025-08-04 15:24:42 - src.core.message_processor.1847486200752 - INFO - 消息类型: 2
2025-08-04 15:24:42 - src.core.message_processor.1847486200752 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 1
2025-08-04 15:24:42 - src.core.message_processor.1847486200752 - INFO - 消息类型: 私聊
2025-08-04 15:24:42 - src.core.message_processor.1847486200752 - INFO - 用户 7881299718050125 不满足创建新会话条件，忽略文本消息
2025-08-04 15:25:11 - src.core.message_processor.1847486200752 - INFO - 收到原始消息: {"data": {"appinfo": "3146145609906947747", "at_list": [], "content": "查询", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "4352", "receiver": "1688854710715177", "send_time": "1754292309", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1021626"}, "type": 11041}
2025-08-04 15:25:11 - src.core.message_processor.1847486200752 - INFO - 机器人名字: 邾俊勇
2025-08-04 15:25:11 - src.core.message_processor.1847486200752 - INFO - 消息类型: 2
2025-08-04 15:25:11 - src.core.message_processor.1847486200752 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 查询
2025-08-04 15:25:11 - src.core.message_processor.1847486200752 - INFO - 消息类型: 私聊
2025-08-04 15:25:11 - src.core.message_processor.1847486200752 - INFO - 收到查询消息，开始处理新会话创建: 用户=嫒宝粑粑(7881299718050125), 会话ID=S:1688854710715177_7881299718050125
2025-08-04 15:25:11 - src.core.message_processor.1847486200752 - INFO - 用户完成时间记录已清除: 7881299718050125
2025-08-04 15:25:11 - src.core.message_processor.1847486200752 - INFO - 数据库创建查询会话成功: 7881299718050125
2025-08-04 15:25:12 - src.core.message_processor.1847486200752 - INFO - 查询会话创建成功并发送欢迎消息: 7881299718050125
2025-08-04 15:25:12 - src.core.message_processor.1847486200752 - INFO - 收到原始消息: {"data": {"appinfo": "CAEQ2MDBxAYYqYbwiJKAgAMgAw==", "at_list": [], "content": "您好！欢迎咨询，请问您需要以下哪项服务？请直接回复对应数字：\n1.查询快递物流信息\n2.投诉/反馈问题\n3.联系人工客服", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 1, "local_id": "4353", "receiver": "7881299718050125", "send_time": "1754292310", "sender": "1688854710715177", "sender_name": "邾俊勇", "server_id": "1021629"}, "type": 11041}
2025-08-04 15:25:12 - src.core.message_processor.1847486200752 - INFO - 机器人名字: 邾俊勇
2025-08-04 15:25:12 - src.core.message_processor.1847486200752 - INFO - 忽略自己发送的消息
2025-08-04 15:29:09 - src.core.message_processor.1719732157360 - INFO - 日志文件已配置: F:\soft\python\wechat\新建文件夹\客服选项选择\logs\wechat_bot_20250804.log
2025-08-04 15:29:09 - src.core.message_processor.1719732157360 - INFO - 消息处理器日志系统初始化完成
2025-08-04 15:29:09 - src.core.message_processor.1719732157360 - INFO - 设置间隔时间为: 1800秒 (30分钟)
2025-08-04 15:29:09 - src.core.message_processor.1719732157360 - INFO - 设置间隔时间为: 60秒 (1分钟)
