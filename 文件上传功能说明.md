# 文件上传功能使用说明

## 功能概述

在编辑选项回复内容时，现在支持上传txt文件并直接覆盖现有的回复内容。

## 使用步骤

### 1. 打开选项编辑对话框
- 在选项配置界面中，双击任意选项或点击"编辑"按钮
- 选项编辑对话框将会打开

### 2. 使用文件上传功能
- 在回复内容文本框下方，找到"文件上传"区域
- 点击"上传txt文件"按钮

### 3. 选择文件并自动上传
- 在文件选择对话框中选择txt文件
- 支持的文件类型：.txt文本文件
- 选择文件后，内容将自动覆盖现有的回复内容
- 系统会显示上传成功的提示信息

### 4. 保存配置
- 编辑完成后，点击"保存"按钮保存配置

## 功能特点

### 🎯 智能编码识别
- 自动尝试UTF-8编码读取文件
- 如果UTF-8失败，自动尝试GBK编码
- 确保中文内容正确显示

### ⚡ 快速覆盖
- 选择文件后立即覆盖现有内容
- 无需预览，直接替换
- 一键完成内容更新

### 🔄 简化操作
- 点击按钮直接选择文件
- 自动读取并覆盖内容
- 操作简单快捷

### ⚠️ 错误处理
- 文件读取失败时显示错误信息
- 空文件或无效文件的友好提示
- 操作取消时不影响现有内容

## 使用场景

### 📝 标准回复模板
- 快速上传预先准备的客服回复模板
- 统一的服务条款或说明文档
- 常见问题解答内容

### 🎨 格式化内容
- 包含特殊字符或表情的回复内容
- 多行格式化的服务介绍
- 带有符号装饰的专业回复

### 📊 批量内容管理
- 从外部文档管理系统快速导入内容
- 团队协作编辑的回复内容
- 版本控制的标准回复

## 注意事项

1. **文件格式**：目前仅支持.txt文本文件
2. **文件大小**：建议文件大小不超过1MB
3. **编码格式**：推荐使用UTF-8编码保存txt文件
4. **内容检查**：上传前请确认文件内容的准确性
5. **备份建议**：重要内容建议保留原始文件备份

## 故障排除

### 文件无法读取
- 检查文件是否为txt格式
- 确认文件没有被其他程序占用
- 尝试用记事本重新保存文件

### 中文显示乱码
- 将文件另存为UTF-8编码格式
- 或者使用GBK编码保存

### 内容覆盖问题
- 上传文件会完全覆盖现有内容
- 如需保留原内容，请先备份
- 确认文件内容正确后再上传

---

*如有其他问题，请联系技术支持。*
