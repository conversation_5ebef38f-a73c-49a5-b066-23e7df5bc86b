# -*- coding: utf-8 -*-
"""
测试间隔时间功能 - 验证会话重新创建机制
"""

import sys
import os
import time

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_interval_functionality():
    """测试间隔时间功能"""
    print("=== 测试间隔时间功能 ===")
    
    try:
        from src.core.message_processor import MessageProcessor
        from src.core.wechat_handler import WeChatHandler
        from src.utils.mock_ntwork import WeWork
        
        # 创建消息处理器实例
        wechat_handler = WeChatHandler()
        processor = MessageProcessor(wechat_handler, None)
        
        # 设置私聊配置
        processor.group_option_configs = {
            'private_chat': {
                'auto_reply': '请选择服务类型：\n1.咨询\n2.投诉\n3.建议',
                'option_replies': {
                    '1': '感谢您的咨询！',
                    '2': '我们会认真处理您的投诉。',
                    '3': '感谢您的建议！'
                },
                'tree_options': {
                    '1': {
                        'text': '咨询',
                        'type': 'final',
                        'reply': '感谢您的咨询！'
                    },
                    '2': {
                        'text': '投诉',
                        'type': 'final',
                        'reply': '我们会认真处理您的投诉。'
                    },
                    '3': {
                        'text': '建议',
                        'type': 'final',
                        'reply': '感谢您的建议！'
                    }
                }
            }
        }
        
        # 设置较短的间隔时间用于测试（5秒）
        processor.set_interval_seconds(5)
        
        # 模拟微信实例
        mock_wechat = WeWork()
        
        user_id = "test_user_123"
        room_id = "S:1234567890"
        
        print("\n--- 步骤1: 系统消息创建会话 ---")
        
        # 模拟系统消息
        sys_message = {
            "data": {
                "content": "你已添加了测试用户，现在可以开始聊天了。",
                "conversation_id": room_id,
                "sender": user_id
            }
        }
        
        processor.on_sys_message(mock_wechat, sys_message)
        
        if user_id in processor.user_sessions:
            print("✅ 系统消息成功创建会话")
        else:
            print("❌ 系统消息创建会话失败")
            return
        
        print("\n--- 步骤2: 用户完成选择 ---")
        
        # 模拟用户选择
        text_message = {
            "data": {
                "room_wxid": room_id,
                "from_wxid": user_id,
                "from_name": "测试用户",
                "msg": "1",
                "content_type": 0
            }
        }
        
        processor.on_recv_message(mock_wechat, text_message)
        
        # 检查会话是否结束并记录完成时间
        if user_id not in processor.user_sessions and user_id in processor.user_last_completion:
            print("✅ 用户完成选择，会话结束，完成时间已记录")
            completion_time = processor.user_last_completion[user_id]
            print(f"   完成时间: {completion_time}")
        else:
            print("❌ 选择完成处理失败")
            return
        
        print("\n--- 步骤3: 间隔时间内发送文本消息 ---")
        
        # 立即发送文本消息（间隔时间内）
        processor.on_recv_message(mock_wechat, text_message)
        
        if user_id not in processor.user_sessions:
            print("✅ 间隔时间内的文本消息被正确忽略")
        else:
            print("❌ 间隔时间内的文本消息错误地创建了会话")
        
        print("\n--- 步骤4: 等待间隔时间后发送文本消息 ---")
        
        print("等待6秒（超过5秒间隔时间）...")
        time.sleep(6)
        
        # 再次发送文本消息
        processor.on_recv_message(mock_wechat, text_message)
        
        if user_id in processor.user_sessions:
            session = processor.user_sessions[user_id]
            if session.get('source') == 'text_message_recreate':
                print("✅ 超过间隔时间后文本消息成功重新创建会话")
                print(f"   会话来源: {session['source']}")
            else:
                print("❌ 会话来源标记不正确")
        else:
            print("❌ 超过间隔时间后文本消息未能重新创建会话")
        
        print("\n--- 步骤5: 测试间隔时间检查逻辑 ---")
        
        current_time = time.time()
        
        # 测试可以重新创建会话
        can_recreate = processor._can_recreate_session(user_id, current_time)
        print(f"当前是否可以重新创建会话: {can_recreate}")
        
        # 测试新用户（没有完成记录）
        new_user = "new_user_456"
        can_recreate_new = processor._can_recreate_session(new_user, current_time)
        print(f"新用户是否可以重新创建会话: {can_recreate_new}")
        
        if not can_recreate_new:
            print("✅ 新用户正确地不能重新创建会话")
        else:
            print("❌ 新用户错误地可以重新创建会话")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_gui_integration():
    """测试GUI集成"""
    print("\n=== 测试GUI集成 ===")
    
    try:
        # 检查GUI代码是否包含间隔时间设置
        with open('src/gui/main_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            ('interval_var', '间隔时间变量'),
            ('on_interval_changed', '间隔时间变化处理方法'),
            ('重新创建会话间隔', '间隔时间标签'),
            ('set_interval_seconds', '设置间隔时间调用'),
        ]
        
        for check_str, description in checks:
            if check_str in content:
                print(f"✅ {description}: 存在")
            else:
                print(f"❌ {description}: 不存在")
                
    except Exception as e:
        print(f"❌ GUI集成测试失败: {e}")

def main():
    """主测试函数"""
    print("微信私聊机器人 - 间隔时间功能测试")
    print("=" * 60)
    
    test_interval_functionality()
    test_gui_integration()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("\n新功能总结：")
    print("1. ✅ 添加了间隔时间管理功能")
    print("2. ✅ 系统消息创建会话")
    print("3. ✅ 文本消息完成选项选择")
    print("4. ✅ 选择完成后记录完成时间")
    print("5. ✅ 间隔时间内忽略文本消息")
    print("6. ✅ 超过间隔时间后重新创建会话")
    print("7. ✅ GUI界面添加间隔时间设置")
    print("8. ✅ 循环执行机制完整")

if __name__ == "__main__":
    main()
