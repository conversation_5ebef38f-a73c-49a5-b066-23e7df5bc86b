# -*- coding: utf-8 -*-
"""
私聊机器人测试脚本
"""

import sys
import os
import json

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_message_filtering():
    """测试消息过滤逻辑"""
    print("=== 测试消息过滤逻辑 ===")
    
    # 模拟不同类型的消息ID
    test_cases = [
        ("S:1234567890", True, "私聊消息"),
        ("R:1234567890", False, "群聊消息"),
        ("FILEASSIST", False, "文件助手"),
        ("unknown_format", False, "未知格式"),
    ]
    
    for room_wxid, should_process, description in test_cases:
        is_private = room_wxid and room_wxid.startswith('S:')
        is_group = room_wxid and room_wxid.startswith('R:')
        
        # 根据新的逻辑判断是否应该处理
        should_process_actual = is_private and not is_group and room_wxid != "FILEASSIST"
        
        status = "✅ 通过" if should_process_actual == should_process else "❌ 失败"
        print(f"{status} {description}: {room_wxid} -> 处理: {should_process_actual}")

def test_config_loading():
    """测试配置加载"""
    print("\n=== 测试配置加载 ===")
    
    try:
        from src.config.config_manager import ConfigManager
        
        # 测试加载私聊配置
        config_manager = ConfigManager("config-private.json")
        config = config_manager.load_config()
        
        print("✅ 配置加载成功")
        print(f"配置键: {list(config.keys())}")
        
        # 检查私聊配置
        group_configs = config_manager.get_group_option_configs()
        if 'private_chat' in group_configs:
            print("✅ 找到私聊配置")
            private_config = group_configs['private_chat']
            print(f"自动回复长度: {len(private_config.get('auto_reply', ''))}")
            print(f"选项数量: {len(private_config.get('option_replies', {}))}")
        else:
            print("❌ 未找到私聊配置")
            
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")

def test_session_id_generation():
    """测试会话ID生成逻辑"""
    print("\n=== 测试会话ID生成 ===")
    
    test_cases = [
        ("S:1234567890", "user123", False, True, "user123"),
        ("R:1234567890", "user123", True, False, "R:1234567890_user123"),  # 群聊（已不处理）
    ]
    
    for room_wxid, from_wxid, is_group, is_private, expected_session_id in test_cases:
        if is_private:  # 只测试私聊
            session_id = from_wxid  # 新的私聊会话ID生成逻辑
            status = "✅ 通过" if session_id == expected_session_id else "❌ 失败"
            print(f"{status} 私聊会话ID: {room_wxid} + {from_wxid} -> {session_id}")

def main():
    """主测试函数"""
    print("微信私聊选项选择机器人 - 测试脚本")
    print("=" * 50)
    
    test_message_filtering()
    test_config_loading()
    test_session_id_generation()
    
    print("\n" + "=" * 50)
    print("测试完成！")

if __name__ == "__main__":
    main()
