#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试间隔时间修复的脚本
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 模拟 ntwork 模块
class MockNtwork:
    class WeWork:
        def send_text(self, conversation_id, text):
            print(f"[MOCK] 发送消息到 {conversation_id}: {text}")

sys.modules['ntwork'] = MockNtwork()

from src.core.wechat_handler import WeChatHandler
from src.core.message_processor import MessageProcessor
from src.config.config_manager import ConfigManager


def test_interval_fix():
    """测试间隔时间修复"""
    print("=== 测试间隔时间修复 ===")
    
    # 1. 创建配置管理器并加载配置
    print("\n--- 步骤1: 加载配置 ---")
    config_manager = ConfigManager()
    config = config_manager.load_config()
    group_option_configs = config_manager.get_group_option_configs()
    
    private_config = group_option_configs.get('private_chat', {})
    interval_minutes = private_config.get('interval_minutes', 30)
    
    print(f"配置文件中的间隔时间: {interval_minutes}分钟")
    
    # 2. 创建微信处理器和消息处理器
    print("\n--- 步骤2: 创建消息处理器 ---")
    
    class MockWeChatHandler:
        def __init__(self):
            self.user_id = "test_bot"
            self.name = "测试机器人"
            self.exit_flag = False
    
    wechat_handler = MockWeChatHandler()
    message_processor = MessageProcessor(wechat_handler)
    
    print(f"消息处理器默认间隔时间: {message_processor.interval_seconds}秒")
    
    # 3. 应用配置（模拟主窗口的逻辑）
    print("\n--- 步骤3: 应用配置 ---")
    
    # 更新配置
    message_processor.update_config(group_option_configs, [])
    
    # 应用间隔时间设置
    interval_seconds = interval_minutes * 60
    message_processor.set_interval_seconds(interval_seconds)
    
    print(f"应用配置后的间隔时间: {message_processor.interval_seconds}秒")
    
    # 4. 测试数据库检查
    print("\n--- 步骤4: 测试数据库检查 ---")
    
    test_user_id = "7881299718050125"
    
    if message_processor.session_db:
        can_create = message_processor.session_db.can_create_new_session(test_user_id, message_processor.interval_seconds)
        print(f"数据库检查结果: {can_create}")
    else:
        print("数据库管理器未初始化")
    
    # 5. 模拟消息处理
    print("\n--- 步骤5: 模拟消息处理 ---")
    
    mock_wechat_instance = MockNtwork.WeWork()
    
    # 模拟消息数据
    message_data = {
        "data": {
            "content": "1",
            "conversation_id": f"S:1688854710715177_{test_user_id}",
            "sender": test_user_id,
            "sender_name": "测试用户",
            "content_type": 0,
            "local_id": f"msg_{int(time.time() * 1000)}",
            "server_id": f"server_{int(time.time() * 1000)}"
        }
    }
    
    print(f"模拟用户 {test_user_id} 发送消息: {message_data['data']['content']}")
    
    # 处理消息
    message_processor.on_recv_message(mock_wechat_instance, message_data)
    
    print("\n=== 测试完成 ===")


if __name__ == "__main__":
    test_interval_fix()
