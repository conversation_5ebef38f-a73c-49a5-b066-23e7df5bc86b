# -*- coding: utf-8 -*-
"""
群管理选项卡 - 管理监控群列表
"""

import time
import logging
import tkinter as tk
from tkinter import messagebox
from typing import List, Dict, Any, Optional

# 尝试导入真实的ttkbootstrap，如果失败则使用模拟版本
try:
    import ttkbootstrap as ttk
except ImportError:
    from ..utils import mock_ttkbootstrap as ttk


class RoomManagementTab:
    """
    联系人管理选项卡 - 负责私聊联系人的管理和显示（保留用于兼容性）
    """
    
    def __init__(self, parent, main_window):
        """
        初始化联系人管理选项卡

        Args:
            parent: 父容器
            main_window: 主窗口实例
        """
        self.parent = parent
        self.main_window = main_window
        self.logger = logging.getLogger(__name__)
        
        # 创建主框架
        self.frame = ttk.Frame(parent, padding="10")
        
        # 群列表相关变量
        self.room_current_page = 1
        self.room_page_size = 500
        self.room_total_pages = 1
        self.room_all_rooms = []
        self.room_search_keyword = ""
        
        # 创建界面
        self.create_widgets()
        
    def create_widgets(self):
        """创建群管理界面组件"""
        # 群列表管理区域
        room_frame = ttk.Frame(self.frame)
        room_frame.pack(fill=tk.BOTH, expand=True)

        # 左侧群列表（保留用于兼容性，私聊机器人不需要群管理）
        left_frame = ttk.LabelFrame(room_frame, text="所有群列表（私聊机器人无需配置）", padding="10", bootstyle="primary")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 顶部控制区域
        control_frame = ttk.Frame(left_frame)
        control_frame.pack(fill=tk.X, pady=(0, 5))
        
        # 搜索框
        search_frame = ttk.Frame(control_frame)
        search_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Label(search_frame, text="搜索:").pack(side=tk.LEFT, padx=5)
        self.room_search_entry = ttk.Entry(search_frame)
        self.room_search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        self.room_search_button = ttk.Button(
            search_frame, 
            text="搜索", 
            command=self.search_rooms, 
            bootstyle="primary-outline"
        )
        self.room_search_button.pack(side=tk.LEFT, padx=5)
        
        # 刷新按钮
        refresh_btn = ttk.Button(
            control_frame, 
            text="刷新群列表", 
            command=self.refresh_room_list,
            bootstyle="primary-outline"
        )
        refresh_btn.pack(side=tk.RIGHT, padx=5)
        
        # 分页控件
        pagination_frame = ttk.Frame(left_frame)
        pagination_frame.pack(fill=tk.X, pady=5)
        
        self.room_prev_page_btn = ttk.Button(
            pagination_frame, 
            text="上一页", 
            command=self.prev_room_page, 
            bootstyle="primary-outline"
        )
        self.room_prev_page_btn.pack(side=tk.LEFT, padx=5)
        
        self.room_page_label = ttk.Label(pagination_frame, text="第 1 页")
        self.room_page_label.pack(side=tk.LEFT, padx=5)
        
        self.room_next_page_btn = ttk.Button(
            pagination_frame, 
            text="下一页", 
            command=self.next_room_page, 
            bootstyle="primary-outline"
        )
        self.room_next_page_btn.pack(side=tk.LEFT, padx=5)
        
        # 创建滚动条和树形视图的框架
        tree_frame = ttk.Frame(left_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        self.all_rooms_tree = ttk.Treeview(
            tree_frame, 
            columns=('room_id',),
            height=10,
            bootstyle="primary"
        )
        scrollbar = ttk.Scrollbar(
            tree_frame, 
            orient="vertical", 
            command=self.all_rooms_tree.yview, 
            bootstyle="primary-round"
        )
        self.all_rooms_tree.configure(yscrollcommand=scrollbar.set)
        
        self.all_rooms_tree['show'] = 'tree headings'
        self.all_rooms_tree.heading('#0', text='群名称')
        self.all_rooms_tree.heading('room_id', text='群ID')
        self.all_rooms_tree.column('#0', width=200, stretch=True)
        self.all_rooms_tree.column('room_id', width=150, stretch=True)
        
        self.all_rooms_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 中间操作按钮
        btn_frame = ttk.Frame(room_frame)
        btn_frame.pack(side=tk.LEFT, padx=10, fill=tk.Y)
        
        ttk.Button(
            btn_frame, 
            text="添加 >>", 
            command=self.add_selected_room, 
            bootstyle="primary-outline"
        ).pack(pady=5)
        
        ttk.Button(
            btn_frame, 
            text="<< 移除", 
            command=self.remove_selected_room, 
            bootstyle="danger-outline"
        ).pack(pady=5)
        
        ttk.Button(
            btn_frame, 
            text="全选添加 >>", 
            command=self.add_all_rooms, 
            bootstyle="success-outline"
        ).pack(pady=5)

        # 右侧监控群列表（保留用于兼容性，私聊机器人无需配置）
        right_frame = ttk.LabelFrame(room_frame, text="监控群列表（私聊机器人无需配置）", padding="10", bootstyle="primary")
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # 监控群树形视图
        monitor_frame = ttk.Frame(right_frame)
        monitor_frame.pack(fill=tk.BOTH, expand=True)
        
        self.monitored_rooms_tree = ttk.Treeview(
            monitor_frame,
            columns=('room_id',),
            height=10,
            bootstyle="primary"
        )
        monitor_scrollbar = ttk.Scrollbar(
            monitor_frame, 
            orient="vertical", 
            command=self.monitored_rooms_tree.yview, 
            bootstyle="primary-round"
        )
        self.monitored_rooms_tree.configure(yscrollcommand=monitor_scrollbar.set)
        
        self.monitored_rooms_tree['show'] = 'tree headings'
        self.monitored_rooms_tree.heading('#0', text='群名称')
        self.monitored_rooms_tree.heading('room_id', text='群ID')
        self.monitored_rooms_tree.column('#0', width=200, stretch=True)
        self.monitored_rooms_tree.column('room_id', width=150, stretch=True)
        
        self.monitored_rooms_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        monitor_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def add_selected_room(self):
        """添加选中的群到监控列表"""
        selected = self.all_rooms_tree.selection()
        for item in selected:
            name = self.all_rooms_tree.item(item, 'text')
            room_id = self.all_rooms_tree.item(item, 'values')[0]
            
            # 检查是否已经存在
            exists = False
            for existing in self.monitored_rooms_tree.get_children():
                if self.monitored_rooms_tree.item(existing)['values'][0] == room_id:
                    exists = True
                    break
            
            if not exists:
                # 使用新的ID以避免冲突
                new_id = f"monitored_{room_id}"
                self.monitored_rooms_tree.insert('', 'end', iid=new_id,
                    text=name,  # 显示群名称
                    values=(room_id,))  # 保存群ID
                self.logger.info(f"添加监控群：{name}")

    def remove_selected_room(self):
        """从监控列表中移除选中的群"""
        selected = self.monitored_rooms_tree.selection()
        for item in selected:
            self.monitored_rooms_tree.delete(item)

    def add_all_rooms(self):
        """添加所有群到监控列表"""
        added_count = 0
        for room in self.room_all_rooms:
            try:
                nickname = room.get('nickname', '未知群名')
                conversation_id = room.get('conversation_id', '')
                if not conversation_id:
                    continue
                    
                # 检查是否已经存在
                exists = False
                for existing in self.monitored_rooms_tree.get_children():
                    if self.monitored_rooms_tree.item(existing)['values'][0] == conversation_id:
                        exists = True
                        break
                
                if not exists:
                    # 使用新的ID以避免冲突
                    new_id = f"monitored_{conversation_id}"
                    self.monitored_rooms_tree.insert('', 'end', iid=new_id,
                        text=nickname,  # 显示群名称
                        values=(conversation_id,))  # 保存群ID
                    added_count += 1
            except Exception as e:
                self.logger.error(f"处理群信息时出错: {e}, 群数据: {room}")
                continue
        
        self.logger.info(f"已添加 {added_count} 个群到监控列表")
        messagebox.showinfo("成功", f"已添加 {added_count} 个群到监控列表")

    def search_rooms(self):
        """搜索群列表"""
        self.room_search_keyword = self.room_search_entry.get().strip()
        self.room_current_page = 1
        self.update_rooms_display()

    def prev_room_page(self):
        """上一页群列表"""
        if self.room_current_page > 1:
            self.room_current_page -= 1
            self.update_rooms_display()

    def next_room_page(self):
        """下一页群列表"""
        if self.room_current_page < self.room_total_pages:
            self.room_current_page += 1
            self.update_rooms_display()

    def update_rooms_display(self):
        """更新群列表显示"""
        # 清空当前树形视图中的所有项
        for item in self.all_rooms_tree.get_children():
            self.all_rooms_tree.delete(item)
        
        # 计算分页显示
        filtered_rooms = self.room_all_rooms
        if self.room_search_keyword:
            filtered_rooms = [
                room for room in self.room_all_rooms
                if self.room_search_keyword.lower() in room.get('nickname', '').lower()
            ]
        
        # 计算总页数
        self.room_total_pages = max(1, (len(filtered_rooms) + self.room_page_size - 1) // self.room_page_size)
        
        # 确保当前页在有效范围内
        if self.room_current_page > self.room_total_pages:
            self.room_current_page = self.room_total_pages
        
        # 更新页码显示
        self.room_page_label.config(text=f"第 {self.room_current_page}/{self.room_total_pages} 页")
        
        # 计算当前页应显示的群列表
        start_idx = (self.room_current_page - 1) * self.room_page_size
        end_idx = min(start_idx + self.room_page_size, len(filtered_rooms))
        current_page_rooms = filtered_rooms[start_idx:end_idx]
        
        # 显示当前页的群
        for room in current_page_rooms:
            try:
                nickname = room.get('nickname', '未知群名')
                conversation_id = room.get('conversation_id', '')
                if not conversation_id:
                    continue
                
                self.all_rooms_tree.insert('', 'end', 
                    text=nickname,
                    values=(conversation_id,),
                    tags=('room',))
            except Exception as e:
                self.logger.error(f"处理群信息时出错: {e}, 群数据: {room}")
                continue
        
        self.logger.info(f"当前显示第 {self.room_current_page}/{self.room_total_pages} 页，共 {len(filtered_rooms)} 个群（显示 {len(current_page_rooms)} 个）")

    def refresh_room_list(self):
        """刷新群列表"""
        if not self.main_window.wechat_handler or not self.main_window.wechat_handler.is_connected:
            self.logger.error("微信未初始化，请重启程序")
            return

        try:
            self.logger.info("正在刷新群列表...")

            # 添加重试机制
            max_retries = 3
            self.room_all_rooms = []
            total_rooms = 0
            current_page = 1

            while True:
                success = False
                for i in range(max_retries):
                    try:
                        # 带分页参数获取群列表
                        rooms = self.main_window.wechat_handler.get_rooms(
                            page_num=current_page,
                            page_size=self.room_page_size
                        )
                        if rooms and 'room_list' in rooms and rooms['room_list']:
                            room_count = len(rooms['room_list'])
                            self.logger.info(f"成功获取第 {current_page} 页群列表，有 {room_count} 个群")
                            self.room_all_rooms.extend(rooms['room_list'])
                            total_rooms += room_count
                            success = True
                            break
                        else:
                            # 如果返回空列表，可能已经获取完所有群
                            if current_page > 1:
                                self.logger.info(f"第 {current_page} 页没有更多群，获取完成")
                                success = True
                                break
                            else:
                                self.logger.warning(f"第 {current_page} 页获取群列表为空，尝试重试... ({i + 1}/{max_retries})")
                                time.sleep(2)
                    except Exception as e:
                        self.logger.warning(f"获取群列表第 {current_page} 页失败: {e}，尝试重试... ({i + 1}/{max_retries})")
                        time.sleep(2)

                # 如果当前页获取失败且是第一页，则无法继续
                if not success and current_page == 1:
                    self.logger.error("无法获取群列表，请检查企业微信状态")
                    return

                # 如果当前页获取的群数量少于页大小，说明已经没有更多群了
                if success and (len(rooms.get('room_list', [])) < self.room_page_size):
                    self.logger.info(f"已获取所有群，总共 {total_rooms} 个群")
                    break

                # 继续获取下一页
                current_page += 1

            # 显示第一页
            self.room_current_page = 1
            self.room_search_keyword = ""
            if hasattr(self, 'room_search_entry'):
                self.room_search_entry.delete(0, tk.END)
            self.update_rooms_display()

        except Exception as e:
            self.logger.error(f"刷新群列表失败: {str(e)}")
            self.logger.error("错误详情: ", exc_info=True)

    def load_monitored_rooms(self, monitored_rooms: List[Dict[str, str]]):
        """
        加载监控群列表

        Args:
            monitored_rooms: 监控群列表
        """
        for room in monitored_rooms:
            new_id = f"monitored_{room['id']}"
            self.monitored_rooms_tree.insert('', 'end', iid=new_id,
                text=room['name'],
                values=(room['id'],))

    def get_monitored_rooms(self) -> List[Dict[str, str]]:
        """
        获取监控群列表

        Returns:
            监控群列表
        """
        return [
            {
                'name': self.monitored_rooms_tree.item(item)['text'],
                'id': self.monitored_rooms_tree.item(item)['values'][0]
            }
            for item in self.monitored_rooms_tree.get_children()
        ]
