# -*- coding: utf-8 -*-
"""
选项配置选项卡 - 管理自动回复选项配置
"""

import logging
import tkinter as tk
from tkinter import messagebox, filedialog
from typing import Dict, Any, List, Optional

# 尝试导入真实的ttkbootstrap，如果失败则使用模拟版本
try:
    import ttkbootstrap as ttk
except ImportError:
    from ..utils import mock_ttkbootstrap as ttk


class OptionConfigurationTab:
    """
    选项配置选项卡 - 负责自动回复选项的配置管理
    """
    
    def __init__(self, parent, main_window):
        """
        初始化选项配置选项卡
        
        Args:
            parent: 父容器
            main_window: 主窗口实例
        """
        self.parent = parent
        self.main_window = main_window
        self.logger = logging.getLogger(__name__)
        
        # 创建主框架
        self.frame = ttk.Frame(parent, padding="10")
        
        # 初始化树状选项数据
        self.tree_option_data = {}
        
        # 创建界面
        self.create_widgets()
        
        # 创建默认的树状选项
        self.create_default_tree_options()

        # 加载私聊配置
        self.main_window.root.after(100, self.load_private_config)
        
    def create_widgets(self):
        """创建选项配置界面组件"""
        # 私聊机器人说明
        info_frame = ttk.LabelFrame(self.frame, text="私聊机器人配置", padding="10", bootstyle="primary")
        info_frame.pack(fill=tk.X, padx=5, pady=(0, 5))

        # 第一行：说明文字
        info_row1 = ttk.Frame(info_frame)
        info_row1.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(
            info_row1,
            text="此配置将应用于所有私聊消息。配置会自动保存。",
            bootstyle="info",
            font=("", 10)
        ).pack(side=tk.LEFT)

        # 第二行：间隔时间设置
        interval_row = ttk.Frame(info_frame)
        interval_row.pack(fill=tk.X)

        ttk.Label(
            interval_row,
            text="重新创建会话间隔时间:",
            font=("", 10)
        ).pack(side=tk.LEFT, padx=(0, 5))

        self.interval_var = tk.StringVar(value="30")  # 默认30分钟
        self.interval_spinbox = ttk.Spinbox(
            interval_row,
            from_=1,
            to=1440,  # 最大24小时
            width=8,
            textvariable=self.interval_var,
            command=self.on_interval_changed
        )
        self.interval_spinbox.pack(side=tk.LEFT, padx=(0, 5))
        self.interval_spinbox.bind('<KeyRelease>', self.on_interval_changed)

        ttk.Label(interval_row, text="分钟").pack(side=tk.LEFT, padx=(0, 10))

        ttk.Label(
            interval_row,
            text="(用户完成选择后，需等待此时间才能重新开始对话)",
            bootstyle="secondary",
            font=("", 9)
        ).pack(side=tk.LEFT)

        # 自动回复内容配置
        auto_reply_frame = ttk.LabelFrame(self.frame, text="自动回复内容", padding="10", bootstyle="info")
        auto_reply_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        ttk.Label(auto_reply_frame, text="自动回复内容（多行文本）:", bootstyle="inverse-info").pack(anchor=tk.W, pady=(0, 5))

        # 创建滚动文本框
        auto_reply_text_frame = ttk.Frame(auto_reply_frame)
        auto_reply_text_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.auto_reply_text = tk.Text(auto_reply_text_frame, height=8, wrap=tk.WORD)
        auto_reply_scrollbar = ttk.Scrollbar(auto_reply_text_frame, orient="vertical", command=self.auto_reply_text.yview)
        self.auto_reply_text.configure(yscrollcommand=auto_reply_scrollbar.set)

        # 绑定文本修改事件，实现自动保存
        self.auto_reply_text.bind('<KeyRelease>', self.on_text_changed)
        self.auto_reply_text.bind('<Button-1>', self.on_text_changed)

        self.auto_reply_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        auto_reply_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 设置默认内容
        default_auto_reply = """请问您需要咨询以下哪项业务？请直接回复对应数字：
1.查询快递物流信息
2.投诉/反馈问题（丢件/破损/延误等）
3.联系人工客服
4.网点/快递员联系方式查询
5.修改收件地址或时间"""
        self.auto_reply_text.insert('1.0', default_auto_reply)

        # 选项回复配置（树状结构）
        options_frame = ttk.LabelFrame(self.frame, text="选项回复配置（树状结构）", padding="10", bootstyle="success")
        options_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 添加/删除按钮区域
        button_frame = ttk.Frame(options_frame)
        button_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(button_frame, text="添加根选项", command=self.add_root_option, bootstyle="success-outline").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="添加子选项", command=self.add_sub_option, bootstyle="info-outline").pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="删除选项", command=self.remove_selected_option, bootstyle="danger-outline").pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="展开全部", command=self.expand_all_options, bootstyle="secondary-outline").pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="折叠全部", command=self.collapse_all_options, bootstyle="secondary-outline").pack(side=tk.LEFT, padx=5)

        # 创建树状视图
        tree_frame = ttk.Frame(options_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True)

        # 创建Treeview控件
        self.options_tree = ttk.Treeview(
            tree_frame,
            columns=('reply_content', 'option_type'),
            height=15,
            bootstyle="success"
        )

        # 配置列
        self.options_tree.heading('#0', text='选项路径')
        self.options_tree.heading('reply_content', text='回复内容')
        self.options_tree.heading('option_type', text='类型')

        self.options_tree.column('#0', width=200, stretch=True)
        self.options_tree.column('reply_content', width=400, stretch=True)
        self.options_tree.column('option_type', width=100, stretch=False)

        # 添加滚动条
        tree_scrollbar_v = ttk.Scrollbar(tree_frame, orient="vertical", command=self.options_tree.yview)
        tree_scrollbar_h = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.options_tree.xview)
        self.options_tree.configure(yscrollcommand=tree_scrollbar_v.set, xscrollcommand=tree_scrollbar_h.set)

        # 布局
        self.options_tree.pack(side="left", fill="both", expand=True)
        tree_scrollbar_v.pack(side="right", fill="y")
        tree_scrollbar_h.pack(side="bottom", fill="x")

        # 绑定双击事件编辑
        self.options_tree.bind("<Double-1>", self.edit_option_item)

        # 私聊机器人配置会自动保存，无需手动保存按钮

        # 初始化自动保存定时器
        self.auto_save_timer = None

        # 设置初始间隔时间
        self.main_window.root.after(100, self.set_initial_interval)

    def create_default_tree_options(self):
        """创建默认的树状选项"""
        default_tree_data = {
            '1': {
                'text': '查询快递物流信息',
                'reply': '您好！请提供您的快递单号，我们将为您查询物流信息。',
                'type': 'final',
                'children': {}
            },
            '2': {
                'text': '投诉/反馈问题',
                'reply': '很抱歉给您带来不便，请选择具体问题类型：',
                'type': 'menu',
                'children': {
                    '2.1': {
                        'text': '丢件问题',
                        'reply': '请提供您的快递单号和详细情况，我们将立即为您处理丢件问题。',
                        'type': 'final',
                        'children': {}
                    },
                    '2.2': {
                        'text': '破损问题',
                        'reply': '请拍照上传破损情况并提供快递单号，我们将为您申请赔偿。',
                        'type': 'final',
                        'children': {}
                    },
                    '2.3': {
                        'text': '延误问题',
                        'reply': '请提供快递单号，我们将查询延误原因并为您跟进处理。',
                        'type': 'final',
                        'children': {}
                    }
                }
            },
            '3': {
                'text': '联系人工客服',
                'reply': '正在为您转接人工客服，请稍候...',
                'type': 'final',
                'children': {}
            }
        }

        self.tree_option_data = default_tree_data
        self.refresh_options_tree()

    def refresh_options_tree(self):
        """刷新选项树显示"""
        # 清空现有项目
        for item in self.options_tree.get_children():
            self.options_tree.delete(item)

        # 递归添加树项目
        def add_tree_items(parent_id, data, parent_item=''):
            for key, value in data.items():
                item_text = f"{key}. {value['text']}"
                item_id = self.options_tree.insert(
                    parent_item,
                    'end',
                    text=item_text,
                    values=(value['reply'], value['type']),
                    iid=key if not parent_item else f"{parent_item}_{key}"
                )

                # 递归添加子项目
                if value['children']:
                    add_tree_items(key, value['children'], item_id)

        add_tree_items('', self.tree_option_data)

        # 展开所有项目
        self.expand_all_options()

    def add_root_option(self):
        """添加根选项"""
        # 找到下一个可用的根选项编号
        existing_roots = [int(k) for k in self.tree_option_data.keys()]
        next_num = max(existing_roots) + 1 if existing_roots else 1

        # 创建新的根选项
        new_key = str(next_num)
        self.tree_option_data[new_key] = {
            'text': f'新选项{next_num}',
            'reply': f'这是选项{next_num}的回复内容',
            'type': 'final',
            'children': {}
        }

        self.refresh_options_tree()
        self.logger.info(f"添加了根选项{next_num}")
        # 触发自动保存
        self.on_text_changed()

    def add_sub_option(self):
        """添加子选项"""
        selected = self.options_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个父选项！")
            return

        parent_item = selected[0]

        # 获取父选项的数据路径
        parent_path = self.get_option_path(parent_item)
        parent_data = self.get_option_data_by_path(parent_path)

        if not parent_data:
            messagebox.showerror("错误", "无法找到父选项数据！")
            return

        # 修改父选项类型为菜单类型
        parent_data['type'] = 'menu'

        # 找到下一个可用的子选项编号
        existing_children = list(parent_data['children'].keys())
        if existing_children:
            # 提取数字部分并找到最大值
            child_nums = []
            for child_key in existing_children:
                if '.' in child_key:
                    try:
                        child_num = int(child_key.split('.')[-1])
                        child_nums.append(child_num)
                    except ValueError:
                        pass
            next_child_num = max(child_nums) + 1 if child_nums else 1
        else:
            next_child_num = 1

        # 创建新的子选项键
        parent_key = parent_path[-1] if parent_path else '1'
        new_child_key = f"{parent_key}.{next_child_num}"

        # 添加子选项
        parent_data['children'][new_child_key] = {
            'text': f'子选项{next_child_num}',
            'reply': f'这是{parent_key}的子选项{next_child_num}的回复内容',
            'type': 'final',
            'children': {}
        }

        self.refresh_options_tree()
        self.logger.info(f"为选项{parent_key}添加了子选项{next_child_num}")
        # 触发自动保存
        self.on_text_changed()

    def remove_selected_option(self):
        """删除选中的选项"""
        selected = self.options_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择要删除的选项！")
            return

        item = selected[0]
        option_path = self.get_option_path(item)

        if len(option_path) == 1:
            # 删除根选项
            if len(self.tree_option_data) <= 1:
                messagebox.showwarning("警告", "至少需要保留一个根选项！")
                return
            del self.tree_option_data[option_path[0]]
        else:
            # 删除子选项
            parent_data = self.get_option_data_by_path(option_path[:-1])
            if parent_data and 'children' in parent_data:
                del parent_data['children'][option_path[-1]]

                # 如果父选项没有子选项了，改为final类型
                if not parent_data['children']:
                    parent_data['type'] = 'final'

        self.refresh_options_tree()
        self.logger.info(f"删除了选项: {' -> '.join(option_path)}")
        # 触发自动保存
        self.on_text_changed()

    def get_option_path(self, item_id: str) -> List[str]:
        """获取选项的路径"""
        if '_' in item_id:
            return item_id.split('_')
        else:
            return [item_id]

    def get_option_data_by_path(self, path: List[str]) -> Optional[Dict[str, Any]]:
        """根据路径获取选项数据"""
        data = self.tree_option_data
        for key in path:
            if key in data:
                if len(path) == 1 or key == path[-1]:
                    return data[key]
                else:
                    data = data[key]['children']
            else:
                return None
        return None

    def expand_all_options(self):
        """展开所有选项"""
        def expand_item(item):
            self.options_tree.item(item, open=True)
            for child in self.options_tree.get_children(item):
                expand_item(child)

        for item in self.options_tree.get_children():
            expand_item(item)

    def collapse_all_options(self):
        """折叠所有选项"""
        def collapse_item(item):
            self.options_tree.item(item, open=False)
            for child in self.options_tree.get_children(item):
                collapse_item(child)

        for item in self.options_tree.get_children():
            collapse_item(item)

    def edit_option_item(self, event):
        """双击编辑选项项目"""
        item = self.options_tree.selection()[0]
        option_path = self.get_option_path(item)
        option_data = self.get_option_data_by_path(option_path)

        if not option_data:
            return

        # 创建编辑对话框
        self.show_option_edit_dialog(option_path, option_data)

    def show_option_edit_dialog(self, option_path: List[str], option_data: Dict[str, Any]):
        """显示选项编辑对话框"""
        dialog = tk.Toplevel(self.main_window.root)
        dialog.title(f"编辑选项: {' -> '.join(option_path)}")
        dialog.geometry("650x500")
        dialog.transient(self.main_window.root)
        dialog.grab_set()

        # 选项文本
        ttk.Label(dialog, text="选项文本:", font=("", 10, "bold")).pack(anchor=tk.W, padx=10, pady=(10, 5))
        text_entry = ttk.Entry(dialog, width=70)
        text_entry.pack(padx=10, pady=(0, 10), fill=tk.X)
        text_entry.insert(0, option_data['text'])

        # 回复内容
        ttk.Label(dialog, text="回复内容:", font=("", 10, "bold")).pack(anchor=tk.W, padx=10, pady=(10, 5))

        reply_frame = ttk.Frame(dialog)
        reply_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 5))

        reply_text = tk.Text(reply_frame, height=8, wrap=tk.WORD)
        reply_scrollbar = ttk.Scrollbar(reply_frame, orient="vertical", command=reply_text.yview)
        reply_text.configure(yscrollcommand=reply_scrollbar.set)

        reply_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        reply_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        reply_text.insert('1.0', option_data['reply'])

        # 文件上传区域
        file_frame = ttk.LabelFrame(dialog, text="文件上传", padding="5")
        file_frame.pack(fill=tk.X, padx=10, pady=(5, 10))

        # 文件选择按钮和显示
        file_select_frame = ttk.Frame(file_frame)
        file_select_frame.pack(fill=tk.X, pady=2)

        selected_file_var = tk.StringVar()
        file_label = ttk.Label(file_select_frame, text="未选择文件", foreground="gray")
        file_label.pack(side=tk.LEFT, padx=(0, 10))

        def upload_txt_file():
            """直接上传txt文件并覆盖回复内容"""
            file_path = filedialog.askopenfilename(
                title="选择txt文件",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )

            if not file_path:
                return

            try:
                # 尝试UTF-8编码读取
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
            except UnicodeDecodeError:
                try:
                    # 尝试GBK编码读取
                    with open(file_path, 'r', encoding='gbk') as f:
                        content = f.read()
                except Exception as e:
                    tk.messagebox.showerror("错误", f"读取文件失败: {str(e)}")
                    return
            except Exception as e:
                tk.messagebox.showerror("错误", f"读取文件失败: {str(e)}")
                return

            if not content.strip():
                tk.messagebox.showwarning("警告", "文件内容为空！")
                return

            # 覆盖回复内容
            reply_text.delete('1.0', tk.END)
            reply_text.insert('1.0', content)

            # 更新文件标签显示
            import os
            filename = os.path.basename(file_path)
            file_label.config(text=f"已上传: {filename}", foreground="green")
            selected_file_var.set(file_path)

            tk.messagebox.showinfo("成功", f"已成功上传并覆盖回复内容！\n文件: {filename}")

        ttk.Button(file_select_frame, text="上传txt文件", command=upload_txt_file,
                  bootstyle="info").pack(side=tk.LEFT, padx=(0, 5))

        # 选项类型
        ttk.Label(dialog, text="选项类型:", font=("", 10, "bold")).pack(anchor=tk.W, padx=10, pady=(10, 5))

        type_frame = ttk.Frame(dialog)
        type_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        type_var = tk.StringVar(value=option_data['type'])
        ttk.Radiobutton(type_frame, text="最终选项（直接回复）", variable=type_var, value="final").pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(type_frame, text="菜单选项（显示子选项）", variable=type_var, value="menu").pack(side=tk.LEFT)

        # 按钮区域
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        def save_changes():
            option_data['text'] = text_entry.get().strip()
            option_data['reply'] = reply_text.get('1.0', tk.END).strip()
            option_data['type'] = type_var.get()

            self.refresh_options_tree()
            dialog.destroy()
            self.logger.info(f"更新了选项: {' -> '.join(option_path)}")
            # 触发自动保存
            self.on_text_changed()

        def cancel_changes():
            dialog.destroy()

        ttk.Button(button_frame, text="保存", command=save_changes, bootstyle="success").pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=cancel_changes, bootstyle="secondary").pack(side=tk.RIGHT)

    def refresh_group_options(self):
        """刷新配置（私聊机器人无需此功能）"""
        pass

    def on_group_selection_changed(self, event=None):
        """群选择改变时的处理（私聊机器人无需此功能）"""
        pass

    def load_private_config(self):
        """加载私聊配置"""
        try:
            # 尝试加载私聊配置
            config = None
            if 'private_chat' in self.main_window.group_option_configs:
                config = self.main_window.group_option_configs['private_chat']
            elif '' in self.main_window.group_option_configs:
                config = self.main_window.group_option_configs['']

            if config:
                # 加载自动回复内容
                self.auto_reply_text.delete('1.0', tk.END)
                self.auto_reply_text.insert('1.0', config.get('auto_reply', ''))

                # 加载间隔时间设置
                interval_minutes = config.get('interval_minutes', 30)
                self.interval_var.set(str(interval_minutes))

                # 立即应用间隔时间设置
                if self.main_window.message_processor:
                    self.main_window.message_processor.set_interval_seconds(interval_minutes * 60)

                # 加载树状选项数据
                tree_data = config.get('tree_options', {})
                if tree_data:
                    self.tree_option_data = tree_data
                else:
                    # 兼容旧版本的平面选项配置
                    old_options = config.get('option_replies', {})
                    if old_options:
                        self.tree_option_data = self.convert_flat_to_tree(old_options)
                    else:
                        self.create_default_tree_options()
                        return

                self.refresh_options_tree()
            else:
                # 使用默认配置
                self.create_default_tree_options()

        except Exception as e:
            self.logger.error(f"加载私聊配置失败: {str(e)}")

    def load_group_config(self, group_id: str):
        """兼容性方法，重定向到加载私聊配置"""
        self.load_private_config()

    def convert_flat_to_tree(self, flat_options: Dict[str, str]) -> Dict[str, Any]:
        """将平面选项配置转换为树状结构"""
        tree_data = {}
        for key, reply in flat_options.items():
            tree_data[key] = {
                'text': f'选项{key}',
                'reply': reply,
                'type': 'final',
                'children': {}
            }
        return tree_data

    def save_option_config(self):
        """保存私聊选项配置（自动调用）"""
        try:
            # 获取当前配置
            current_config = {
                'auto_reply': self.auto_reply_text.get('1.0', tk.END).strip(),
                'tree_options': self.tree_option_data.copy(),
                'interval_minutes': int(self.interval_var.get()) if self.interval_var.get().isdigit() else 30
            }

            # 保存到私聊配置
            self.main_window.group_option_configs['private_chat'] = current_config.copy()
            self.main_window.group_option_configs[''] = current_config.copy()  # 兼容性
            self.logger.info("保存私聊选项配置")

            # 保存到配置文件
            self.main_window.save_config()

            # 更新消息处理器中的选项配置
            if self.main_window.message_processor:
                self.main_window.message_processor.update_config(
                    self.main_window.group_option_configs,
                    self.main_window.paused_rooms
                )

        except Exception as e:
            self.logger.error(f"保存选项配置失败: {e}")
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")

    def on_text_changed(self, event=None):
        """文本内容改变时的处理"""
        # 取消之前的定时器
        if self.auto_save_timer:
            self.main_window.root.after_cancel(self.auto_save_timer)

        # 设置新的定时器，2秒后自动保存
        self.auto_save_timer = self.main_window.root.after(2000, self.auto_save_config)

    def auto_save_config(self):
        """自动保存配置"""
        try:
            self.save_option_config()
            self.logger.info("配置已自动保存")
        except Exception as e:
            self.logger.error(f"自动保存失败: {e}")

    def on_interval_changed(self, event=None):
        """间隔时间改变时的处理"""
        try:
            minutes = int(self.interval_var.get())
            seconds = minutes * 60

            # 更新消息处理器的间隔时间
            if self.main_window.message_processor:
                self.main_window.message_processor.set_interval_seconds(seconds)

            self.logger.info(f"间隔时间已更新为: {minutes}分钟 ({seconds}秒)")

            # 触发自动保存
            self.on_text_changed()

        except ValueError:
            self.logger.warning("间隔时间输入无效，使用默认值30分钟")
            self.interval_var.set("30")
            if self.main_window.message_processor:
                self.main_window.message_processor.set_interval_seconds(1800)

    def set_initial_interval(self):
        """设置初始间隔时间"""
        try:
            if self.main_window.message_processor:
                minutes = int(self.interval_var.get())
                seconds = minutes * 60
                self.main_window.message_processor.set_interval_seconds(seconds)
                self.logger.info(f"初始间隔时间设置为: {minutes}分钟")
        except (ValueError, AttributeError):
            if self.main_window.message_processor:
                self.main_window.message_processor.set_interval_seconds(1800)  # 默认30分钟
