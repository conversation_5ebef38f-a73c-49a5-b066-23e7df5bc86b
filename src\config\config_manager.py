# -*- coding: utf-8 -*-
"""
配置管理模块 - 处理配置文件的读取和保存
"""

import json
import os
import logging
from typing import Dict, List, Any, Optional


class ConfigManager:
    """
    配置管理器 - 负责配置文件的读取、保存和管理
    """
    
    def __init__(self, config_file: str = "config.json"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.logger = logging.getLogger(__name__)
        self._config_data = {}
        
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            配置数据字典
        """
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, "r", encoding="utf-8") as f:
                    self._config_data = json.load(f)
                    self.logger.info("配置加载成功")
            else:
                self.logger.warning(f"配置文件 {self.config_file} 不存在，使用默认配置")
                self._config_data = self._get_default_config()
                
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            self._config_data = self._get_default_config()
            
        return self._config_data.copy()
    
    def save_config(self, config_data: Optional[Dict[str, Any]] = None) -> bool:
        """
        保存配置到文件
        
        Args:
            config_data: 要保存的配置数据，如果为None则保存当前配置
            
        Returns:
            保存是否成功
        """
        try:
            data_to_save = config_data if config_data is not None else self._config_data
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data_to_save, f, ensure_ascii=False, indent=4)
                
            self.logger.info("配置保存成功")
            return True
            
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
            return False
    
    def get_group_option_configs(self) -> Dict[str, Dict[str, Any]]:
        """
        获取私聊选项配置（保持接口兼容性）

        Returns:
            私聊选项配置字典
        """
        return self._config_data.get('group_option_configs', {})

    def set_group_option_configs(self, configs: Dict[str, Dict[str, Any]]):
        """
        设置私聊选项配置（保持接口兼容性）

        Args:
            configs: 私聊选项配置字典
        """
        self._config_data['group_option_configs'] = configs
    
    def get_monitored_rooms(self) -> List[Dict[str, str]]:
        """
        获取监控群列表
        
        Returns:
            监控群列表
        """
        return self._config_data.get('monitored_rooms', [])
    
    def set_monitored_rooms(self, rooms: List[Dict[str, str]]):
        """
        设置监控群列表
        
        Args:
            rooms: 监控群列表
        """
        self._config_data['monitored_rooms'] = rooms
    
    def get_paused_rooms(self) -> List[str]:
        """
        获取暂停群列表
        
        Returns:
            暂停群ID列表
        """
        return self._config_data.get('paused_rooms', [])
    
    def set_paused_rooms(self, rooms: List[str]):
        """
        设置暂停群列表
        
        Args:
            rooms: 暂停群ID列表
        """
        self._config_data['paused_rooms'] = rooms
    
    def _get_default_config(self) -> Dict[str, Any]:
        """
        获取默认配置
        
        Returns:
            默认配置字典
        """
        return {
            'group_option_configs': {
                'private_chat': {
                    'auto_reply': '请问您需要咨询以下哪项业务？请直接回复对应数字：\n1.查询快递物流信息\n2.投诉/反馈问题（丢件/破损/延误等）\n3.联系人工客服\n4.网点/快递员联系方式查询\n5.修改收件地址或时间',
                    'interval_minutes': 30,
                    'option_replies': {
                        '1': '输入 查快递+单号 查询快递',
                        '2': '加客服微信',
                        '3': '加客服微信2',
                        '4': '加客服微信3',
                        '5': '加客服微信4'
                    }
                }
            },
            'monitored_rooms': [],
            'paused_rooms': []
        }
    
    def update_config(self, key: str, value: Any):
        """
        更新配置项
        
        Args:
            key: 配置键
            value: 配置值
        """
        self._config_data[key] = value
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """
        获取配置项
        
        Args:
            key: 配置键
            default: 默认值
            
        Returns:
            配置值
        """
        return self._config_data.get(key, default)
