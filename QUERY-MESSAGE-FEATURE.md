# 查询消息功能说明

## 功能概述

在 `MessageProcessor` 的 `on_recv_message` 方法中，新增了对"查询"消息的特殊处理。当用户发送"查询"消息时，系统会：

1. **清除历史会话** - 删除用户的所有历史会话记录
2. **创建新会话** - 立即创建一个新的会话
3. **发送欢迎消息** - 发送配置的自动回复内容

## 实现细节

### 1. 消息检测

在 `on_recv_message` 方法中添加了查询消息检测：

```python
# 检查是否是"查询"消息，如果是则创建新会话并清除历史会话
if content.strip() == "查询":
    self._handle_query_message(wechat_instance, room_wxid, from_wxid, sender_name, is_private)
    return
```

### 2. 查询消息处理

新增 `_handle_query_message` 方法：

- 验证是否为私聊消息
- 获取私聊配置
- 清除用户历史会话
- 创建新的查询会话

### 3. 会话清除

新增 `_clear_user_sessions` 方法：

- 清除数据库中的活跃会话
- 清除内存中的会话数据
- 清除用户完成时间记录

### 4. 新会话创建

新增 `_create_new_query_session` 方法：

- 在数据库中创建新会话（标记来源为 'query_message'）
- 在内存中创建会话数据
- 发送自动回复消息

## 使用方法

### 用户操作

1. 用户发送消息："查询"
2. 系统自动清除该用户的所有历史会话
3. 系统创建新会话并发送欢迎消息
4. 用户可以重新开始选择流程

### 配置要求

确保在 `group_option_configs` 中配置了私聊设置：

```python
{
    'private_chat': {
        'auto_reply': '欢迎咨询！请选择您需要的服务：\n1. 产品咨询\n2. 技术支持\n3. 售后服务',
        'tree_options': {
            # 选项配置...
        }
    }
}
```

## 功能特点

### 1. 即时重置

- 无需等待间隔时间
- 立即清除所有历史记录
- 立即创建新会话

### 2. 完整清理

- 清除数据库中的活跃会话
- 清除内存中的会话数据
- 清除完成时间记录

### 3. 日志记录

- 详细记录查询消息处理过程
- 记录会话清除和创建操作
- 便于调试和监控

### 4. 错误处理

- 完善的异常处理机制
- 失败时记录详细错误信息
- 不影响其他功能正常运行

## 测试验证

运行 `test_query_message.py` 可以验证功能：

```bash
python test_query_message.py
```

测试包括：
1. 发送查询消息创建新会话
2. 用户选择选项完成对话
3. 再次发送查询消息清除历史
4. 验证非查询消息正常处理

## 注意事项

1. **仅限私聊** - 查询功能只在私聊中生效，群聊消息会被忽略
2. **精确匹配** - 必须发送完全匹配的"查询"文本（去除首尾空格后）
3. **配置依赖** - 需要正确配置私聊的 `auto_reply` 内容
4. **数据库支持** - 支持数据库和内存两种会话管理方式

## 日志示例

```
2025-08-04 15:18:54 - INFO - 收到查询消息，开始处理新会话创建: 用户=测试用户(S:test_user_123), 会话ID=S:test_user_123
2025-08-04 15:18:54 - INFO - 用户完成时间记录已清除: S:test_user_123
2025-08-04 15:18:54 - INFO - 数据库创建查询会话成功: S:test_user_123
2025-08-04 15:18:54 - INFO - 查询会话创建成功并发送欢迎消息: S:test_user_123
```

这个功能为用户提供了一个便捷的重置方式，无需等待间隔时间就可以重新开始咨询流程。
