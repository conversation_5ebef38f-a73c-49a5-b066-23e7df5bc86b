# -*- coding: utf-8 -*-
"""
微信处理器模块 - 负责微信连接和基础操作
"""

import time
import logging
import threading
from typing import Optional, Callable, Any

from ..utils.decorators import singleton_thread_safe

# 尝试导入真实的ntwork，如果失败则使用模拟版本
try:
    import ntwork
except ImportError:
    from ..utils import mock_ntwork as ntwork


@singleton_thread_safe
class WeChatHandler:
    """
    微信处理器 - 负责微信连接、登录和基础操作
    """
    
    def __init__(self):
        """初始化微信处理器"""
        self.logger = logging.getLogger(__name__)
        self.wechat = ntwork.WeWork()
        self.user_id: Optional[str] = None
        self.name: Optional[str] = None
        self.is_connected = False
        self.exit_flag = False
        
    def initialize(self) -> bool:
        """
        初始化微信连接
        
        Returns:
            初始化是否成功
        """
        try:
            # 清理之前的实例
            self._cleanup_previous_instance()
            
            # 打开企业微信
            try:
                self.wechat.open(smart=True)
            except Exception as e:
                self.logger.error(f"打开企业微信失败: {e}")
                # 尝试重新初始化 ntwork
                ntwork.exit_()
                time.sleep(2)
                self.wechat = ntwork.WeWork()
                self.wechat.open(smart=True)
            
            self.logger.info("等待登录......")
            self.wechat.wait_login(timeout=500)
            self.logger.info("登录成功，等待数据同步...")
            time.sleep(20)  # 增加等待时间到20秒，让数据更充分同步
            
            # 获取登录信息
            if self._get_login_info():
                self.is_connected = True
                return True
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"初始化微信失败: {str(e)}")
            self.logger.error(f"错误详情: ", exc_info=True)
            return False
    
    def _cleanup_previous_instance(self):
        """清理之前的实例"""
        try:
            # 注册空回调替代注销
            self.wechat.msg_register(ntwork.MT_RECV_TEXT_MSG)(lambda x, y: None)
        except:
            pass
        time.sleep(1)  # 等待资源释放
    
    def _get_login_info(self) -> bool:
        """
        获取登录信息
        
        Returns:
            获取是否成功
        """
        max_retries = 3
        for i in range(max_retries):
            try:
                login_info = self.wechat.get_login_info()
                if login_info and 'user_id' in login_info:
                    self.user_id = login_info["user_id"]
                    if login_info.get("nickname") == '':
                        self.name = login_info.get("username")
                    else:
                        self.name = login_info.get("nickname")
                    self.logger.info(f"登录信息: user_id:{self.user_id}, name:{self.name}")
                    return True
                else:
                    if i < max_retries - 1:
                        self.logger.warning(f"获取登录信息不完整，正在重试... ({i + 1}/{max_retries})")
                        time.sleep(2)  # 等待2秒后重试
                    else:
                        raise RuntimeError("无法获取完整的登录信息")
            except Exception as e:
                if i < max_retries - 1:
                    self.logger.warning(f"获取登录信息失败，正在重试... ({i + 1}/{max_retries})")
                    time.sleep(2)
                else:
                    self.logger.error(f"获取登录信息失败: {e}")
                    return False
        return False
    
    def register_message_callback(self, text_callback: Callable[[ntwork.WeWork, Any], None],
                                 sys_callback: Callable[[ntwork.WeWork, Any], None] = None):
        """
        注册消息回调函数

        Args:
            text_callback: 文本消息处理回调函数
            sys_callback: 系统消息处理回调函数
        """
        try:
            self.wechat.msg_register(ntwork.MT_RECV_TEXT_MSG)(text_callback)
            self.logger.info("文本消息回调注册成功")

            if sys_callback:
                self.wechat.msg_register(ntwork.MT_RECV_SYS_MSG)(sys_callback)
                self.logger.info("系统消息回调注册成功")
        except Exception as e:
            self.logger.error(f"注册消息回调失败: {e}")
            raise
    
    def unregister_message_callback(self):
        """取消消息回调"""
        try:
            self.wechat.msg_register(ntwork.MT_RECV_TEXT_MSG)(lambda wechat_instance, message: None)
            self.wechat.msg_register(ntwork.MT_RECV_SYS_MSG)(lambda wechat_instance, message: None)
            self.logger.info("消息回调已取消")
        except Exception as e:
            self.logger.error(f"取消消息回调失败: {e}")
    
    def get_rooms(self, page_num: int = 1, page_size: int = 500) -> Optional[dict]:
        """
        获取群列表
        
        Args:
            page_num: 页码
            page_size: 每页大小
            
        Returns:
            群列表数据
        """
        try:
            return self.wechat.get_rooms(page_num=page_num, page_size=page_size)
        except Exception as e:
            self.logger.error(f"获取群列表失败: {e}")
            return None
    
    def send_text(self, conversation_id: str, content: str) -> bool:
        """
        发送文本消息
        
        Args:
            conversation_id: 会话ID
            content: 消息内容
            
        Returns:
            发送是否成功
        """
        try:
            self.wechat.send_text(conversation_id, content)
            return True
        except Exception as e:
            self.logger.error(f"发送文本消息失败: {e}")
            return False
    
    def send_link_card(self, conversation_id: str, title: str, desc: str, url: str, image_url: str) -> bool:
        """
        发送链接卡片

        Args:
            conversation_id: 会话ID
            title: 标题
            desc: 描述
            url: 链接地址
            image_url: 图片地址

        Returns:
            发送是否成功
        """
        try:
            self.wechat.send_link_card(
                conversation_id=conversation_id,
                title=title,
                desc=desc,
                url=url,
                image_url=image_url
            )
            return True
        except Exception as e:
            self.logger.error(f"发送链接卡片失败: {e}")
            return False

    def send_card(self, conversation_id: str, user_id: str) -> bool:
        """
        发送用户卡片

        Args:
            conversation_id: 会话ID
            user_id: 用户ID

        Returns:
            发送是否成功
        """
        try:
            self.wechat.send_card(
                conversation_id=conversation_id,
                user_id=user_id
            )
            return True
        except Exception as e:
            self.logger.error(f"发送用户卡片失败: {e}")
            return False

    def send_image(self, conversation_id: str, file_path: str) -> bool:
        """
        发送图片/文件

        Args:
            conversation_id: 会话ID
            file_path: 文件路径

        Returns:
            发送是否成功
        """
        try:
            self.wechat.send_image(
                conversation_id=conversation_id,
                file_path=file_path
            )
            return True
        except Exception as e:
            self.logger.error(f"发送图片/文件失败: {e}")
            return False

    def send_video(self, conversation_id: str, file_path: str) -> bool:
        """
        发送视频

        Args:
            conversation_id: 会话ID
            file_path: 视频文件路径

        Returns:
            发送是否成功
        """
        try:
            self.wechat.send_video(
                conversation_id=conversation_id,
                file_path=file_path
            )
            return True
        except Exception as e:
            self.logger.error(f"发送视频失败: {e}")
            return False

    def send_miniapp(self, aes_key: str, file_id: str, size: str, appicon: str,
                    appid: str, appname: str, conversation_id: str, page_path: str,
                    title: str, username: str) -> bool:
        """
        发送小程序

        Args:
            aes_key: AES密钥
            file_id: 文件ID
            size: 大小
            appicon: 应用图标
            appid: 应用ID
            appname: 应用名称
            conversation_id: 会话ID
            page_path: 页面路径
            title: 标题
            username: 用户名

        Returns:
            发送是否成功
        """
        try:
            self.wechat.send_miniapp(
                aes_key=aes_key,
                file_id=file_id,
                size=size,
                appicon=appicon,
                appid=appid,
                appname=appname,
                conversation_id=conversation_id,
                page_path=page_path,
                title=title,
                username=username
            )
            return True
        except Exception as e:
            self.logger.error(f"发送小程序失败: {e}")
            return False
    
    def get_login_info(self) -> Optional[dict]:
        """
        获取登录信息
        
        Returns:
            登录信息字典
        """
        try:
            return self.wechat.get_login_info()
        except Exception as e:
            self.logger.error(f"获取登录信息失败: {e}")
            return None
    
    def exit_program(self):
        """退出程序"""
        self.logger.info("正在退出程序...")
        self.exit_flag = True
        try:
            import keyboard
            keyboard.unhook_all()
        except ImportError:
            try:
                from ..utils import mock_keyboard as keyboard
                keyboard.unhook_all()
            except:
                pass
        except:
            pass
        ntwork.exit_()
        self.is_connected = False
