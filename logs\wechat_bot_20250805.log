2025-08-05 13:35:08 - src.core.message_processor.1773149109168 - INFO - 日志文件已配置: F:\soft\python\wechat\新建文件夹\客服选项选择\logs\wechat_bot_20250805.log
2025-08-05 13:35:08 - src.core.message_processor.1773149109168 - INFO - 消息处理器日志系统初始化完成
2025-08-05 13:35:09 - src.core.message_processor.1773149109168 - INFO - 设置间隔时间为: 1800秒 (30分钟)
2025-08-05 13:35:09 - src.core.message_processor.1773149109168 - INFO - 设置间隔时间为: 60秒 (1分钟)
2025-08-05 13:36:19 - src.core.message_processor.2544743365552 - INFO - 日志文件已配置: F:\soft\python\wechat\新建文件夹\客服选项选择\logs\wechat_bot_20250805.log
2025-08-05 13:36:20 - src.core.message_processor.2544743365552 - INFO - 消息处理器日志系统初始化完成
2025-08-05 13:39:37 - src.core.message_processor.2544743365552 - INFO - 收到原始消息: {"data": {"appinfo": "1RT-DQj3RZKVZs1", "at_list": [], "content": "我是嫒宝粑粑", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "4366", "receiver": "1688854710715177", "send_time": "1754372377", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1021739"}, "type": 11041}
2025-08-05 13:39:37 - src.core.message_processor.2544743365552 - INFO - 机器人名字: 邾俊勇
2025-08-05 13:39:37 - src.core.message_processor.2544743365552 - INFO - 消息类型: 2
2025-08-05 13:39:37 - src.core.message_processor.2544743365552 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 我是嫒宝粑粑
2025-08-05 13:39:37 - src.core.message_processor.2544743365552 - INFO - 消息类型: 私聊
2025-08-05 13:39:37 - src.core.message_processor.2544743365552 - INFO - 用户会话超时，清理会话: 7881299718050125
2025-08-05 13:39:37 - src.core.message_processor.2544743365552 - INFO - 发送会话超时消息给用户: 7881299718050125
2025-08-05 13:39:37 - src.core.message_processor.2544743365552 - INFO - 用户 7881299718050125 不满足创建新会话条件，忽略文本消息
2025-08-05 13:39:37 - src.core.message_processor.2544743365552 - INFO - 收到系统消息: {"data": {"appinfo": "qzahprKaTMWoQfI", "content": "以上是打招呼内容", "content_type": 1011, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "receiver": "1688854710715177", "send_time": "1754372377", "sender": "7881299718050125", "sender_name": "", "server_id": "1021742"}, "type": 11187}
2025-08-05 13:39:37 - src.core.message_processor.2544743365552 - INFO - 收到系统消息: {"data": {"appinfo": "uGs13AIKSciPwit", "content": "你已添加了嫒宝粑粑，现在可以开始聊天了。", "content_type": 1011, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "receiver": "1688854710715177", "send_time": "1754372377", "sender": "7881299718050125", "sender_name": "", "server_id": "1021744"}, "type": 11187}
2025-08-05 13:39:37 - src.core.message_processor.2544743365552 - INFO - 检查是否是新联系人消息----True
2025-08-05 13:39:37 - src.core.message_processor.2544743365552 - INFO - 检查是否是新联系人消息----True
2025-08-05 13:39:37 - src.core.message_processor.2544743365552 - INFO - 检测到新联系人消息，开始处理...
2025-08-05 13:39:37 - src.core.message_processor.2544743365552 - INFO - 检测到新联系人消息，开始处理...
2025-08-05 13:39:37 - src.core.message_processor.2544743365552 - INFO - 系统消息解析: conversation_id=S:1688854710715177_7881299718050125, from_wxid=7881299718050125
2025-08-05 13:39:38 - src.core.message_processor.2544743365552 - INFO - 系统消息解析: conversation_id=S:1688854710715177_7881299718050125, from_wxid=7881299718050125
2025-08-05 13:39:38 - src.core.message_processor.2544743365552 - INFO - 处理新联系人: 7881299718050125, 会话ID: S:1688854710715177_7881299718050125
2025-08-05 13:39:38 - src.core.message_processor.2544743365552 - INFO - 处理新联系人: 7881299718050125, 会话ID: S:1688854710715177_7881299718050125
2025-08-05 13:39:38 - src.core.message_processor.2544743365552 - INFO - 数据库创建会话成功: 7881299718050125
2025-08-05 13:39:38 - src.core.message_processor.2544743365552 - INFO - 用户 7881299718050125 已有活跃会话，跳过创建
2025-08-05 13:39:38 - src.core.message_processor.2544743365552 - INFO - 收到原始消息: {"data": {"appinfo": "CAEQmbLGxAYYqYbwiJKAgAMgAQ==", "at_list": [], "content": "您的会话已超时，如需继续咨询请重新发送消息。", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 1, "local_id": "4367", "receiver": "7881299718050125", "send_time": "1754372377", "sender": "1688854710715177", "sender_name": "邾俊勇", "server_id": "1021746"}, "type": 11041}
2025-08-05 13:39:38 - src.core.message_processor.2544743365552 - INFO - 向新联系人发送欢迎消息并创建会话: 7881299718050125, 会话ID: 7881299718050125
2025-08-05 13:39:38 - src.core.message_processor.2544743365552 - INFO - 机器人名字: 邾俊勇
2025-08-05 13:39:38 - src.core.message_processor.2544743365552 - INFO - 忽略自己发送的消息
2025-08-05 13:39:38 - src.core.message_processor.2544743365552 - INFO - 收到原始消息: {"data": {"appinfo": "CAEQmrLGxAYYqYbwiJKAgAMgAg==", "at_list": [], "content": "您好！欢迎咨询，请问您需要以下哪项服务？请直接回复对应数字：\n1.查询快递物流信息\n2.投诉/反馈问题\n3.联系人工客服", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 1, "local_id": "4370", "receiver": "7881299718050125", "send_time": "1754372377", "sender": "1688854710715177", "sender_name": "邾俊勇", "server_id": "1021748"}, "type": 11041}
2025-08-05 13:39:38 - src.core.message_processor.2544743365552 - INFO - 机器人名字: 邾俊勇
2025-08-05 13:39:38 - src.core.message_processor.2544743365552 - INFO - 忽略自己发送的消息
2025-08-05 13:39:58 - src.core.message_processor.2544743365552 - INFO - 收到原始消息: {"data": {"appinfo": "2985545302588690313", "at_list": [], "content": "3", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "4371", "receiver": "1688854710715177", "send_time": "1754372397", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1021752"}, "type": 11041}
2025-08-05 13:39:58 - src.core.message_processor.2544743365552 - INFO - 机器人名字: 邾俊勇
2025-08-05 13:39:58 - src.core.message_processor.2544743365552 - INFO - 消息类型: 2
2025-08-05 13:39:58 - src.core.message_processor.2544743365552 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 3
2025-08-05 13:39:58 - src.core.message_processor.2544743365552 - INFO - 消息类型: 私聊
2025-08-05 13:39:59 - src.core.message_processor.2544743365552 - INFO - 开始处理特殊回复，内容长度: 205
2025-08-05 13:39:59 - src.core.message_processor.2544743365552 - INFO - 检测到特殊回复格式
2025-08-05 13:39:59 - src.core.message_processor.2544743365552 - INFO - 解析到的参数: {'wait_reply': '正在为您转接人工客服，请稍候...', 'title': '企业微信客服', 'desc': '点击立即咨询', 'url': 'https://work.weixin.qq.com/kfid/kfca4f4214fae5fdcf9', 'img': 'https://img2.baidu.com/it/u=2413920414,256334177&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500'}
2025-08-05 13:39:59 - src.core.message_processor.2544743365552 - INFO - 发送等待回复给私聊用户: 7881299718050125, 聊天ID: S:1688854710715177_7881299718050125
2025-08-05 13:39:59 - src.core.message_processor.2544743365552 - INFO - 收到原始消息: {"data": {"appinfo": "CAEQr7LGxAYYqYbwiJKAgAMgAw==", "at_list": [], "content": "正在为您转接人工客服，请稍候...", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 1, "local_id": "4372", "receiver": "7881299718050125", "send_time": "1754372398", "sender": "1688854710715177", "sender_name": "邾俊勇", "server_id": "1021755"}, "type": 11041}
2025-08-05 13:39:59 - src.core.message_processor.2544743365552 - INFO - 机器人名字: 邾俊勇
2025-08-05 13:39:59 - src.core.message_processor.2544743365552 - INFO - 忽略自己发送的消息
2025-08-05 13:40:00 - src.core.message_processor.2544743365552 - INFO - 小程序缺失参数: ['aes_key', 'file_id', 'size', 'appicon', 'appid', 'appname', 'page_path', 'username']
2025-08-05 13:40:00 - src.core.message_processor.2544743365552 - INFO - 所有链接卡片参数都存在，尝试发送链接卡片
2025-08-05 13:40:00 - src.core.message_processor.2544743365552 - INFO - 调用send_link_card: conversation_id=S:1688854710715177_7881299718050125, title=企业微信客服, desc=点击立即咨询, url=https://work.weixin.qq.com/kfid/kfca4f4214fae5fdcf9, image_url=https://img2.baidu.com/it/u=2413920414,256334177&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500
2025-08-05 13:40:00 - src.core.message_processor.2544743365552 - INFO - ✅ 成功发送链接卡片给私聊用户: 7881299718050125, 聊天ID: S:1688854710715177_7881299718050125
2025-08-05 13:40:00 - src.core.message_processor.2544743365552 - INFO - 会话已在数据库中标记为失效: 7881299718050125
2025-08-05 13:40:00 - src.core.message_processor.2544743365552 - INFO - 对话结束，清理会话: 7881299718050125, 记录完成时间: 1754372400.232954
2025-08-05 13:40:09 - src.core.message_processor.2544743365552 - INFO - 收到原始消息: {"data": {"appinfo": "2933929994860967867", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "4374", "receiver": "1688854710715177", "send_time": "1754372408", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1021761"}, "type": 11041}
2025-08-05 13:40:09 - src.core.message_processor.2544743365552 - INFO - 机器人名字: 邾俊勇
2025-08-05 13:40:09 - src.core.message_processor.2544743365552 - INFO - 消息类型: 2
2025-08-05 13:40:09 - src.core.message_processor.2544743365552 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 1
2025-08-05 13:40:09 - src.core.message_processor.2544743365552 - INFO - 消息类型: 私聊
2025-08-05 13:40:09 - src.core.message_processor.2544743365552 - INFO - 用户 7881299718050125 不满足创建新会话条件，忽略文本消息
2025-08-05 13:46:27 - src.core.message_processor.2544743365552 - INFO - 收到原始消息: {"data": {"appinfo": "2433383419869387118", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "4375", "receiver": "1688854710715177", "send_time": "1754372786", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1021766"}, "type": 11041}
2025-08-05 13:46:27 - src.core.message_processor.2544743365552 - INFO - 机器人名字: 邾俊勇
2025-08-05 13:46:27 - src.core.message_processor.2544743365552 - INFO - 消息类型: 2
2025-08-05 13:46:27 - src.core.message_processor.2544743365552 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 1
2025-08-05 13:46:28 - src.core.message_processor.2544743365552 - INFO - 消息类型: 私聊
2025-08-05 13:46:28 - src.core.message_processor.2544743365552 - INFO - 用户 7881299718050125 不满足创建新会话条件，忽略文本消息
2025-08-05 13:46:51 - src.core.message_processor.2544743365552 - INFO - 收到原始消息: {"data": {"appinfo": "8726061551698403231", "at_list": [], "content": "2", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "4376", "receiver": "1688854710715177", "send_time": "1754372809", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1021769"}, "type": 11041}
2025-08-05 13:46:51 - src.core.message_processor.2544743365552 - INFO - 机器人名字: 邾俊勇
2025-08-05 13:46:51 - src.core.message_processor.2544743365552 - INFO - 消息类型: 2
2025-08-05 13:46:51 - src.core.message_processor.2544743365552 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 2
2025-08-05 13:46:51 - src.core.message_processor.2544743365552 - INFO - 消息类型: 私聊
2025-08-05 13:46:51 - src.core.message_processor.2544743365552 - INFO - 用户 7881299718050125 不满足创建新会话条件，忽略文本消息
2025-08-05 13:48:32 - src.core.message_processor.2544743365552 - INFO - 收到原始消息: {"data": {"appinfo": "1479818262527152225", "at_list": [], "content": "2", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "4377", "receiver": "1688854710715177", "send_time": "1754372910", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1021772"}, "type": 11041}
2025-08-05 13:48:32 - src.core.message_processor.2544743365552 - INFO - 机器人名字: 邾俊勇
2025-08-05 13:48:32 - src.core.message_processor.2544743365552 - INFO - 消息类型: 2
2025-08-05 13:48:32 - src.core.message_processor.2544743365552 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 2
2025-08-05 13:48:32 - src.core.message_processor.2544743365552 - INFO - 消息类型: 私聊
2025-08-05 13:48:32 - src.core.message_processor.2544743365552 - INFO - 用户 7881299718050125 不满足创建新会话条件，忽略文本消息
2025-08-05 13:57:36 - src.core.message_processor.2031410644360 - INFO - 日志文件已配置: F:\soft\python\wechat\新建文件夹\客服选项选择\logs\wechat_bot_20250805.log
2025-08-05 13:57:36 - src.core.message_processor.2031410644360 - INFO - 消息处理器日志系统初始化完成
2025-08-05 13:57:36 - src.core.message_processor.2031410644360 - INFO - 设置间隔时间为: 60秒 (1分钟)
2025-08-05 13:59:57 - src.core.message_processor.2959327704176 - INFO - 日志文件已配置: F:\soft\python\wechat\新建文件夹\客服选项选择\logs\wechat_bot_20250805.log
2025-08-05 13:59:57 - src.core.message_processor.2959327704176 - INFO - 消息处理器日志系统初始化完成
2025-08-05 13:59:57 - src.core.message_processor.2959327704176 - INFO - 设置间隔时间为: 60秒 (1分钟)
2025-08-05 13:59:57 - src.core.message_processor.2959327704176 - INFO - 收到原始消息: {"data": {"content": "1", "conversation_id": "S:1688854710715177_7881299718050125", "sender": "7881299718050125", "sender_name": "测试用户", "content_type": 0, "local_id": "msg_1754373597914", "server_id": "server_1754373597914"}}
2025-08-05 13:59:57 - src.core.message_processor.2959327704176 - INFO - 机器人名字: 测试机器人
2025-08-05 13:59:57 - src.core.message_processor.2959327704176 - INFO - 消息类型: 0
2025-08-05 13:59:57 - src.core.message_processor.2959327704176 - INFO - 消息处理开始 | 发送者: 测试用户(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 1
2025-08-05 13:59:57 - src.core.message_processor.2959327704176 - INFO - 消息类型: 私聊
2025-08-05 13:59:57 - src.core.message_processor.2959327704176 - INFO - [DEBUG] 检查用户 7881299718050125 创建会话条件，当前间隔设置: 60秒
2025-08-05 13:59:57 - src.core.message_processor.2959327704176 - INFO - [DEBUG] 数据库检查结果: True
2025-08-05 13:59:57 - src.core.message_processor.2959327704176 - INFO - 用户 7881299718050125 可以创建新会话（无活跃会话且满足间隔条件）
2025-08-05 13:59:57 - src.core.message_processor.2959327704176 - INFO - 数据库创建会话成功: 7881299718050125
2025-08-05 13:59:57 - src.core.message_processor.2959327704176 - INFO - 从文本消息重新创建会话并发送欢迎消息: 7881299718050125, 会话ID: 7881299718050125
2025-08-05 14:01:02 - src.core.message_processor.2523377407168 - INFO - 日志文件已配置: F:\soft\python\wechat\新建文件夹\客服选项选择\logs\wechat_bot_20250805.log
2025-08-05 14:01:02 - src.core.message_processor.2523377407168 - INFO - 消息处理器日志系统初始化完成
2025-08-05 14:01:02 - src.core.message_processor.2523377407168 - INFO - 设置间隔时间为: 60秒 (1分钟)
2025-08-05 14:01:02 - src.core.message_processor.2523377407168 - INFO - 设置间隔时间为: 1800秒 (30分钟)
