# -*- coding: utf-8 -*-
"""
测试文件发送特殊格式功能
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.core.wechat_handler import WeChatHandler
from src.core.message_processor import MessageProcessor


def test_file_send_format():
    """测试文件发送特殊格式"""
    print("测试文件发送特殊格式功能...")
    
    try:
        # 创建微信处理器和消息处理器
        wechat_handler = WeChatHandler()
        message_processor = MessageProcessor(wechat_handler)
        
        # 测试文件发送格式解析
        test_reply_content = """wait_reply=正在为您发送文件...
file_path=C:/images/example.jpg"""
        
        print(f"测试回复内容:\n{test_reply_content}")
        
        # 解析特殊格式参数
        params = message_processor._parse_special_reply_params(test_reply_content)
        print(f"解析到的参数: {params}")
        
        # 验证参数
        expected_params = {
            'wait_reply': '正在为您发送文件...',
            'file_path': 'C:/images/example.jpg'
        }
        
        if params == expected_params:
            print("✅ 文件发送参数解析正确")
        else:
            print(f"❌ 文件发送参数解析错误，期望: {expected_params}, 实际: {params}")
            return False
        
        # 测试不同文件类型
        test_cases = [
            {
                'content': """wait_reply=发送图片中...
file_path=/path/to/image.png""",
                'expected': {
                    'wait_reply': '发送图片中...',
                    'file_path': '/path/to/image.png'
                }
            },
            {
                'content': """wait_reply=发送文档中...
file_path=D:\\documents\\report.pdf""",
                'expected': {
                    'wait_reply': '发送文档中...',
                    'file_path': 'D:\\documents\\report.pdf'
                }
            },
            {
                'content': """file_path=./local/file.txt""",
                'expected': {
                    'file_path': './local/file.txt'
                }
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n测试用例 {i}:")
            print(f"内容: {test_case['content']}")
            
            params = message_processor._parse_special_reply_params(test_case['content'])
            print(f"解析结果: {params}")
            
            if params == test_case['expected']:
                print(f"✅ 测试用例 {i} 通过")
            else:
                print(f"❌ 测试用例 {i} 失败")
                return False
        
        # 测试混合格式（包含普通文本和文件发送）
        test_mixed_content = """这是一些说明文字
wait_reply=正在准备文件...
file_path=/uploads/manual.pdf
请查收附件"""
        
        print(f"\n测试混合内容:\n{test_mixed_content}")
        
        mixed_params = message_processor._parse_special_reply_params(test_mixed_content)
        normal_text = message_processor._extract_normal_text_from_special_reply(test_mixed_content)
        
        print(f"解析到的混合参数: {mixed_params}")
        print(f"提取的普通文本: {normal_text}")
        
        expected_mixed_params = {
            'wait_reply': '正在准备文件...',
            'file_path': '/uploads/manual.pdf'
        }
        expected_normal_text = "这是一些说明文字\n请查收附件"
        
        if mixed_params == expected_mixed_params and normal_text == expected_normal_text:
            print("✅ 混合内容解析正确")
        else:
            print(f"❌ 混合内容解析错误")
            print(f"期望参数: {expected_mixed_params}, 实际: {mixed_params}")
            print(f"期望文本: {expected_normal_text}, 实际: {normal_text}")
            return False
        
        print("\n🎉 所有文件发送格式测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_wechat_handler_send_image():
    """测试微信处理器的send_image方法"""
    print("\n测试微信处理器的send_image方法...")
    
    try:
        wechat_handler = WeChatHandler()
        
        # 测试发送图片/文件
        test_files = [
            "C:/images/test.jpg",
            "/path/to/document.pdf",
            "./local/file.txt"
        ]
        
        for file_path in test_files:
            print(f"测试发送文件: {file_path}")
            result = wechat_handler.send_image("test_conversation", file_path)
            
            if result:
                print(f"✅ 发送 {file_path} 成功")
            else:
                print(f"❌ 发送 {file_path} 失败")
                return False
        
        print("✅ send_image方法测试通过")
        return True
            
    except Exception as e:
        print(f"❌ send_image方法测试失败: {e}")
        return False


def test_all_special_formats():
    """测试所有特殊格式的兼容性"""
    print("\n测试所有特殊格式的兼容性...")
    
    try:
        wechat_handler = WeChatHandler()
        message_processor = MessageProcessor(wechat_handler)
        
        # 测试所有格式混合
        all_formats_content = """wait_reply=处理中，请稍候...
title=测试链接
desc=这是一个测试链接
url=https://example.com
img=https://example.com/image.jpg
user_id=support_001
file_path=/files/attachment.pdf"""
        
        print(f"测试所有格式混合:\n{all_formats_content}")
        
        params = message_processor._parse_special_reply_params(all_formats_content)
        print(f"解析到的所有参数: {params}")
        
        expected_all_params = {
            'wait_reply': '处理中，请稍候...',
            'title': '测试链接',
            'desc': '这是一个测试链接',
            'url': 'https://example.com',
            'img': 'https://example.com/image.jpg',
            'user_id': 'support_001',
            'file_path': '/files/attachment.pdf'
        }
        
        if params == expected_all_params:
            print("✅ 所有格式兼容性测试通过")
            return True
        else:
            print(f"❌ 格式兼容性测试失败")
            print(f"期望: {expected_all_params}")
            print(f"实际: {params}")
            return False
            
    except Exception as e:
        print(f"❌ 兼容性测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("=" * 60)
    print("文件发送特殊格式功能测试")
    print("=" * 60)
    
    # 测试格式解析
    format_test_ok = test_file_send_format()
    
    # 测试发送方法
    send_test_ok = test_wechat_handler_send_image()
    
    # 测试兼容性
    compatibility_test_ok = test_all_special_formats()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print(f"格式解析测试: {'✅ 通过' if format_test_ok else '❌ 失败'}")
    print(f"发送方法测试: {'✅ 通过' if send_test_ok else '❌ 失败'}")
    print(f"兼容性测试: {'✅ 通过' if compatibility_test_ok else '❌ 失败'}")
    
    if all([format_test_ok, send_test_ok, compatibility_test_ok]):
        print("\n🎉 所有测试通过！文件发送功能已成功添加！")
        print("\n支持的特殊格式:")
        print("1. 链接卡片: wait_reply= + title= + desc= + url= + img=")
        print("2. 用户卡片: wait_reply= + user_id=")
        print("3. 文件发送: wait_reply= + file_path=")
        print("\n使用方法:")
        print("在选项回复内容中使用以下格式:")
        print("wait_reply=等待消息（可选）")
        print("file_path=文件路径")
        return True
    else:
        print("\n❌ 部分测试失败，请检查问题。")
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        input("\n按回车键退出...")
    sys.exit(0 if success else 1)
