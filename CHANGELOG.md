# 更新日志

## v2.4.0 - 私聊专用版本 (2025-07-17)

### 🎉 重大更新
- **私聊专用处理**: 机器人现在只处理私聊消息，完全忽略群聊消息
- **私聊配置**: 新增 `private_chat` 配置键，专门用于私聊机器人配置
- **简化会话管理**: 私聊会话ID直接使用发送者ID，简化会话管理逻辑

### 🔧 核心修改
- **消息过滤**: 只处理消息ID以 `S:` 开头的私聊消息
- **配置结构**: 支持 `private_chat` 和空字符串作为默认配置键
- **界面更新**: 更新窗口标题为"微信私聊选项选择机器人"
- **日志优化**: 所有日志信息更新为私聊相关描述

### 📝 文档更新
- 新增 `README-PRIVATE.md`: 私聊版本专用说明文档
- 新增 `USAGE-PRIVATE.md`: 详细使用指南
- 新增 `config-private.json`: 私聊专用配置文件示例
- 新增 `test_private_chat.py`: 私聊功能测试脚本

### 🛠️ 技术改进
- **消息处理器**: 重构消息处理逻辑，移除群聊相关代码
- **配置管理**: 更新默认配置，包含私聊配置模板
- **界面兼容**: 保留群管理界面用于兼容性，但标注为私聊无需配置
- **错误处理**: 优化错误日志，明确区分私聊和群聊处理

### 🔄 兼容性
- 保持原有API接口不变，确保向后兼容
- 原有配置文件仍然可用，自动适配私聊模式
- GUI界面保持原有布局，仅更新标签和说明

### ⚠️ 重要变更
- **不再处理群聊消息**: 所有群聊消息将被忽略
- **会话ID变更**: 私聊会话ID从 `{room_id}_{user_id}` 改为 `{user_id}`
- **配置键变更**: 推荐使用 `private_chat` 作为配置键

---

## v2.3.0 - 新增视频发送功能 (2025-07-17)

### ✨ 新增功能

- **视频发送特殊格式**: 新增 `video_path=` 参数支持，可以发送各种格式的视频文件
- **send_video方法**: 在微信处理器中新增视频发送方法
- **多格式支持**: 支持 .mp4, .avi, .mov, .mkv, .webm 等主流视频格式

### 🔧 技术改进

- **参数解析增强**: 支持解析 `video_path=` 参数
- **错误处理**: 视频发送失败时自动降级为文本消息
- **兼容性测试**: 确保与现有所有特殊格式完全兼容

---

## v2.2.0 - 新增小程序发送功能 (2025-07-17)

### ✨ 新增功能

- **小程序特殊格式**: 新增完整的小程序发送支持，包含11个参数
- **send_miniapp方法**: 在微信处理器中新增小程序发送方法
- **多参数解析**: 支持解析 `aes_key=`, `file_id=`, `size=`, `appicon=`, `appid=`, `appname=`, `conversation_id=`, `page_path=`, `title=`, `username=` 参数

### 🔧 技术改进

- **参数解析增强**: 大幅扩展参数解析能力，支持复杂的小程序格式
- **错误处理**: 小程序发送失败时自动降级为文本消息
- **测试覆盖**: 新增专门的小程序功能测试和综合测试更新

---

## v2.1.0 - 新增文件发送功能 (2025-07-17)

### ✨ 新增功能

- **文件发送特殊格式**: 新增 `file_path=` 参数支持，可以发送图片和文件
- **send_image方法**: 在微信处理器中新增文件发送方法
- **格式兼容性**: 完全兼容现有的链接卡片和用户卡片格式

### 🔧 技术改进

- **参数解析增强**: 支持解析 `file_path=` 参数
- **错误处理**: 文件发送失败时自动降级为文本消息
- **测试覆盖**: 新增专门的文件发送功能测试

### 📋 支持的特殊格式

1. **链接卡片**: `wait_reply=` + `title=` + `desc=` + `url=` + `img=`
2. **用户卡片**: `wait_reply=` + `user_id=`
3. **文件发送**: `wait_reply=` + `file_path=`
4. **视频发送**: `wait_reply=` + `video_path=` ⭐ 新增
5. **小程序**: `wait_reply=` + `aes_key=` + `file_id=` + `size=` + `appicon=` + `appid=` + `appname=` + `conversation_id=` + `page_path=` + `title=` + `username=`
6. **混合格式**: 普通文本 + 特殊参数

### 🧪 测试文件

- `test_video_send.py`: 视频发送功能专项测试 ⭐ 新增
- `test_all_special_formats.py`: 所有特殊格式综合测试（已更新）

---

## v2.0.0 - 模块化重构 (2025-07-17)

### 🎉 重大更新

- **完全模块化重构**: 将原始的单文件项目重构为清晰的模块化架构
- **改进的代码组织**: 按功能职责分离代码，提高可维护性

### 📁 新的项目结构

```
src/
├── core/           # 核心业务逻辑
├── gui/            # 用户界面模块  
├── config/         # 配置管理
└── utils/          # 工具类模块
```

### ✨ 新增功能

- **模块化架构**: 清晰的代码分层和职责分离
- **依赖管理**: 更好的依赖隔离和模拟支持
- **测试支持**: 添加模块测试脚本
- **文档完善**: 详细的README和项目说明

### 🔧 技术改进

- **单例模式**: 线程安全的单例装饰器
- **配置管理**: 专门的配置管理器类
- **日志系统**: 改进的GUI日志处理器
- **错误处理**: 更好的异常处理和错误恢复

### 📦 模块详情

#### 核心模块 (src/core/)
- `wechat_handler.py`: 微信连接和基础操作
- `message_processor.py`: 消息处理和回复逻辑

#### GUI模块 (src/gui/)
- `main_window.py`: 主窗口和应用程序入口
- `room_management.py`: 群管理选项卡
- `option_configuration.py`: 选项配置选项卡
- `log_display.py`: 日志显示选项卡

#### 配置模块 (src/config/)
- `config_manager.py`: 配置文件的读取、保存和管理

#### 工具模块 (src/utils/)
- `decorators.py`: 装饰器（单例模式等）
- `logging_handler.py`: 自定义日志处理器
- `mock_*.py`: 测试用的模拟模块

### 🧪 测试和验证

- **模块导入测试**: 验证所有模块可正确导入
- **基本功能测试**: 验证核心功能正常工作
- **项目结构测试**: 验证文件结构完整性
- **集成测试**: 验证模块间协作正常

### 📋 兼容性

- **配置兼容**: 完全兼容原版本的配置文件
- **功能兼容**: 保持所有原有功能不变
- **界面兼容**: GUI界面和操作方式保持一致

### 🔄 迁移说明

从v1.x迁移到v2.0.0：

1. 原始文件已备份为 `main_backup.py`
2. 配置文件 `config.json` 无需修改
3. 新的启动文件为 `app.py`
4. 运行 `python test_modules.py` 验证安装

### 🛠️ 开发改进

- **代码可读性**: 模块化后代码更易理解
- **维护性**: 每个模块职责明确，易于维护
- **扩展性**: 新功能可作为独立模块添加
- **测试性**: 可单独测试各个模块

### 📚 文档更新

- 新增 `README.md`: 详细的项目说明
- 新增 `requirements.txt`: 依赖包列表
- 新增 `test_modules.py`: 模块测试脚本
- 新增 `CHANGELOG.md`: 版本更新记录

---

## v1.x - 原始版本

### 功能特性

- 企业微信自动回复
- 树状选项菜单配置
- 按群分组的选项配置
- 特殊回复格式支持
- GUI界面管理

### 文件结构

- `main.py`: 单文件包含所有功能
- `config.json`: 配置文件

---

**注意**: 原始版本已备份为 `main_backup.py`，如需回退可使用该文件。
