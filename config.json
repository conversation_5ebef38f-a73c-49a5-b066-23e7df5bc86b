{"group_option_configs": {"private_chat": {"auto_reply": "您好！欢迎咨询，请问您需要以下哪项服务？请直接回复对应数字：\n1.查询快递物流信息\n2.投诉/反馈问题\n3.联系人工客服", "tree_options": {"1": {"text": "查询快递物流信息", "reply": "您好！请提供您的快递单号，我们将为您查询物流信息。", "type": "final", "children": {}}, "2": {"text": "投诉/反馈问题", "reply": "很抱歉给您带来不便，请选择具体问题类型：", "type": "final", "children": {}}, "3": {"text": "联系人工客服", "reply": "wait_reply=正在为您转接人工客服，请稍候...\naes_key=68a61fe893a14f4b9f5b8b06a53404f6\nappicon=http://mmbiz.qpic.cn/sz_mmbiz_png/iaCrlo1rtEDKdOOEYjeAibS9kRtkqXfZM3TRaGmG5aTnSs6QkZv92cPjRaXbZtTHGdncC8cXkztziaYtQNKaVwHhA/640?wx_fmt=png&wxfrom=200\nappname=银探-示例小程序\nfile_id=3069020102046230600201000204211c032902030f424102044ac06db4020468848800042436623236656165372d383035332d346563322d616333362d613634323331386563633764020100020303cb00041031707d814f740055715c414000e2a9f30201010201000400\npage_path=pages/index/index.html\nsize=248573\ntitle=精彩内容分享\nusername=gh_310c3cad0c90@app", "type": "final", "children": {}}}, "interval_minutes": 1}, "": {"auto_reply": "您好！欢迎咨询，请问您需要以下哪项服务？请直接回复对应数字：\n1.查询快递物流信息\n2.投诉/反馈问题\n3.联系人工客服", "tree_options": {"1": {"text": "查询快递物流信息", "reply": "您好！请提供您的快递单号，我们将为您查询物流信息。", "type": "final", "children": {}}, "2": {"text": "投诉/反馈问题", "reply": "很抱歉给您带来不便，请选择具体问题类型：", "type": "final", "children": {}}, "3": {"text": "联系人工客服", "reply": "wait_reply=正在为您转接人工客服，请稍候...\naes_key=68a61fe893a14f4b9f5b8b06a53404f6\nappicon=http://mmbiz.qpic.cn/sz_mmbiz_png/iaCrlo1rtEDKdOOEYjeAibS9kRtkqXfZM3TRaGmG5aTnSs6QkZv92cPjRaXbZtTHGdncC8cXkztziaYtQNKaVwHhA/640?wx_fmt=png&wxfrom=200\nappname=银探-示例小程序\nfile_id=3069020102046230600201000204211c032902030f424102044ac06db4020468848800042436623236656165372d383035332d346563322d616333362d613634323331386563633764020100020303cb00041031707d814f740055715c414000e2a9f30201010201000400\npage_path=pages/index/index.html\nsize=248573\ntitle=精彩内容分享\nusername=gh_310c3cad0c90@app", "type": "final", "children": {}}}, "interval_minutes": 1}, "R:1970326585985933": {"auto_reply": "请问您需要咨询以下哪项业务？请直接回复对应数字：\n1.查询快递物流信息\n2.投诉/反馈问题（丢件/破损/延误等）\n3.联系人工客服\n4.网点/快递员联系方式查询\n5.修改收件地址或时间", "option_replies": {"1": "输入 查快递+单号 查询快递", "2": "加客服微信1", "3": "加客服微信2", "4": "加客服微信3", "5": "加客服微信4"}}, "R:10893113709328015": {"auto_reply": "请问您需要咨询以下哪项业务？请直接回复对应数字：\n1.查询快递物流信息\n2.投诉/反馈问题（丢件/破损/延误等）\n3.联系人工客服\n4.网点/快递员联系方式查询\n5.修改收件地址或时间", "option_replies": {"1": "加客服微信1", "2": "加客服微信2", "3": "加客服微信3", "4": "加客服微信4", "5": "加客服微信5"}}, "R:10881056632797218": {"auto_reply": "请问您需要咨询以下哪项业务？请直接回复对应数字：\n1.查询快递物流信息\n2.投诉/反馈问题（丢件/破损/延误等）\n3.联系人工客服", "tree_options": {"1": {"text": "查询快递物流信息", "reply": "您好！请提供您的快递单号，我们将为您查询物流信息。", "type": "final", "children": {}}, "2": {"text": "投诉/反馈问题", "reply": "很抱歉给您带来不便，请选择具体问题类型：", "type": "menu", "children": {"2.1": {"text": "丢件问题", "reply": "请提供您的快递单号和详细情况，我们将立即为您处理丢件问题。", "type": "final", "children": {}}, "2.2": {"text": "破损问题", "reply": "请拍照上传破损情况并提供快递单号，我们将为您申请赔偿。", "type": "final", "children": {}}, "2.3": {"text": "延误问题", "reply": "请提供快递单号，我们将查询延误原因并为您跟进处理。", "type": "final", "children": {}}}}, "3": {"text": "联系人工客服", "reply": "wait_reply=正在为您转接人工客服，请稍候...\naes_key=7e2b31c325234382bcaa1c74b2f3e7d6\n appicon=http://mmbiz.qpic.cn/sz_mmbiz_png/iaCrlo1rtEDKdOOEYjeAibS9kRtkqXfZM3TRaGmG5aTnSs6QkZv92cPjRaXbZtTHGdncC8cXkztziaYtQNKaVwHhA/640?wx_fmt=png&wxfrom=200\nappid=wxeb51fb5f546fefda\nappname=银探-示例小程序\nfile_id=3069020102046230600201000204211c032902030f42420204f663607a02046870cb90042435373864393532332d346233642d343730332d383538652d333765616437366134303939020100020303cb00041031707d814f740055715c414000e2a9f30201010201000400\npage_path=pages/index/index.html\nsize=248573\ntitle=精彩内容分享\nusername=gh_310c3cad0c90@app", "type": "final", "children": {}}}}}, "paused_rooms": [], "monitored_rooms": []}