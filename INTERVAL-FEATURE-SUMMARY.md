# 微信私聊机器人 - 间隔时间循环功能

## 功能概述

实现了完整的间隔时间循环机制，允许用户在完成一次选项选择后，经过设置的间隔时间，再次发送文本消息时重新创建会话，形成循环使用。

## 核心功能

### 1. 会话创建机制
- **系统消息创建**: 只有系统消息（好友验证、客服名片进入）可以创建新会话
- **文本消息重新创建**: 超过间隔时间后，文本消息可以重新创建会话
- **循环机制**: 完成选择 → 等待间隔时间 → 重新创建会话 → 再次选择

### 2. 间隔时间管理
- **可配置间隔**: 通过GUI界面设置间隔时间（分钟）
- **完成时间记录**: 记录用户每次完成选择的时间
- **智能检查**: 自动检查是否超过间隔时间
- **默认设置**: 默认30分钟间隔时间

### 3. 用户体验优化
- **无缝循环**: 用户可以重复使用机器人服务
- **时间控制**: 避免频繁打扰，控制服务频率
- **状态管理**: 完整的会话状态跟踪

## 技术实现

### 1. 消息处理器增强 (`message_processor.py`)

#### 新增属性
```python
# 间隔时间管理
self.user_last_completion = {}  # 用户最后完成选择的时间记录
self.interval_seconds = 1800    # 间隔时间设置（秒），默认30分钟
```

#### 核心方法
```python
def set_interval_seconds(self, seconds: int):
    """设置间隔时间"""
    
def _can_recreate_session(self, session_id: str, current_time: float) -> bool:
    """检查是否可以重新创建会话"""
    
def _create_new_session_from_text(self, wechat_instance, private_config, ...):
    """从文本消息创建新会话"""
```

### 2. 文本消息处理逻辑更新

#### 原逻辑
```python
# 如果用户没有会话，忽略消息
if not session:
    self.logger.info(f"用户 {from_wxid} 没有活跃会话，忽略文本消息")
    return
```

#### 新逻辑
```python
# 如果用户没有会话，检查是否可以重新创建会话
if not session:
    if self._can_recreate_session(session_id, current_time):
        self.logger.info(f"用户 {from_wxid} 超过间隔时间，重新创建会话")
        self._create_new_session_from_text(...)
        return
    else:
        self.logger.info(f"用户 {from_wxid} 没有活跃会话且未超过间隔时间，忽略文本消息")
        return
```

### 3. 选择完成时记录时间

```python
if option_type == 'final':
    # 最终选项，结束对话
    current_time = time.time()
    self.user_last_completion[session_id] = current_time  # 记录完成时间
    del self.user_sessions[session_id]
    self.logger.info(f"对话结束，清理会话: {session_id}, 记录完成时间: {current_time}")
```

### 4. GUI界面增强 (`main_window.py`)

#### 间隔时间设置组件
```python
# 添加间隔时间设置
interval_frame = ttk.Frame(control_frame)
interval_frame.pack(side=tk.RIGHT, padx=5)

ttk.Label(interval_frame, text="重新创建会话间隔:").pack(side=tk.LEFT, padx=(0, 5))

self.interval_var = tk.StringVar(value="30")  # 默认30分钟
interval_spinbox = ttk.Spinbox(
    interval_frame,
    from_=1,
    to=1440,  # 最大24小时
    width=8,
    textvariable=self.interval_var,
    command=self.on_interval_changed
)
```

#### 事件处理
```python
def on_interval_changed(self, event=None):
    """间隔时间改变时的处理"""
    try:
        minutes = int(self.interval_var.get())
        seconds = minutes * 60
        
        # 更新消息处理器的间隔时间
        if self.message_processor:
            self.message_processor.set_interval_seconds(seconds)
    except ValueError:
        self.logger.warning("间隔时间输入无效，使用默认值30分钟")
```

## 使用流程

### 1. 首次使用
1. 用户通过好友验证或客服名片进入
2. 系统消息触发，自动创建会话
3. 发送欢迎消息和选项菜单
4. 用户进行选项选择
5. 完成选择后会话结束，记录完成时间

### 2. 循环使用
1. 用户在间隔时间内发送消息 → 被忽略
2. 用户在间隔时间后发送消息 → 重新创建会话
3. 自动发送欢迎消息和选项菜单
4. 用户再次进行选项选择
5. 完成后更新完成时间，等待下次循环

### 3. 管理员配置
1. 在GUI界面右上角设置间隔时间
2. 支持1分钟到24小时的设置范围
3. 实时生效，无需重启程序

## 配置示例

### 界面设置
- **间隔时间**: 30分钟（默认）
- **范围**: 1-1440分钟（1分钟到24小时）
- **位置**: 主界面右上角控制区域

### 日志示例
```
2025-07-23 22:35:36 - INFO - 设置间隔时间为: 1800秒 (30分钟)
2025-07-23 22:35:36 - INFO - 向新联系人发送欢迎消息并创建会话: user123, 会话ID: user123
2025-07-23 22:35:45 - INFO - 对话结束，清理会话: user123, 记录完成时间: 1642781345.123
2025-07-23 22:40:00 - INFO - 用户 user123 没有活跃会话且未超过间隔时间，忽略文本消息
2025-07-23 23:10:00 - INFO - 用户 user123 超过间隔时间，重新创建会话
2025-07-23 23:10:00 - INFO - 从文本消息重新创建会话并发送欢迎消息: user123, 会话ID: user123
```

## 优势特点

### 1. 用户友好
- **无需重新添加好友**: 用户可以直接发送消息重新开始
- **智能时间控制**: 避免频繁打扰用户
- **一致的体验**: 每次重新开始都是完整的选项流程

### 2. 管理便捷
- **可视化配置**: GUI界面直接设置间隔时间
- **实时调整**: 无需重启程序即可生效
- **灵活控制**: 支持1分钟到24小时的广泛范围

### 3. 系统稳定
- **内存管理**: 及时清理过期会话和记录
- **状态跟踪**: 完整的用户状态管理
- **异常处理**: 完善的错误处理机制

### 4. 业务价值
- **提高复用率**: 用户可以多次使用服务
- **控制服务频率**: 避免过度使用
- **提升用户体验**: 便捷的重复使用机制

## 应用场景

### 1. 客服咨询
- 用户完成一次咨询后，30分钟后可以再次咨询
- 避免频繁打扰，但保持服务可用性

### 2. 信息查询
- 用户查询信息后，可以在间隔时间后再次查询
- 适合需要定期查询的业务场景

### 3. 服务预约
- 用户完成预约后，可以在间隔时间后进行新的预约
- 控制预约频率，避免系统过载

## 技术特点

### 1. 高效实现
- 最小化内存使用
- 精确的时间控制
- 快速的状态检查

### 2. 可扩展性
- 支持不同用户的不同间隔时间
- 可以扩展为更复杂的时间策略
- 支持多种触发条件

### 3. 可维护性
- 清晰的代码结构
- 完整的日志记录
- 模块化的设计

这个间隔时间循环功能为微信私聊机器人提供了完整的循环使用机制，既保证了用户体验，又实现了合理的使用频率控制。
