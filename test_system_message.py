# -*- coding: utf-8 -*-
"""
测试系统消息功能 - 验证好友验证和客服名片进入的处理
"""

import sys
import os
import json
import time

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_system_message_detection():
    """测试系统消息检测逻辑"""
    print("=== 测试系统消息检测逻辑 ===")
    
    try:
        from src.core.message_processor import MessageProcessor
        from src.core.wechat_handler import WeChatHandler
        
        # 创建消息处理器实例
        wechat_handler = WeChatHandler()
        processor = MessageProcessor(wechat_handler, None)
        
        # 测试用例
        test_cases = [
            {
                "description": "好友验证通过消息",
                "data": {
                    "type": "friend_verify",
                    "content": "用户通过了好友验证",
                    "conversation_id": "S:1234567890",
                    "from_wxid": "user123"
                },
                "expected": True
            },
            {
                "description": "客服名片进入消息",
                "data": {
                    "type": "service_card",
                    "content": "用户通过客服名片进入对话",
                    "conversation_id": "S:1234567890",
                    "from_wxid": "user456"
                },
                "expected": True
            },
            {
                "description": "新用户开始对话",
                "data": {
                    "type": "new_chat",
                    "content": "新用户开始对话",
                    "conversation_id": "S:1234567890",
                    "from_wxid": "user789"
                },
                "expected": True
            },
            {
                "description": "普通系统消息",
                "data": {
                    "type": "system",
                    "content": "系统通知消息",
                    "conversation_id": "S:1234567890",
                    "from_wxid": "system"
                },
                "expected": False
            },
            {
                "description": "群聊系统消息",
                "data": {
                    "type": "friend_verify",
                    "content": "用户通过了好友验证",
                    "conversation_id": "R:1234567890",
                    "from_wxid": "user123"
                },
                "expected": True  # 检测为新联系人消息，但会被私聊过滤拒绝
            }
        ]
        
        for test_case in test_cases:
            result = processor._is_new_contact_message(test_case["data"])
            status = "✅ 通过" if result == test_case["expected"] else "❌ 失败"
            print(f"{status} {test_case['description']}: {result}")
            
    except Exception as e:
        print(f"❌ 系统消息检测测试失败: {e}")

def test_session_management():
    """测试会话管理功能"""
    print("\n=== 测试会话管理功能 ===")
    
    try:
        from src.core.message_processor import MessageProcessor
        from src.core.wechat_handler import WeChatHandler
        
        # 创建消息处理器实例
        wechat_handler = WeChatHandler()
        processor = MessageProcessor(wechat_handler, None)
        
        # 测试会话创建
        session_id = "test_user_123"
        room_wxid = "S:1234567890"
        current_time = time.time()
        
        # 第一次调用应该返回None（没有会话）
        session = processor._get_or_create_session(session_id, room_wxid, current_time)
        if session is None:
            print("✅ 新用户会话检测正确（返回None）")
        else:
            print("❌ 新用户会话检测失败")
        
        # 手动创建会话
        processor.user_sessions[session_id] = {
            'start_time': current_time,
            'waiting_for_option': True,
            'chat_id': room_wxid,
            'current_path': [],
            'source': 'system_message'
        }
        
        # 再次调用应该返回会话
        session = processor._get_or_create_session(session_id, room_wxid, current_time)
        if session is not None:
            print("✅ 现有会话获取正确")
        else:
            print("❌ 现有会话获取失败")
        
        # 测试会话超时
        old_time = current_time - 400  # 超过5分钟（300秒）
        processor.user_sessions[session_id]['start_time'] = old_time
        
        session = processor._get_or_create_session(session_id, room_wxid, current_time)
        if session is None and session_id not in processor.user_sessions:
            print("✅ 会话超时清理正确")
        else:
            print("❌ 会话超时清理失败")
            
    except Exception as e:
        print(f"❌ 会话管理测试失败: {e}")

def test_callback_registration():
    """测试回调注册功能"""
    print("\n=== 测试回调注册功能 ===")
    
    try:
        from src.core.wechat_handler import WeChatHandler
        
        # 创建微信处理器实例
        wechat_handler = WeChatHandler()
        
        # 检查是否有新的注册方法
        import inspect
        register_method = wechat_handler.register_message_callback
        signature = inspect.signature(register_method)
        
        if len(signature.parameters) >= 2:
            print("✅ 消息回调注册方法支持多个回调参数")
        else:
            print("❌ 消息回调注册方法参数不足")
            
        # 检查是否有系统消息常量
        try:
            import ntwork
            if hasattr(ntwork, 'MT_RECV_SYS_MSG'):
                print("✅ 系统消息常量存在")
            else:
                print("⚠️  系统消息常量不存在（可能使用模拟版本）")
        except ImportError:
            print("⚠️  使用模拟ntwork版本")
            
    except Exception as e:
        print(f"❌ 回调注册测试失败: {e}")

def main():
    """主测试函数"""
    print("微信私聊选项选择机器人 - 系统消息功能测试")
    print("=" * 60)
    
    test_system_message_detection()
    test_session_management()
    test_callback_registration()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("\n新增功能总结：")
    print("1. ✅ 添加了系统消息监控 (MT_RECV_SYS_MSG)")
    print("2. ✅ 实现了新联系人检测逻辑")
    print("3. ✅ 支持好友验证通过自动发送欢迎消息")
    print("4. ✅ 支持客服名片进入自动发送欢迎消息")
    print("5. ✅ 完善了会话管理和超时处理")
    print("6. ✅ 添加了定期清理过期会话功能")
    print("7. ✅ 文本消息完整选择后自动结束会话")

if __name__ == "__main__":
    main()
