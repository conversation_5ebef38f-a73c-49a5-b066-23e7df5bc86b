#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件上传功能
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    import ttkbootstrap as ttk
except ImportError:
    from src.utils import mock_ttkbootstrap as ttk

def test_file_upload():
    """测试文件上传功能"""
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("文件上传功能测试")
    root.geometry("400x300")
    
    # 创建文本框模拟回复内容输入
    text_frame = ttk.Frame(root)
    text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    ttk.Label(text_frame, text="回复内容:").pack(anchor=tk.W)
    
    reply_text = tk.Text(text_frame, height=10, wrap=tk.WORD)
    scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=reply_text.yview)
    reply_text.configure(yscrollcommand=scrollbar.set)
    
    reply_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    # 文件上传按钮
    button_frame = ttk.Frame(root)
    button_frame.pack(fill=tk.X, padx=10, pady=5)
    
    def upload_txt_file():
        """直接上传txt文件并覆盖回复内容"""
        from tkinter import filedialog

        file_path = filedialog.askopenfilename(
            title="选择txt文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if not file_path:
            return

        try:
            # 尝试UTF-8编码读取
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            try:
                # 尝试GBK编码读取
                with open(file_path, 'r', encoding='gbk') as f:
                    content = f.read()
            except Exception as e:
                tk.messagebox.showerror("错误", f"读取文件失败: {str(e)}")
                return
        except Exception as e:
            tk.messagebox.showerror("错误", f"读取文件失败: {str(e)}")
            return

        if not content.strip():
            tk.messagebox.showwarning("警告", "文件内容为空！")
            return

        # 覆盖回复内容
        reply_text.delete('1.0', tk.END)
        reply_text.insert('1.0', content)

        # 显示成功信息
        import os
        filename = os.path.basename(file_path)
        tk.messagebox.showinfo("成功", f"已成功上传并覆盖回复内容！\n文件: {filename}")
    
    ttk.Button(button_frame, text="上传txt文件", command=upload_txt_file,
              bootstyle="info").pack(side=tk.LEFT)
    
    # 清空按钮
    def clear_text():
        reply_text.delete('1.0', tk.END)
    
    ttk.Button(button_frame, text="清空", command=clear_text, 
              bootstyle="warning").pack(side=tk.LEFT, padx=(10, 0))
    
    root.mainloop()

if __name__ == "__main__":
    test_file_upload()
