# 微信私聊选项选择机器人

## 概述

这是一个专门针对企业微信私聊的自动回复机器人，支持树状选项菜单配置和多种特殊回复格式。

## 主要功能

### 🔥 核心特性
- **私聊专用**: 只处理私聊消息，忽略所有群聊消息
- **选项菜单**: 支持数字选项选择的自动回复
- **会话管理**: 智能的用户会话状态管理
- **特殊格式**: 支持链接卡片、图片、视频、小程序等多种回复格式
- **GUI界面**: 友好的图形化配置界面

### 📋 支持的回复格式
1. **普通文本回复**
2. **链接卡片**: `title=标题\ndesc=描述\nurl=链接\nimg=图片URL`
3. **图片/文件**: `file_path=文件路径`
4. **视频**: `video_path=视频路径`
5. **用户卡片**: `user_id=用户ID`
6. **小程序**: 支持完整的小程序参数配置
7. **等待回复**: `wait_reply=等待消息` + 其他格式组合

## 配置说明

### 私聊配置结构
```json
{
    "group_option_configs": {
        "private_chat": {
            "auto_reply": "欢迎消息和选项菜单",
            "option_replies": {
                "1": "选项1的回复内容",
                "2": "选项2的回复内容",
                ...
            }
        }
    }
}
```

### 配置文件
- `config-private.json`: 私聊机器人专用配置文件
- `config.json`: 原始配置文件（兼容性保留）

## 使用方法

### 1. 启动程序
```bash
python app.py
```

### 2. 配置选项
- 在"选项配置"选项卡中设置自动回复内容和选项
- 支持树状菜单结构配置
- 可以配置特殊格式的回复内容

### 3. 启动监控
- 点击"开始监控"按钮
- 程序将自动处理所有私聊消息
- 在"运行日志"中查看处理状态

## 主要修改

### 相比群聊版本的变化
1. **消息过滤**: 只处理以 `S:` 开头的私聊消息ID
2. **配置简化**: 移除群聊相关的监控列表配置
3. **会话管理**: 简化为基于用户ID的会话管理
4. **界面调整**: 更新界面标题和说明文字
5. **日志优化**: 所有日志信息改为私聊相关描述

### 技术细节
- 私聊消息ID格式: `S:xxxxxxxxx`
- 群聊消息ID格式: `R:xxxxxxxxx` (已忽略)
- 会话ID: 直接使用发送者ID
- 配置键: 使用 `private_chat` 或空字符串作为默认配置

## 注意事项

1. **企业微信**: 需要企业微信环境，不支持个人微信
2. **权限要求**: 需要机器人账号有发送消息的权限
3. **消息类型**: 只处理文本消息（content_type: 0, 2）
4. **会话超时**: 用户会话有超时机制，超时后重新发送欢迎消息

## 文件结构

```
├── app.py                 # 主入口文件
├── config-private.json    # 私聊配置文件
├── src/
│   ├── core/             # 核心逻辑
│   │   ├── message_processor.py  # 消息处理器
│   │   └── wechat_handler.py     # 微信处理器
│   ├── gui/              # 界面模块
│   ├── config/           # 配置管理
│   └── utils/            # 工具模块
└── logs/                 # 日志文件
```

## 版本信息

- **版本**: v2.1.0
- **类型**: 私聊专用版本
- **基于**: 原群聊版本 v2.0.0
- **更新日期**: 2025-07-17
