# -*- coding: utf-8 -*-
"""
调试系统消息处理问题
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def debug_system_message():
    """调试系统消息处理"""
    print("=== 调试系统消息处理 ===")
    
    try:
        from src.core.message_processor import MessageProcessor
        from src.core.wechat_handler import WeChatHandler
        from src.utils.mock_ntwork import WeWork
        
        # 创建消息处理器实例
        wechat_handler = WeChatHandler()
        processor = MessageProcessor(wechat_handler, None)
        
        # 设置私聊配置
        processor.group_option_configs = {
            'private_chat': {
                'auto_reply': '您好！欢迎咨询，请问您需要以下哪项服务？请直接回复对应数字：\n1.产品咨询\n2.技术支持\n3.售后服务\n4.投诉建议\n5.联系人工客服',
                'option_replies': {
                    '1': '感谢您对我们产品的关注！',
                    '2': '我们的技术支持团队随时为您服务！',
                    '3': '请提供您的订单号或产品信息',
                    '4': '您的意见对我们很重要！',
                    '5': '正在为您转接人工客服，请稍候...'
                }
            }
        }
        
        print("配置已设置:")
        print(f"  private_chat配置存在: {'private_chat' in processor.group_option_configs}")
        print(f"  auto_reply长度: {len(processor.group_option_configs['private_chat']['auto_reply'])}")
        
        # 模拟微信实例
        mock_wechat = WeWork()
        
        # 真实的系统消息数据
        data = {
            "appinfo": "cxgd60p0S7uyYtn",
            "content": "你已添加了嫒宝粑粑，现在可以开始聊天了。",
            "content_type": 1011,
            "conversation_id": "S:1688854710715177_7881299718050125",
            "is_pc": 0,
            "receiver": "1688854710715177",
            "send_time": "1753105338",
            "sender": "7881299718050125",
            "sender_name": "",
            "server_id": "1018286"
        }
        
        print("\n--- 步骤1: 测试消息检测 ---")
        is_new_contact = processor._is_new_contact_message(data)
        print(f"是否为新联系人消息: {is_new_contact}")
        
        if is_new_contact:
            print("\n--- 步骤2: 直接调用处理方法 ---")
            try:
                processor._handle_new_contact(mock_wechat, data)
                print("处理方法调用完成")
                
                # 检查会话是否创建
                sender_id = data['sender']
                if sender_id in processor.user_sessions:
                    print(f"✅ 会话创建成功: {sender_id}")
                    session = processor.user_sessions[sender_id]
                    print(f"  会话详情: {session}")
                else:
                    print(f"❌ 会话创建失败: {sender_id}")
                    print(f"  当前会话: {list(processor.user_sessions.keys())}")
                    
            except Exception as e:
                print(f"❌ 处理方法调用失败: {e}")
                import traceback
                traceback.print_exc()
        
        print("\n--- 步骤3: 测试完整的系统消息处理 ---")
        
        # 清空会话
        processor.user_sessions.clear()
        
        system_message = {
            "data": data,
            "type": 11187
        }
        
        try:
            processor.on_sys_message(mock_wechat, system_message)
            print("完整系统消息处理完成")
            
            # 检查结果
            sender_id = data['sender']
            if sender_id in processor.user_sessions:
                print(f"✅ 完整处理成功: {sender_id}")
            else:
                print(f"❌ 完整处理失败: {sender_id}")
                
        except Exception as e:
            print(f"❌ 完整处理异常: {e}")
            import traceback
            traceback.print_exc()
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_system_message()
