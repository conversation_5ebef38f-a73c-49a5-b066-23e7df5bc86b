# -*- coding: utf-8 -*-
"""
测试真实的系统消息处理
"""

import sys
import os
import json
import time

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_real_system_message():
    """测试真实的系统消息"""
    print("=== 测试真实的系统消息处理 ===")
    
    try:
        from src.core.message_processor import MessageProcessor
        from src.core.wechat_handler import WeChatHandler
        from src.utils.mock_ntwork import WeWork
        
        # 创建消息处理器实例
        wechat_handler = WeChatHandler()
        processor = MessageProcessor(wechat_handler, None)
        
        # 设置私聊配置
        processor.group_option_configs = {
            'private_chat': {
                'auto_reply': '您好！欢迎咨询，请问您需要以下哪项服务？请直接回复对应数字：\n1.产品咨询\n2.技术支持\n3.售后服务\n4.投诉建议\n5.联系人工客服',
                'option_replies': {
                    '1': '感谢您对我们产品的关注！',
                    '2': '我们的技术支持团队随时为您服务！',
                    '3': '请提供您的订单号或产品信息',
                    '4': '您的意见对我们很重要！',
                    '5': '正在为您转接人工客服，请稍候...'
                }
            }
        }
        
        # 模拟微信实例
        mock_wechat = WeWork()
        
        # 真实的系统消息数据
        real_system_message = {
            "data": {
                "appinfo": "cxgd60p0S7uyYtn",
                "content": "你已添加了嫒宝粑粑，现在可以开始聊天了。",
                "content_type": 1011,
                "conversation_id": "S:1688854710715177_7881299718050125",
                "is_pc": 0,
                "receiver": "1688854710715177",
                "send_time": "1753105338",
                "sender": "7881299718050125",
                "sender_name": "",
                "server_id": "1018286"
            },
            "type": 11187
        }
        
        print("处理真实系统消息...")
        print(f"消息内容: {real_system_message['data']['content']}")
        print(f"会话ID: {real_system_message['data']['conversation_id']}")
        print(f"发送者: {real_system_message['data']['sender']}")
        
        # 处理系统消息
        processor.on_sys_message(mock_wechat, real_system_message)
        
        # 检查是否创建了会话
        sender_id = real_system_message['data']['sender']
        if sender_id in processor.user_sessions:
            session = processor.user_sessions[sender_id]
            print(f"✅ 成功创建会话: {sender_id}")
            print(f"   会话开始时间: {session['start_time']}")
            print(f"   等待选项: {session['waiting_for_option']}")
            print(f"   聊天ID: {session['chat_id']}")
            print(f"   来源: {session['source']}")
        else:
            print(f"❌ 未能创建会话: {sender_id}")
            print(f"   当前会话列表: {list(processor.user_sessions.keys())}")
        
        # 测试后续的文本消息处理
        print("\n--- 测试后续文本消息处理 ---")
        
        text_message = {
            "data": {
                "room_wxid": "S:1688854710715177_7881299718050125",
                "from_wxid": "7881299718050125",
                "from_name": "嫒宝粑粑",
                "msg": "1",
                "content_type": 0
            }
        }
        
        print("处理用户选择消息...")
        processor.on_recv_message(mock_wechat, text_message)
        
        # 检查会话状态
        if sender_id in processor.user_sessions:
            print("✅ 文本消息处理成功，会话仍然存在")
        else:
            print("ℹ️  会话可能已结束（如果选择了final类型选项）")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_message_detection():
    """测试消息检测逻辑"""
    print("\n=== 测试消息检测逻辑 ===")
    
    try:
        from src.core.message_processor import MessageProcessor
        from src.core.wechat_handler import WeChatHandler
        
        # 创建消息处理器实例
        wechat_handler = WeChatHandler()
        processor = MessageProcessor(wechat_handler, None)
        
        # 测试不同的消息内容
        test_cases = [
            {
                "content": "你已添加了嫒宝粑粑，现在可以开始聊天了。",
                "expected": True,
                "description": "真实的添加好友消息"
            },
            {
                "content": "客服为你服务",
                "expected": True,
                "description": "客服服务消息"
            },
            {
                "content": "当前为客服聊天模式",
                "expected": True,
                "description": "客服聊天模式消息"
            },
            {
                "content": "普通的系统通知",
                "expected": False,
                "description": "普通系统消息"
            }
        ]
        
        for test_case in test_cases:
            data = {
                "type": "system",
                "content": test_case["content"]
            }
            
            result = processor._is_new_contact_message(data)
            status = "✅ 通过" if result == test_case["expected"] else "❌ 失败"
            print(f"{status} {test_case['description']}: '{test_case['content']}' -> {result}")
            
    except Exception as e:
        print(f"❌ 消息检测测试失败: {e}")

def main():
    """主测试函数"""
    print("微信私聊机器人 - 真实系统消息测试")
    print("=" * 60)
    
    test_message_detection()
    test_real_system_message()
    
    print("\n" + "=" * 60)
    print("测试完成！")

if __name__ == "__main__":
    main()
