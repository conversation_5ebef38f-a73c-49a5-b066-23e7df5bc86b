# 日志配置使用说明

本文档详细说明如何在微信机器人项目中配置和使用日志系统。

## 功能特性

- ✅ **文件保存**: 支持将日志保存到指定文件
- ✅ **控制台输出**: 支持同时在控制台显示日志
- ✅ **日志轮转**: 支持按文件大小自动轮转日志文件
- ✅ **多级别**: 支持DEBUG、INFO、WARNING、ERROR、CRITICAL等级别
- ✅ **预设配置**: 提供开发、生产、调试等预设配置
- ✅ **自定义配置**: 支持完全自定义的日志配置
- ✅ **多实例**: 支持创建多个独立的logger实例

## 快速开始

### 1. 基本使用

```python
from src.core.message_processor import MessageProcessor
from src.core.wechat_handler import WeChatHandler
from src.utils.logger_config import get_logger_config

# 创建微信处理器
wechat_handler = WeChatHandler()

# 使用默认配置
log_config = get_logger_config('development')
message_processor = MessageProcessor(wechat_handler, log_config=log_config)

# 使用日志
message_processor.logger.info("机器人启动成功")
message_processor.logger.warning("这是一条警告")
message_processor.logger.error("这是一条错误")
```

### 2. 自定义配置

```python
# 自定义日志配置
custom_config = {
    'log_file_path': 'logs/my_bot.log',  # 日志文件路径
    'console_output': True,              # 是否输出到控制台
    'log_level': 'DEBUG',               # 日志级别
    'max_file_size': 10,                # 最大文件大小(MB)
    'backup_count': 5                   # 备份文件数量
}

message_processor = MessageProcessor(wechat_handler, log_config=custom_config)
```

## 配置参数详解

### 基本参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `log_file_path` | str | None | 日志文件保存路径，None表示不保存文件 |
| `console_output` | bool | True | 是否输出到控制台 |
| `log_level` | str | 'INFO' | 日志级别 (DEBUG/INFO/WARNING/ERROR/CRITICAL) |
| `max_file_size` | int | 10 | 单个日志文件最大大小(MB) |
| `backup_count` | int | 5 | 保留的备份文件数量 |
| `preset` | str | None | 预设配置名称 |

### 预设配置

#### development (开发环境)
```python
{
    'log_file_path': 'logs/wechat_bot_YYYYMMDD.log',
    'console_output': True,
    'log_level': 'DEBUG',
    'max_file_size': 5,
    'backup_count': 3
}
```

#### production (生产环境)
```python
{
    'log_file_path': 'logs/wechat_bot_YYYYMMDD.log',
    'console_output': False,
    'log_level': 'INFO',
    'max_file_size': 50,
    'backup_count': 10
}
```

#### debug (调试模式)
```python
{
    'log_file_path': 'logs/wechat_bot_YYYYMMDD.log',
    'console_output': True,
    'log_level': 'DEBUG',
    'max_file_size': 1,
    'backup_count': 2
}
```

#### minimal (最小模式)
```python
{
    'log_file_path': None,
    'console_output': True,
    'log_level': 'WARNING'
}
```

## 使用示例

### 示例1: 使用预设配置

```python
from src.utils.logger_config import get_logger_config

# 使用开发环境预设
config = get_logger_config('development')
message_processor = MessageProcessor(wechat_handler, log_config=config)
```

### 示例2: 预设配置 + 参数覆盖

```python
# 使用生产环境预设，但启用控制台输出
config = get_logger_config(
    'production',
    console_output=True,
    log_file_path='logs/custom_production.log'
)
message_processor = MessageProcessor(wechat_handler, log_config=config)
```

### 示例3: 完全自定义

```python
config = {
    'log_file_path': 'logs/my_custom_bot.log',
    'console_output': True,
    'log_level': 'INFO',
    'max_file_size': 20,
    'backup_count': 7
}
message_processor = MessageProcessor(wechat_handler, log_config=config)
```

### 示例4: 仅控制台输出

```python
config = {
    'log_file_path': None,  # 不保存文件
    'console_output': True,
    'log_level': 'INFO'
}
message_processor = MessageProcessor(wechat_handler, log_config=config)
```

### 示例5: 在配置中指定预设

```python
config = {
    'preset': 'debug',
    'log_file_path': 'logs/debug_custom.log'  # 覆盖预设路径
}
message_processor = MessageProcessor(wechat_handler, log_config=config)
```

## 独立使用Logger

如果你需要在其他地方创建独立的logger：

```python
from src.utils.logger_config import setup_logger

# 创建独立的logger
logger = setup_logger('my_module', 'development')
logger.info("这是独立的logger")

# 或者使用自定义配置
logger = setup_logger(
    'my_module',
    log_file_path='logs/my_module.log',
    console_output=True,
    log_level='DEBUG'
)
```

## 日志文件管理

### 自动轮转
当日志文件达到指定大小时，系统会自动创建新文件：
- `bot.log` (当前文件)
- `bot.log.1` (第1个备份)
- `bot.log.2` (第2个备份)
- ...

### 默认路径
如果不指定路径，系统会自动创建：
- 目录: `logs/`
- 文件名: `wechat_bot_YYYYMMDD.log`

## 最佳实践

1. **开发阶段**: 使用 `development` 预设，启用DEBUG级别
2. **生产环境**: 使用 `production` 预设，关闭控制台输出
3. **调试问题**: 使用 `debug` 预设，小文件快速轮转
4. **临时测试**: 使用 `minimal` 预设，仅控制台输出

## 注意事项

1. 确保日志目录有写入权限
2. 定期清理旧的日志文件
3. 生产环境建议关闭DEBUG级别日志
4. 大量日志输出可能影响性能

## 故障排除

### 问题1: 日志文件无法创建
**解决方案**: 检查目录权限，确保程序有写入权限

### 问题2: 日志不显示在控制台
**解决方案**: 检查 `console_output` 参数是否为 `True`

### 问题3: 日志级别过滤不正确
**解决方案**: 检查 `log_level` 参数设置是否正确

## 运行示例

运行提供的示例文件查看效果：

```bash
python example_logger_usage.py
```

这将创建多个日志文件并展示不同的配置效果。
