#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试查询消息功能的脚本
"""

import sys
import os
import time
from unittest.mock import Mock, MagicMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 模拟 ntwork 模块
class MockNtwork:
    class WeWork:
        def send_text(self, conversation_id, text):
            pass

sys.modules['ntwork'] = MockNtwork()

from src.core.message_processor import MessageProcessor


def test_query_message_functionality():
    """测试查询消息功能"""
    print("开始测试查询消息功能...")

    # 创建模拟的微信处理器
    mock_wechat_handler = Mock()
    mock_wechat_handler.user_id = "test_bot_id"
    mock_wechat_handler.name = "测试机器人"
    mock_wechat_handler.exit_flag = False
    
    # 创建模拟的微信实例
    mock_wechat_instance = Mock()
    mock_wechat_instance.send_text = Mock()
    
    # 创建消息处理器
    processor = MessageProcessor(
        wechat_handler=mock_wechat_handler,
        gui_instance=None,
        log_config={'preset': 'debug'}
    )
    
    # 设置私聊配置
    private_config = {
        'auto_reply': '欢迎咨询！请选择您需要的服务：\n1. 产品咨询\n2. 技术支持\n3. 售后服务',
        'tree_options': {
            '1': {
                'text': '产品咨询',
                'reply': '您选择了产品咨询，我们的产品经理将为您服务。',
                'type': 'final',
                'children': {}
            },
            '2': {
                'text': '技术支持',
                'reply': '您选择了技术支持，请描述您遇到的技术问题。',
                'type': 'final',
                'children': {}
            },
            '3': {
                'text': '售后服务',
                'reply': '您选择了售后服务，请提供您的订单号。',
                'type': 'final',
                'children': {}
            }
        }
    }
    
    processor.update_config(
        group_option_configs={'private_chat': private_config},
        paused_rooms=[]
    )
    
    # 测试用户信息
    test_user_id = "S:test_user_123"
    test_room_id = "S:test_user_123"  # 私聊的room_id和user_id相同
    test_sender_name = "测试用户"
    
    print(f"测试用户: {test_sender_name} ({test_user_id})")
    
    # 模拟消息数据结构
    def create_message(content, sender_id=test_user_id, room_id=test_room_id):
        return {
            "data": {
                "content": content,
                "conversation_id": room_id,
                "sender": sender_id,
                "sender_name": test_sender_name,
                "content_type": 0,  # 文本消息
                "local_id": f"msg_{int(time.time() * 1000)}",
                "server_id": f"server_{int(time.time() * 1000)}"
            }
        }
    
    print("\n=== 测试1: 发送查询消息 ===")
    
    # 1. 发送查询消息
    query_message = create_message("查询")
    processor.on_recv_message(mock_wechat_instance, query_message)
    
    # 验证是否发送了自动回复
    mock_wechat_instance.send_text.assert_called_with(
        test_room_id, 
        private_config['auto_reply']
    )
    print("✅ 查询消息处理成功，已发送自动回复")
    
    # 验证会话是否创建
    assert test_user_id in processor.user_sessions, "会话未创建"
    session = processor.user_sessions[test_user_id]
    assert session['waiting_for_option'] == True, "会话状态不正确"
    assert session['source'] == 'query_message', "会话来源不正确"
    print("✅ 新会话创建成功")
    
    print("\n=== 测试2: 用户选择选项 ===")
    
    # 2. 用户选择选项1
    mock_wechat_instance.send_text.reset_mock()
    option_message = create_message("1")
    processor.on_recv_message(mock_wechat_instance, option_message)
    
    # 验证是否发送了选项回复
    mock_wechat_instance.send_text.assert_called_with(
        test_room_id,
        private_config['tree_options']['1']['reply']
    )
    print("✅ 选项处理成功，已发送选项回复")
    
    # 验证会话是否结束
    assert test_user_id not in processor.user_sessions, "会话未正确结束"
    assert test_user_id in processor.user_last_completion, "完成时间未记录"
    print("✅ 会话正确结束")
    
    print("\n=== 测试3: 再次发送查询消息（清除历史） ===")
    
    # 3. 再次发送查询消息，应该清除历史并创建新会话
    mock_wechat_instance.send_text.reset_mock()
    query_message2 = create_message("查询")
    processor.on_recv_message(mock_wechat_instance, query_message2)
    
    # 验证是否再次发送了自动回复
    mock_wechat_instance.send_text.assert_called_with(
        test_room_id,
        private_config['auto_reply']
    )
    print("✅ 第二次查询消息处理成功")
    
    # 验证新会话是否创建，历史是否清除
    assert test_user_id in processor.user_sessions, "新会话未创建"
    assert test_user_id not in processor.user_last_completion, "历史完成时间未清除"
    new_session = processor.user_sessions[test_user_id]
    assert new_session['source'] == 'query_message', "新会话来源不正确"
    print("✅ 历史会话已清除，新会话创建成功")
    
    print("\n=== 测试4: 非查询消息处理 ===")
    
    # 4. 发送非查询消息，应该按正常流程处理
    mock_wechat_instance.send_text.reset_mock()
    normal_message = create_message("2")
    processor.on_recv_message(mock_wechat_instance, normal_message)
    
    # 验证是否发送了选项2的回复
    mock_wechat_instance.send_text.assert_called_with(
        test_room_id,
        private_config['tree_options']['2']['reply']
    )
    print("✅ 非查询消息正常处理")
    
    print("\n🎉 所有测试通过！查询消息功能工作正常。")
    
    # 打印会话统计
    stats = processor.get_session_statistics()
    print(f"\n📊 会话统计: {stats}")


if __name__ == "__main__":
    test_query_message_functionality()
