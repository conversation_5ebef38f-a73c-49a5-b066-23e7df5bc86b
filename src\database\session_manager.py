# -*- coding: utf-8 -*-
"""
会话数据库管理器 - 使用SQLite3管理用户会话状态
"""

import sqlite3
import time
import json
import os
from typing import Dict, List, Optional, Any
from datetime import datetime


class SessionDatabaseManager:
    """会话数据库管理器"""
    
    def __init__(self, db_path: str = "data/sessions.db"):
        """
        初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.ensure_data_directory()
        self.init_database()
    
    def ensure_data_directory(self):
        """确保数据目录存在"""
        data_dir = os.path.dirname(self.db_path)
        if data_dir and not os.path.exists(data_dir):
            os.makedirs(data_dir)
    
    def init_database(self):
        """初始化数据库表结构"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建活跃会话表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS active_sessions (
                    session_id TEXT PRIMARY KEY,
                    chat_id TEXT NOT NULL,
                    start_time REAL NOT NULL,
                    waiting_for_option INTEGER DEFAULT 1,
                    current_path TEXT DEFAULT '[]',
                    source TEXT DEFAULT 'system_message',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建失效会话表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS expired_sessions (
                    session_id TEXT PRIMARY KEY,
                    chat_id TEXT NOT NULL,
                    start_time REAL NOT NULL,
                    end_time REAL NOT NULL,
                    completion_time REAL,
                    total_duration REAL,
                    source TEXT DEFAULT 'system_message',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expired_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_active_session_id ON active_sessions(session_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_active_start_time ON active_sessions(start_time)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_expired_session_id ON expired_sessions(session_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_expired_completion_time ON expired_sessions(completion_time)')
            
            conn.commit()
    
    def create_active_session(self, session_id: str, chat_id: str, source: str = 'system_message') -> bool:
        """
        创建活跃会话
        
        Args:
            session_id: 会话ID
            chat_id: 聊天ID
            source: 会话来源
            
        Returns:
            是否创建成功
        """
        try:
            current_time = time.time()
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO active_sessions 
                    (session_id, chat_id, start_time, waiting_for_option, current_path, source, updated_at)
                    VALUES (?, ?, ?, 1, '[]', ?, CURRENT_TIMESTAMP)
                ''', (session_id, chat_id, current_time, source))
                
                conn.commit()
                return True
                
        except Exception as e:
            print(f"创建活跃会话失败: {e}")
            return False
    
    def get_active_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        获取活跃会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            会话数据或None
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT session_id, chat_id, start_time, waiting_for_option, current_path, source
                    FROM active_sessions 
                    WHERE session_id = ?
                ''', (session_id,))
                
                row = cursor.fetchone()
                if row:
                    return {
                        'session_id': row[0],
                        'chat_id': row[1],
                        'start_time': row[2],
                        'waiting_for_option': bool(row[3]),
                        'current_path': json.loads(row[4]) if row[4] else [],
                        'source': row[5]
                    }
                return None
                
        except Exception as e:
            print(f"获取活跃会话失败: {e}")
            return None
    
    def update_session_path(self, session_id: str, current_path: List[str]) -> bool:
        """
        更新会话路径
        
        Args:
            session_id: 会话ID
            current_path: 当前路径
            
        Returns:
            是否更新成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    UPDATE active_sessions 
                    SET current_path = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE session_id = ?
                ''', (json.dumps(current_path), session_id))
                
                conn.commit()
                return cursor.rowcount > 0
                
        except Exception as e:
            print(f"更新会话路径失败: {e}")
            return False
    
    def expire_session(self, session_id: str, completion_time: Optional[float] = None) -> bool:
        """
        使会话失效（移动到失效会话表）
        
        Args:
            session_id: 会话ID
            completion_time: 完成时间（如果是用户完成选择）
            
        Returns:
            是否操作成功
        """
        try:
            current_time = time.time()
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 获取活跃会话信息
                cursor.execute('''
                    SELECT session_id, chat_id, start_time, source
                    FROM active_sessions 
                    WHERE session_id = ?
                ''', (session_id,))
                
                row = cursor.fetchone()
                if not row:
                    return False
                
                session_id, chat_id, start_time, source = row
                total_duration = current_time - start_time
                
                # 插入到失效会话表
                cursor.execute('''
                    INSERT OR REPLACE INTO expired_sessions 
                    (session_id, chat_id, start_time, end_time, completion_time, total_duration, source)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (session_id, chat_id, start_time, current_time, completion_time, total_duration, source))
                
                # 从活跃会话表删除
                cursor.execute('DELETE FROM active_sessions WHERE session_id = ?', (session_id,))
                
                conn.commit()
                return True
                
        except Exception as e:
            print(f"使会话失效失败: {e}")
            return False
    
    def get_expired_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        获取失效会话信息
        
        Args:
            session_id: 会话ID
            
        Returns:
            失效会话数据或None
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT session_id, chat_id, start_time, end_time, completion_time, total_duration, source
                    FROM expired_sessions 
                    WHERE session_id = ?
                    ORDER BY expired_at DESC
                    LIMIT 1
                ''', (session_id,))
                
                row = cursor.fetchone()
                if row:
                    return {
                        'session_id': row[0],
                        'chat_id': row[1],
                        'start_time': row[2],
                        'end_time': row[3],
                        'completion_time': row[4],
                        'total_duration': row[5],
                        'source': row[6]
                    }
                return None
                
        except Exception as e:
            print(f"获取失效会话失败: {e}")
            return None
    
    def can_create_new_session(self, session_id: str, interval_seconds: int) -> bool:
        """
        检查是否可以创建新会话

        Args:
            session_id: 会话ID
            interval_seconds: 间隔时间（秒）

        Returns:
            是否可以创建新会话
        """
        try:
            # 检查是否有活跃会话
            active_session = self.get_active_session(session_id)
            if active_session:
                return False

            # 检查失效会话的完成时间
            expired_session = self.get_expired_session(session_id)
            if not expired_session:
                # 没有任何会话记录，可以立即创建
                return True

            # 如果有失效会话，检查间隔时间
            completion_time = expired_session.get('completion_time')
            if not completion_time:
                # 没有完成时间（可能是超时失效），使用结束时间
                completion_time = expired_session.get('end_time')

            if completion_time:
                current_time = time.time()
                time_passed = current_time - completion_time
                return time_passed >= interval_seconds

            return True

        except Exception as e:
            print(f"检查是否可以创建新会话失败: {e}")
            return True  # 出错时允许创建
    
    def cleanup_old_expired_sessions(self, days_to_keep: int = 30) -> int:
        """
        清理旧的失效会话记录
        
        Args:
            days_to_keep: 保留天数
            
        Returns:
            清理的记录数
        """
        try:
            cutoff_time = time.time() - (days_to_keep * 24 * 60 * 60)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    DELETE FROM expired_sessions 
                    WHERE end_time < ?
                ''', (cutoff_time,))
                
                deleted_count = cursor.rowcount
                conn.commit()
                return deleted_count
                
        except Exception as e:
            print(f"清理旧失效会话失败: {e}")
            return 0
    
    def get_session_statistics(self) -> Dict[str, int]:
        """
        获取会话统计信息
        
        Returns:
            统计信息字典
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 活跃会话数
                cursor.execute('SELECT COUNT(*) FROM active_sessions')
                active_count = cursor.fetchone()[0]
                
                # 失效会话数
                cursor.execute('SELECT COUNT(*) FROM expired_sessions')
                expired_count = cursor.fetchone()[0]
                
                # 今天的会话数
                today_start = time.time() - (24 * 60 * 60)
                cursor.execute('SELECT COUNT(*) FROM expired_sessions WHERE end_time > ?', (today_start,))
                today_count = cursor.fetchone()[0]
                
                return {
                    'active_sessions': active_count,
                    'expired_sessions': expired_count,
                    'today_sessions': today_count
                }
                
        except Exception as e:
            print(f"获取会话统计失败: {e}")
            return {'active_sessions': 0, 'expired_sessions': 0, 'today_sessions': 0}
