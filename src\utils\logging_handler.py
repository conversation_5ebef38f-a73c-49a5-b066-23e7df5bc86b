# -*- coding: utf-8 -*-
"""
日志处理器模块 - 自定义日志处理器
"""

import logging
import tkinter as tk


class GuiHandler(logging.Handler):
    """
    自定义日志处理器，将日志输出到GUI文本控件
    """
    
    def __init__(self, text_widget):
        """
        初始化GUI日志处理器
        
        Args:
            text_widget: tkinter Text控件，用于显示日志
        """
        logging.Handler.__init__(self)
        self.text_widget = text_widget

    def emit(self, record):
        """
        发送日志记录到GUI
        
        Args:
            record: 日志记录对象
        """
        msg = self.format(record)

        def append():
            """在GUI线程中安全地添加日志文本"""
            self.text_widget.configure(state="normal")
            self.text_widget.insert(tk.END, msg + "\n")
            self.text_widget.see(tk.END)
            self.text_widget.configure(state="disabled")

        # 使用after方法确保在主线程中执行GUI操作
        self.text_widget.after(0, append)


class NullWriter:
    """
    空写入器，用于重定向标准错误输出
    """
    
    def write(self, arg):
        """忽略写入操作"""
        pass
