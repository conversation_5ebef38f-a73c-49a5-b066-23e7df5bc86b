# 微信选项选择机器人 - 模块化版本

这是一个企业微信自动回复机器人，支持树状选项菜单配置、按群分组的选项配置、特殊回复格式（链接卡片等）、用户会话管理和GUI界面管理。

## 版本信息

- **版本**: 2.0.0 (模块化版本)
- **原版本**: 1.x (单文件版本，已备份为 `main_backup.py`)

## 功能特性

- ✅ **树状选项菜单**: 支持多层级的选项配置
- ✅ **按群分组配置**: 每个群可以有独立的选项配置
- ✅ **特殊回复格式**: 支持链接卡片、用户卡片、文件发送、视频发送、小程序、等待回复等特殊格式
- ✅ **用户会话管理**: 自动管理用户对话状态和超时
- ✅ **GUI界面**: 友好的图形界面，支持群管理、选项配置、日志查看
- ✅ **模块化架构**: 清晰的代码结构，易于维护和扩展

## 项目结构

```
微信选项选择机器人/
├── app.py                      # 主入口文件
├── main_backup.py              # 原始单文件版本备份
├── config.json                 # 配置文件
├── requirements.txt            # 依赖包列表
├── test_modules.py             # 模块测试脚本
├── README.md                   # 项目说明文档
└── src/                        # 源代码目录
    ├── __init__.py
    ├── core/                   # 核心业务逻辑
    │   ├── __init__.py
    │   ├── wechat_handler.py   # 微信连接和基础操作
    │   └── message_processor.py # 消息处理和回复逻辑
    ├── gui/                    # 用户界面模块
    │   ├── __init__.py
    │   ├── main_window.py      # 主窗口
    │   ├── room_management.py  # 群管理选项卡
    │   ├── option_configuration.py # 选项配置选项卡
    │   └── log_display.py      # 日志显示选项卡
    ├── config/                 # 配置管理
    │   ├── __init__.py
    │   └── config_manager.py   # 配置文件管理
    └── utils/                  # 工具类模块
        ├── __init__.py
        ├── decorators.py       # 装饰器（单例等）
        ├── logging_handler.py  # 日志处理器
        ├── mock_ntwork.py      # 模拟ntwork模块（测试用）
        ├── mock_keyboard.py    # 模拟keyboard模块（测试用）
        └── mock_ttkbootstrap.py # 模拟ttkbootstrap模块（测试用）
```

## 安装和运行

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 运行程序

```bash
python app.py
```

### 3. 测试模块结构

```bash
python test_modules.py
```

## 依赖包

- `ntwork`: 企业微信SDK
- `ttkbootstrap`: 现代化的tkinter主题库
- `keyboard`: 键盘监听库
- `requests`: HTTP请求库

## 使用说明

### 1. 群管理

- 在"群管理"选项卡中添加需要监控的群
- 支持搜索和分页浏览群列表
- 可以批量添加或单独管理监控群

### 2. 选项配置

- 在"选项配置"选项卡中为每个群配置自动回复选项
- 支持树状结构的多层级选项菜单
- 支持多种特殊回复格式：

#### 特殊回复格式说明

**1. 链接卡片格式**
```
wait_reply=正在为您查询...
title=查询结果
desc=这是查询结果的详细描述
url=https://example.com
img=https://example.com/image.jpg
```

**2. 用户卡片格式**
```
wait_reply=正在为您推荐客服...
user_id=customer_service_001
```

**3. 文件发送格式**
```
wait_reply=正在为您准备文件...
file_path=C:/documents/manual.pdf
```

**4. 视频发送格式**
```
wait_reply=正在为您发送视频...
video_path=C:/videos/example.mp4
```

**5. 小程序格式**
```
wait_reply=正在为您打开小程序...
aes_key=miniapp_key_123
file_id=miniapp_file_456
size=2048
appicon=https://miniapp.com/icon.png
appid=wx_miniapp_123456
appname=测试小程序
conversation_id=miniapp_conv_789
page_path=pages/miniapp/index
title=小程序标题
username=miniappuser
```

**6. 混合格式（普通文本+特殊参数）**
```
这是说明文字
wait_reply=处理中...
file_path=/path/to/file.jpg
请查收附件
```

**格式说明：**
- `wait_reply=` 参数是可选的，用于在发送主要内容前先发送等待消息
- 特殊参数必须单独占一行，格式为 `参数名=参数值`
- 可以在特殊参数前后添加普通文本说明
- 系统会自动识别格式类型并调用相应的发送方法

### 3. 监控运行

- 点击"启动监控"开始自动回复
- 在"运行日志"选项卡中查看运行状态
- 点击"停止监控"停止自动回复

## 模块化改进

相比原始的单文件版本，模块化版本具有以下优势：

1. **代码组织清晰**: 按功能模块分离，易于理解和维护
2. **职责分离**: 每个模块有明确的职责边界
3. **易于测试**: 可以单独测试各个模块
4. **易于扩展**: 新功能可以作为独立模块添加
5. **依赖管理**: 更好的依赖隔离和模拟支持

## 开发和维护

### 添加新功能

1. 在相应的模块目录下创建新文件
2. 在对应的 `__init__.py` 中导出新功能
3. 更新相关的依赖和配置

### 测试

运行 `test_modules.py` 确保所有模块正常工作：

```bash
python test_modules.py
```

### 调试

- 查看"运行日志"选项卡获取实时日志
- 使用日志的保存功能导出日志文件进行分析

## 注意事项

1. 首次运行需要登录企业微信
2. 确保企业微信客户端已安装并可正常使用
3. 配置文件会自动保存，重启程序后配置会自动加载
4. 建议定期备份配置文件

## 许可证

本项目仅供学习和研究使用。
