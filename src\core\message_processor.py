# -*- coding: utf-8 -*-
"""
消息处理器模块 - 负责微信消息的处理和回复逻辑
"""

import time
import json
import random
import logging
import os
from datetime import datetime
from typing import Dict, Any, Optional, List

from .wechat_handler import WeChatHandler
from ..utils.logger_config import LoggerConfig
from ..database.session_manager import SessionDatabaseManager

# 尝试导入真实的ntwork，如果失败则使用模拟版本

import ntwork



class MessageProcessor:
    """
    消息处理器 - 负责处理微信消息和自动回复
    """
    
    def __init__(self, wechat_handler: WeChatHandler, gui_instance=None, log_config=None):
        """
        初始化消息处理器

        Args:
            wechat_handler: 微信处理器实例
            gui_instance: GUI实例（用于获取配置和状态）
            log_config: 日志配置字典，包含 log_file_path, console_output 等
        """
        self.wechat_handler = wechat_handler
        self.gui = gui_instance

        # 消息处理状态
        self.processed_messages = {}  # 记录已处理的消息
        self.user_sessions = {}  # 用户会话状态

        # 配置数据
        self.group_option_configs = {}  # 按群分组的选项配置
        self.paused_rooms = []  # 暂停群列表

        # 间隔时间管理
        self.user_last_completion = {}  # 用户最后完成选择的时间记录（保留用于兼容）
        self.interval_seconds = 1800  # 间隔时间设置（秒），默认30分钟

        # 设置日志配置
        self._setup_logger(log_config or {})

    def _setup_logger(self, log_config: Dict[str, Any]):
        """
        设置日志配置

        Args:
            log_config: 日志配置字典，支持以下参数：
                - log_file_path: 日志文件保存路径 (可选)
                - console_output: 是否输出到控制台 (默认: True)
                - log_level: 日志级别 (默认: INFO)
                - max_file_size: 最大文件大小MB (默认: 10)
                - backup_count: 备份文件数量 (默认: 5)
                - preset: 预设配置名称 ('development', 'production', 'debug', 'minimal')
        """
        # 如果指定了预设配置，则使用预设配置作为基础
        if 'preset' in log_config:
            preset_config = LoggerConfig.get_preset_configs().get(
                log_config['preset'],
                LoggerConfig.get_preset_configs()['development']
            )
            # 合并配置，用户配置优先
            final_config = preset_config.copy()
            final_config.update({k: v for k, v in log_config.items() if k != 'preset'})
        else:
            # 使用默认配置作为基础
            final_config = LoggerConfig.create_logger_config()
            final_config.update(log_config)

        # 使用LoggerConfig工具类设置logger
        logger_name = f"{__name__}.{id(self)}"  # 使用唯一名称避免冲突
        self.logger = LoggerConfig.setup_logger(logger_name, final_config)

        self.logger.info("消息处理器日志系统初始化完成")

        # 初始化数据库会话管理器
        self.session_db = SessionDatabaseManager()

        # 启动时清理旧的失效会话记录
        self._cleanup_old_sessions()

    def _cleanup_old_sessions(self):
        """清理旧的失效会话记录"""
        try:
            if self.session_db:
                deleted_count = self.session_db.cleanup_old_expired_sessions(30)  # 保留30天
                if deleted_count > 0:
                    self.logger.info(f"清理了 {deleted_count} 条旧的失效会话记录")
            else:
                self.logger.warning("数据库管理器未初始化，跳过旧会话清理")
        except Exception as e:
            self.logger.error(f"清理旧会话记录失败: {e}")

    def get_session_statistics(self) -> Dict[str, int]:
        """获取会话统计信息"""
        if self.session_db:
            return self.session_db.get_session_statistics()
        else:
            # 数据库不可用时返回内存统计
            return {
                'active_sessions': len(self.user_sessions),
                'expired_sessions': 0,
                'today_sessions': 0
            }

    def set_interval_seconds(self, seconds: int):
        """
        设置间隔时间

        Args:
            seconds: 间隔时间（秒）
        """
        self.interval_seconds = seconds
        self.logger.info(f"设置间隔时间为: {seconds}秒 ({seconds//60}分钟)")

    def update_config(self, group_option_configs: Dict[str, Dict[str, Any]], paused_rooms: List[str]):
        """
        更新配置
        
        Args:
            group_option_configs: 按群分组的选项配置
            paused_rooms: 暂停群列表
        """
        self.group_option_configs = group_option_configs
        self.paused_rooms = paused_rooms
        
    def on_recv_message(self, wechat_instance: ntwork.WeWork, message):
        """
        消息接收处理函数
        
        Args:
            wechat_instance: 微信实例
            message: 消息数据
        """
        # 使用消息ID来防止重复处理
        message_id = message["data"].get("local_id") or message["data"].get("server_id")
        current_time = time.time()
        print(message)
        # 如果该消息已处理过且时间间隔小于30秒，则忽略
        if message_id in self.processed_messages:
            if current_time - self.processed_messages[message_id] < 30:
                self.logger.info(f"忽略重复接收的消息: {message_id}")
                return
        
        # 记录此消息的处理时间
        self.processed_messages[message_id] = current_time
        
        # 清理过期的消息记录（超过1分钟的）
        self.processed_messages = {k: v for k, v in self.processed_messages.items() 
                                  if current_time - v < 60}
        
        # 检查退出标志
        if self.wechat_handler.exit_flag:
            return
            
        # 添加消息接收日志
        self.logger.info(f"收到原始消息: {json.dumps(message, ensure_ascii=False)}")
        self.logger.info(f"机器人名字: {self.wechat_handler.name}")

        # # 检查按群分组的选项配置是否存在
        # if not self.group_option_configs:
        #     self.logger.error("群选项配置未初始化")
        #     return
            
        data = message["data"]
        content = data.get("content", "").strip()
        if not content:
            return
            
        room_wxid = data.get("conversation_id")
        from_wxid = data.get("sender")
        sender_name = data.get("sender_name", "")
        
        # 检查是否是自己发送的消息
        if from_wxid == self.wechat_handler.user_id:
            self.logger.info("忽略自己发送的消息")
            return

        if room_wxid == "FILEASSIST":
            return
            
        self.logger.info(f"消息类型: {data.get('content_type')}")
        
        # 添加消息类型检查日志  11187
        if data.get("content_type") not in [0, 2]:
            self.logger.info(f"忽略非文本消息，类型: {data.get('content_type')}")
            return
            
        # 添加消息基础信息日志
        self.logger.info(f"消息处理开始 | 发送者: {sender_name}({from_wxid}) | 会话ID: {room_wxid} | 内容: {content}")
        
        # 根据room_wxid前缀判断是群聊还是私聊
        is_private = room_wxid and room_wxid.startswith('S:')
        is_group = room_wxid and room_wxid.startswith('R:')

        self.logger.info(f"消息类型: {'私聊' if is_private else '群聊' if is_group else '未知类型'}")

        # 只处理私聊消息，忽略群聊消息
        if is_group:
            self.logger.info(f"忽略群聊消息，群ID: {room_wxid}")
            return

        if not is_private:
            self.logger.info(f"忽略非私聊消息，会话ID: {room_wxid}")
            return

        try:
            # 检查是否是"查询"消息，如果是则创建新会话并清除历史会话
            if content.strip() == "查询":
                self._handle_query_message(wechat_instance, room_wxid, from_wxid, sender_name, is_private)
                return

            # 处理消息
            self._process_message(wechat_instance, room_wxid, from_wxid, sender_name, content, is_group, is_private)

        except Exception as e:
            self.logger.error(f"消息处理异常: {str(e)}", exc_info=True)

    def on_sys_message(self, wechat_instance: ntwork.WeWork, message):
        """
        系统消息接收处理函数

        Args:
            wechat_instance: 微信实例
            message: 系统消息数据
        """
        try:
            # 检查退出标志
            if self.wechat_handler.exit_flag:
                return

            # 添加系统消息接收日志
            self.logger.info(f"收到系统消息: {json.dumps(message, ensure_ascii=False)}")

            data = message.get("data", {})
            msg_type = data.get("type")

            # 处理好友验证通过或客服名片进入的系统消息
            if self._is_new_contact_message(data):
                self.logger.info("检测到新联系人消息，开始处理...")
                self._handle_new_contact(wechat_instance, data)
            else:
                self.logger.info("不是新联系人消息，跳过处理")

        except Exception as e:
            self.logger.error(f"系统消息处理异常: {str(e)}", exc_info=True)
            import traceback
            self.logger.error(f"异常堆栈: {traceback.format_exc()}")

    def _is_new_contact_message(self, data: Dict[str, Any]) -> bool:
        """
        判断是否是新联系人消息（好友验证通过或客服名片进入）

        Args:
            data: 系统消息数据

        Returns:
            是否是新联系人消息
        """
    
        content = data.get("content", "")

        # 检查是否是好友验证通过的消息
        self.logger.info(f'检查是否是新联系人消息----{"以上是打招呼内容" in content or "已添加了" in content  or "为你服务" in content or "当前为客服聊天" in content}')
        if ("以上是打招呼内容" in content or "已添加了" in content or "为你服务" in content or "当前为客服聊天" in content):
            return True
        return False

    def _handle_new_contact(self, wechat_instance: ntwork.WeWork, data: Dict[str, Any]):
        """
        处理新联系人消息

        Args:
            wechat_instance: 微信实例
            data: 系统消息数据
        """
        try:
            # 从系统消息中提取用户信息
            conversation_id = data.get("conversation_id")
            from_wxid = data.get("sender") or data.get("from_wxid")  # 优先使用sender字段

            self.logger.info(f"系统消息解析: conversation_id={conversation_id}, from_wxid={from_wxid}")

            if not conversation_id or not from_wxid:
                self.logger.warning(f"系统消息缺少必要的用户信息: conversation_id={conversation_id}, from_wxid={from_wxid}")
                return

            # 确保是私聊消息
            if not conversation_id.startswith('S:'):
                self.logger.info(f"忽略非私聊系统消息: {conversation_id}")
                return

            self.logger.info(f"处理新联系人: {from_wxid}, 会话ID: {conversation_id}")

            # 获取私聊选项配置
            private_config = self.group_option_configs.get('private_chat', {})
            if not private_config:
                private_config = self.group_option_configs.get('', {})

            if not private_config:
                self.logger.warning("私聊配置未找到，无法发送欢迎消息")
                return

            # 创建用户会话（只在系统消息中创建）
            session_id = from_wxid

            # 检查是否已有活跃会话
            if self.session_db:
                existing_session = self.session_db.get_active_session(session_id)
                if existing_session:
                    self.logger.info(f"用户 {from_wxid} 已有活跃会话，跳过创建")
                    return
            else:
                # 数据库未初始化，检查内存会话
                if session_id in self.user_sessions:
                    self.logger.info(f"用户 {from_wxid} 已有内存会话，跳过创建")
                    return

            # 发送自动回复内容并创建会话
            auto_reply = private_config.get('auto_reply', '')
            if auto_reply:
                current_time = time.time()

                # 在数据库中创建新会话（如果数据库可用）
                if self.session_db:
                    if self.session_db.create_active_session(session_id, conversation_id, 'system_message'):
                        self.logger.info(f"数据库创建会话成功: {session_id}")
                    else:
                        self.logger.error(f"数据库创建会话失败: {session_id}")

                # 在内存中创建会话
                self.user_sessions[session_id] = {
                    'start_time': current_time,
                    'waiting_for_option': True,
                    'chat_id': conversation_id,
                    'current_path': [],
                    'source': 'system_message'
                }

                wechat_instance.send_text(conversation_id, auto_reply)
                self.logger.info(f"向新联系人发送欢迎消息并创建会话: {from_wxid}, 会话ID: {session_id}")
            else:
                self.logger.warning("私聊配置中没有自动回复内容，无法创建会话")

        except Exception as e:
            self.logger.error(f"处理新联系人失败: {str(e)}", exc_info=True)

    def _handle_query_message(self, wechat_instance: ntwork.WeWork, room_wxid: str,
                             from_wxid: str, sender_name: str, is_private: bool):
        """
        处理"查询"消息 - 创建新会话并清除历史会话

        Args:
            wechat_instance: 微信实例
            room_wxid: 群/会话ID
            from_wxid: 发送者ID
            sender_name: 发送者名称
            is_private: 是否为私聊
        """
        try:
            self.logger.info(f"收到查询消息，开始处理新会话创建: 用户={sender_name}({from_wxid}), 会话ID={room_wxid}")

            # 只处理私聊消息
            if not is_private:
                self.logger.info(f"忽略非私聊的查询消息，会话ID: {room_wxid}")
                return

            # 获取私聊选项配置
            private_config = self.group_option_configs.get('private_chat', {})
            if not private_config:
                private_config = self.group_option_configs.get('', {})

            if not private_config:
                self.logger.warning("私聊配置未找到，无法处理查询消息")
                return

            session_id = from_wxid
            current_time = time.time()

            # 1. 清除历史会话（如果存在）
            self._clear_user_sessions(session_id)

            # 2. 创建新会话
            self._create_new_query_session(wechat_instance, private_config, room_wxid,
                                         session_id, current_time)

        except Exception as e:
            self.logger.error(f"处理查询消息失败: {str(e)}", exc_info=True)

    def _clear_user_sessions(self, session_id: str):
        """
        清除用户的历史会话

        Args:
            session_id: 会话ID
        """
        try:
            # 清除数据库中的活跃会话
            if self.session_db:
                active_session = self.session_db.get_active_session(session_id)
                if active_session:
                    # 将活跃会话标记为失效（查询重置）
                    if self.session_db.expire_session(session_id, time.time()):
                        self.logger.info(f"数据库中的活跃会话已清除: {session_id}")
                    else:
                        self.logger.warning(f"清除数据库活跃会话失败: {session_id}")

            # 清除内存中的会话
            if session_id in self.user_sessions:
                del self.user_sessions[session_id]
                self.logger.info(f"内存中的会话已清除: {session_id}")

            # 清除完成时间记录（允许立即重新开始）
            if session_id in self.user_last_completion:
                del self.user_last_completion[session_id]
                self.logger.info(f"用户完成时间记录已清除: {session_id}")

        except Exception as e:
            self.logger.error(f"清除用户会话失败: {str(e)}", exc_info=True)

    def _create_new_query_session(self, wechat_instance: ntwork.WeWork, private_config: Dict[str, Any],
                                 room_wxid: str, session_id: str, current_time: float):
        """
        创建新的查询会话

        Args:
            wechat_instance: 微信实例
            private_config: 私聊配置
            room_wxid: 会话ID
            session_id: 会话ID
            current_time: 当前时间
        """
        try:
            auto_reply = private_config.get('auto_reply', '')
            if not auto_reply:
                self.logger.warning("私聊配置中没有自动回复内容，无法创建查询会话")
                return

            # 在数据库中创建新会话
            if self.session_db:
                if self.session_db.create_active_session(session_id, room_wxid, 'query_message'):
                    self.logger.info(f"数据库创建查询会话成功: {session_id}")
                else:
                    self.logger.error(f"数据库创建查询会话失败: {session_id}")

            # 在内存中创建会话
            self.user_sessions[session_id] = {
                'start_time': current_time,
                'waiting_for_option': True,
                'chat_id': room_wxid,
                'current_path': [],
                'source': 'query_message'
            }

            # 发送自动回复
            wechat_instance.send_text(room_wxid, auto_reply)
            self.logger.info(f"查询会话创建成功并发送欢迎消息: {session_id}")

        except Exception as e:
            self.logger.error(f"创建查询会话失败: {str(e)}", exc_info=True)

    def _get_monitored_rooms(self) -> List[str]:
        """
        获取监控群列表（私聊机器人无需监控群列表）

        Returns:
            空列表（私聊机器人不需要监控群）
        """
        return []
    
    def _process_message(self, wechat_instance: ntwork.WeWork, room_wxid: str, from_wxid: str,
                        sender_name: str, content: str, is_group: bool, is_private: bool):
        """
        处理消息的核心逻辑

        Args:
            wechat_instance: 微信实例
            room_wxid: 群/会话ID
            from_wxid: 发送者ID
            sender_name: 发送者名称
            content: 消息内容
            is_group: 是否为群聊
            is_private: 是否为私聊
        """
        # 获取私聊选项配置（使用默认配置或特定用户配置）
        private_config = self.group_option_configs.get('private_chat', {})
        if not private_config:
            # 如果没有私聊配置，使用默认配置
            private_config = self.group_option_configs.get('', {})

        if not private_config:
            self.logger.warning(f"私聊 {room_wxid} 没有配置选项，忽略消息")
            return

        # 生成用户会话ID（私聊直接使用发送者ID）
        session_id = from_wxid
        current_time = time.time()

        # 检查用户会话状态（只检查，不创建）
        session = self._get_existing_session(session_id, current_time)

        # 确定发送目标（私聊发送目标就是会话ID）
        target_id = room_wxid

        # 如果用户没有会话，检查是否可以重新创建会话
        if not session:
            # 使用数据库检查是否可以创建新会话（如果数据库可用）
            can_create = False
            if self.session_db:
                can_create = self.session_db.can_create_new_session(session_id, self.interval_seconds)
            else:
                # 数据库不可用时，使用内存检查
                can_create = self._can_recreate_session_memory(session_id, current_time)

            if can_create:
                self.logger.info(f"用户 {from_wxid} 可以创建新会话（无活跃会话且满足间隔条件）")
                self._create_new_session_from_text(wechat_instance, private_config, target_id,
                                                 session_id, room_wxid, from_wxid, current_time)
                return
            else:
                self.logger.info(f"用户 {from_wxid} 不满足创建新会话条件，忽略文本消息")
                return

        # 获取树状选项数据
        tree_options = self._get_tree_options(private_config, room_wxid)
        if not tree_options:
            return

        # 如果用户在等待选项回复
        if session.get('waiting_for_option', False):
            self._handle_option_selection(wechat_instance, session, content, tree_options,
                                        target_id, from_wxid, room_wxid, session_id)

    def _can_recreate_session_memory(self, session_id: str, current_time: float) -> bool:
        """
        检查是否可以重新创建会话（内存版本，数据库不可用时使用）

        Args:
            session_id: 会话ID
            current_time: 当前时间

        Returns:
            是否可以重新创建会话
        """
        # 如果用户从未完成过选择，允许创建
        if session_id not in self.user_last_completion:
            return True

        # 检查是否超过间隔时间
        last_completion_time = self.user_last_completion[session_id]
        time_passed = current_time - last_completion_time

        self.logger.info(f"用户 {session_id} 上次完成时间: {last_completion_time}, "
                        f"已过时间: {time_passed}秒, 间隔设置: {self.interval_seconds}秒")

        return time_passed >= self.interval_seconds

    def _can_recreate_session(self, session_id: str, current_time: float) -> bool:
        """
        检查是否可以重新创建会话

        Args:
            session_id: 会话ID
            current_time: 当前时间

        Returns:
            是否可以重新创建会话
        """
        # 如果用户从未完成过选择，不能重新创建
        if session_id not in self.user_last_completion:
            return False

        # 检查是否超过间隔时间
        last_completion_time = self.user_last_completion[session_id]
        time_passed = current_time - last_completion_time

        self.logger.info(f"用户 {session_id} 上次完成时间: {last_completion_time}, "
                        f"已过时间: {time_passed}秒, 间隔设置: {self.interval_seconds}秒")

        return time_passed >= self.interval_seconds

    def _create_new_session_from_text(self, wechat_instance, private_config: Dict[str, Any],
                                    target_id: str, session_id: str, room_wxid: str,
                                    from_wxid: str, current_time: float):
        """
        从文本消息创建新会话

        Args:
            wechat_instance: 微信实例
            private_config: 私聊配置
            target_id: 目标ID
            session_id: 会话ID
            room_wxid: 会话ID
            from_wxid: 发送者ID
            current_time: 当前时间
        """
        auto_reply = private_config.get('auto_reply', '')
        if auto_reply:
            # 在数据库中创建新会话（如果数据库可用）
            if self.session_db:
                if self.session_db.create_active_session(session_id, room_wxid, 'text_message_recreate'):
                    self.logger.info(f"数据库创建会话成功: {session_id}")
                else:
                    self.logger.error(f"数据库创建会话失败: {session_id}")

            # 在内存中创建会话
            self.user_sessions[session_id] = {
                'start_time': current_time,
                'waiting_for_option': True,
                'chat_id': room_wxid,
                'current_path': [],
                'source': 'text_message_recreate'
            }

            wechat_instance.send_text(target_id, auto_reply)
            self.logger.info(f"从文本消息重新创建会话并发送欢迎消息: {from_wxid}, 会话ID: {session_id}")
        else:
            self.logger.warning("私聊配置中没有自动回复内容，无法创建会话")
    
    def _get_or_create_session(self, session_id: str, room_wxid: str, current_time: float) -> Optional[Dict[str, Any]]:
        """
        获取或创建用户会话

        Args:
            session_id: 会话ID
            room_wxid: 群/会话ID
            current_time: 当前时间

        Returns:
            会话数据或None
        """
        if session_id in self.user_sessions:
            session = self.user_sessions[session_id]
            # 检查会话是否超时（5分钟）
            if current_time - session['start_time'] > 300:  # 5分钟 = 300秒
                self.logger.info(f"用户会话超时，清理会话: {session_id}")
                # 发送会话超时消息
                self._send_session_timeout_message(session_id, room_wxid)

                # 在数据库中将会话标记为失效（超时失效，无完成时间）
                if self.session_db:
                    self.session_db.expire_session(session_id, None)

                if session_id in self.user_sessions:
                    del self.user_sessions[session_id]
                return None
            else:
                return session
        return None

    def _get_existing_session(self, session_id: str, current_time: float) -> Optional[Dict[str, Any]]:
        """
        获取现有用户会话（不创建新会话）

        Args:
            session_id: 会话ID
            current_time: 当前时间

        Returns:
            会话数据或None
        """
        # 检查数据库管理器是否已初始化
        if not self.session_db:
            self.logger.warning("数据库管理器未初始化，回退到内存会话管理")
            return self._get_memory_session(session_id, current_time)

        # 优先从数据库获取活跃会话
        db_session = self.session_db.get_active_session(session_id)
        if db_session:
            # 检查会话是否超时（5分钟）
            if current_time - db_session['start_time'] > 300:  # 5分钟 = 300秒
                self.logger.info(f"用户会话超时，清理会话: {session_id}")
                # 发送会话超时消息
                room_wxid = db_session.get('chat_id', session_id)
                self._send_session_timeout_message(session_id, room_wxid)

                # 在数据库中将会话标记为失效（超时失效，无完成时间）
                if self.session_db:
                    self.session_db.expire_session(session_id, None)

                # 同时清理内存中的会话
                if session_id in self.user_sessions:
                    del self.user_sessions[session_id]
                return None
            else:
                # 确保内存中也有会话数据（保持兼容性）
                if session_id not in self.user_sessions:
                    self.user_sessions[session_id] = {
                        'start_time': db_session['start_time'],
                        'waiting_for_option': db_session['waiting_for_option'],
                        'chat_id': db_session['chat_id'],
                        'current_path': db_session['current_path'],
                        'source': db_session['source']
                    }
                return self.user_sessions[session_id]

        return None

    def _get_memory_session(self, session_id: str, current_time: float) -> Optional[Dict[str, Any]]:
        """
        从内存获取会话（数据库管理器未初始化时的回退方法）

        Args:
            session_id: 会话ID
            current_time: 当前时间

        Returns:
            会话数据或None
        """
        if session_id in self.user_sessions:
            session = self.user_sessions[session_id]
            # 检查会话是否超时（5分钟）
            if current_time - session['start_time'] > 300:  # 5分钟 = 300秒
                self.logger.info(f"用户会话超时，清理会话: {session_id}")
                # 发送会话超时消息
                room_wxid = session.get('chat_id', session_id)
                self._send_session_timeout_message(session_id, room_wxid)

                if session_id in self.user_sessions:
                    del self.user_sessions[session_id]
                return None
            else:
                return session
        return None

    def _send_session_timeout_message(self, session_id: str, room_wxid: str):
        """
        发送会话超时消息

        Args:
            session_id: 会话ID
            room_wxid: 会话ID
        """
        try:
            if self.wechat_handler and self.wechat_handler.wechat:
                timeout_message = "您的会话已超时，如需继续咨询请重新发送消息。"
                self.wechat_handler.wechat.send_text(room_wxid, timeout_message)
                self.logger.info(f"发送会话超时消息给用户: {session_id}")
        except Exception as e:
            self.logger.error(f"发送会话超时消息失败: {str(e)}")

    def cleanup_expired_sessions(self):
        """
        清理过期的会话（定期调用）
        """
        try:
            current_time = time.time()
            expired_sessions = []

            for session_id, session in self.user_sessions.items():
                if current_time - session['start_time'] > 300:  # 5分钟超时
                    expired_sessions.append((session_id, session.get('chat_id', session_id)))

            for session_id, room_wxid in expired_sessions:
                self.logger.info(f"清理过期会话: {session_id}")
                self._send_session_timeout_message(session_id, room_wxid)

                # 在数据库中将会话标记为失效（超时失效，无完成时间）
                if self.session_db:
                    self.session_db.expire_session(session_id, None)

                if session_id in self.user_sessions:
                    del self.user_sessions[session_id]

            if expired_sessions:
                self.logger.info(f"清理了 {len(expired_sessions)} 个过期会话")

        except Exception as e:
            self.logger.error(f"清理过期会话失败: {str(e)}")

    def _get_tree_options(self, current_group_config: Dict[str, Any], room_wxid: str) -> Optional[Dict[str, Any]]:
        """
        获取树状选项数据

        Args:
            current_group_config: 当前群配置
            room_wxid: 群ID

        Returns:
            树状选项数据
        """
        tree_options = current_group_config.get('tree_options', {})
        if not tree_options:
            # 兼容旧版本配置
            old_options = current_group_config.get('option_replies', {})
            if old_options:
                tree_options = self._convert_flat_to_tree_for_processing(old_options)
            else:
                self.logger.warning(f"群 {room_wxid} 没有配置选项数据")
                return None
        return tree_options

    # _send_auto_reply 方法已移除，会话创建现在只在系统消息中进行

    def _handle_option_selection(self, wechat_instance: ntwork.WeWork, session: Dict[str, Any],
                               content: str, tree_options: Dict[str, Any], target_id: str,
                               from_wxid: str, room_wxid: str, session_id: str):
        """
        处理选项选择

        Args:
            wechat_instance: 微信实例
            session: 会话数据
            content: 消息内容
            tree_options: 树状选项数据
            target_id: 目标ID
            from_wxid: 发送者ID
            room_wxid: 群ID
            session_id: 会话ID
        """
        content_stripped = content.strip()
        current_path = session.get('current_path', [])

        # 获取当前层级的选项数据
        current_level_options = self._get_current_level_options(tree_options, current_path)

        if content_stripped in current_level_options:
            option_data = current_level_options[content_stripped]
            option_reply = option_data.get('reply', '')
            option_type = option_data.get('type', 'final')

            if option_reply:
                # 如果是菜单选项，需要在回复内容后添加子选项列表
                if option_type == 'menu':
                    enhanced_reply = self._build_menu_reply(option_data, option_reply)
                else:
                    enhanced_reply = option_reply

                # 处理特殊格式的回复内容
                self._process_special_reply(wechat_instance, target_id, enhanced_reply, from_wxid, room_wxid)

                if option_type == 'final':
                    # 最终选项，结束对话
                    current_time = time.time()

                    # 在数据库中将会话标记为失效（记录完成时间）
                    if self.session_db:
                        if self.session_db.expire_session(session_id, current_time):
                            self.logger.info(f"会话已在数据库中标记为失效: {session_id}")
                        else:
                            self.logger.warning(f"数据库标记会话失效失败: {session_id}")
                    else:
                        self.logger.info(f"数据库不可用，仅在内存中处理会话失效: {session_id}")

                    # 保持兼容性，同时更新内存记录
                    self.user_last_completion[session_id] = current_time
                    if session_id in self.user_sessions:
                        del self.user_sessions[session_id]

                    self.logger.info(f"对话结束，清理会话: {session_id}, 记录完成时间: {current_time}")
                elif option_type == 'menu':
                    # 菜单选项，更新路径并继续对话
                    new_path = current_path + [content_stripped]
                    session['current_path'] = new_path

                    # 在数据库中更新会话路径
                    if self.session_db:
                        self.session_db.update_session_path(session_id, new_path)

                    self.logger.info(f"进入子菜单: {' -> '.join(new_path)}")
            else:
                wechat_instance.send_text(target_id, f"选项{content_stripped}的回复内容未配置")
                self.logger.warning(f"私聊 {room_wxid} 选项{content_stripped}的回复内容未配置")
        else:
            # 用户输入的不是有效选项序号，提示重新输入
            self._send_invalid_option_prompt(wechat_instance, target_id, current_level_options, content_stripped, room_wxid)

    def _convert_flat_to_tree_for_processing(self, flat_options: Dict[str, str]) -> Dict[str, Any]:
        """
        将平面选项配置转换为树状结构（用于消息处理）

        Args:
            flat_options: 平面选项配置

        Returns:
            树状选项数据
        """
        tree_data = {}
        for key, reply in flat_options.items():
            tree_data[key] = {
                'text': f'选项{key}',
                'reply': reply,
                'type': 'final',
                'children': {}
            }
        return tree_data

    def _get_current_level_options(self, tree_options: Dict[str, Any], current_path: List[str]) -> Dict[str, Any]:
        """
        获取当前层级的选项

        Args:
            tree_options: 树状选项数据
            current_path: 当前路径

        Returns:
            当前层级的选项数据
        """
        current_data = tree_options

        # 根据路径导航到当前层级
        for path_item in current_path:
            if path_item in current_data and 'children' in current_data[path_item]:
                current_data = current_data[path_item]['children']
            else:
                return {}

        return current_data

    def _build_menu_reply(self, option_data: Dict[str, Any], option_reply: str) -> str:
        """
        构建菜单回复内容

        Args:
            option_data: 选项数据
            option_reply: 原始回复内容

        Returns:
            增强的回复内容
        """
        # 获取子选项并生成选项列表
        children = option_data.get('children', {})
        if children:
            # 构建子选项列表
            sub_options = []
            # 改进排序逻辑，正确处理小数点序号
            def sort_key(x):
                try:
                    if '.' in x:
                        parts = x.split('.')
                        return (int(parts[0]), int(parts[1]))
                    else:
                        return (int(x), 0)
                except ValueError:
                    return (999, 999)  # 无法解析的放到最后

            for sub_key in sorted(children.keys(), key=sort_key):
                sub_text = children[sub_key]['text']
                sub_options.append(f"{sub_key}. {sub_text}")

            # 将子选项添加到回复内容中
            if sub_options:
                enhanced_reply = option_reply + "\n" + "\n".join(sub_options)
            else:
                enhanced_reply = option_reply
        else:
            enhanced_reply = option_reply

        return enhanced_reply

    def _send_invalid_option_prompt(self, wechat_instance: ntwork.WeWork, target_id: str,
                                  current_level_options: Dict[str, Any], content_stripped: str, room_wxid: str):
        """
        发送无效选项提示

        Args:
            wechat_instance: 微信实例
            target_id: 目标ID
            current_level_options: 当前层级选项
            content_stripped: 用户输入内容
            room_wxid: 聊天ID
        """
        available_keys = list(current_level_options.keys())
        if available_keys:
            # 提取数字部分进行排序
            try:
                numeric_keys = []
                for key in available_keys:
                    if '.' in key:
                        numeric_keys.append(float(key))
                    else:
                        numeric_keys.append(int(key))
                numeric_keys.sort()
                options_range = f"（{numeric_keys[0]}-{numeric_keys[-1]}）"
            except:
                options_range = f"（{', '.join(sorted(available_keys))}）"
        else:
            options_range = ""
        wechat_instance.send_text(target_id, f"请回复对应的数字序号{options_range}")
        self.logger.info(f"用户输入无效选项: {content_stripped}, 提示重新输入, 聊天ID: {room_wxid}")

    def _process_special_reply(self, wechat_instance: ntwork.WeWork, target_id: str,
                             reply_content: str, from_wxid: str, room_wxid: str):
        """
        处理特殊格式的回复内容

        Args:
            wechat_instance: 微信实例
            target_id: 目标ID
            reply_content: 回复内容
            from_wxid: 发送者ID
            room_wxid: 聊天ID
        """
        try:
            self.logger.info(f"开始处理特殊回复，内容长度: {len(reply_content)}")

            # 检查是否包含特殊参数
            miniapp_params = ['aes_key=', 'file_id=', 'size=', 'appicon=', 'appid=', 'appname=', 'page_path=', 'username=']
            other_params = ['wait_reply=', 'title=', 'desc=', 'url=', 'img=', 'user_id=', 'file_path=', 'video_path=']
            all_params = miniapp_params + other_params

            if '\n' in reply_content and any(param in reply_content for param in all_params):
                self.logger.info("检测到特殊回复格式")

                # 解析特殊格式
                params = self._parse_special_reply_params(reply_content)
                self.logger.info(f"解析到的参数: {params}")

                # 先发送等待回复（如果有）
                if 'wait_reply' in params:
                    wechat_instance.send_text(target_id, params['wait_reply'])
                    self.logger.info(f"发送等待回复给私聊用户: {from_wxid}, 聊天ID: {room_wxid}")
                    time.sleep(random.uniform(1, 3))  # 等待1-3秒

                # 检查是否有小程序发送参数
                miniapp_required_params = ['aes_key', 'file_id', 'size', 'appicon', 'appid', 'appname', 'page_path', 'title', 'username']
                miniapp_missing_params = [p for p in miniapp_required_params if p not in params]
                self.logger.info(f"小程序缺失参数: {miniapp_missing_params}")
                if not miniapp_missing_params:
                    self.logger.info("检测到小程序格式，尝试发送小程序")
                    try:
                        self.logger.info(f"调用send_miniapp: conversation_id={target_id}")

                        success = wechat_instance.send_miniapp(
                            aes_key=params['aes_key'],
                            file_id=params['file_id'],
                            size=int(params['size']),
                            appicon=params['appicon'],
                            appid=params['appid'],
                            appname=params['appname'],
                            conversation_id=target_id,
                            page_path=params['page_path'],
                            title=params['title'],
                            username=params['username']
                        )
                       
                        self.logger.info(f"send_miniapp返回: {success}")
                        if success:
                            self.logger.info(f"✅ 成功发送小程序给私聊用户: {from_wxid}, 聊天ID: {room_wxid}")
                        else:
                            raise Exception("发送小程序失败")

                    except Exception as e:
                        self.logger.error(f"❌ 发送小程序失败: {str(e)}")
                        self.logger.error(f"异常类型: {type(e).__name__}")
                        # 如果小程序发送失败，发送文本形式
                        fallback_text = f"小程序: {params.get('title', '未知小程序')}"
                        wechat_instance.send_text(target_id, fallback_text)
                        self.logger.info("发送了fallback文本")
                    return

                # 检查是否有视频发送参数
                if 'video_path' in params:
                    self.logger.info("检测到视频发送格式，尝试发送视频")
                    try:
                        self.logger.info(f"调用send_video: conversation_id={target_id}, file_path={params['video_path']}")

                        success = self.wechat_handler.send_video(
                            conversation_id=target_id,
                            file_path=params['video_path']
                        )

                        if success:
                            self.logger.info(f"✅ 成功发送视频给私聊用户: {from_wxid}, 聊天ID: {room_wxid}")
                        else:
                            raise Exception("发送视频失败")

                    except Exception as e:
                        self.logger.error(f"❌ 发送视频失败: {str(e)}")
                        self.logger.error(f"异常类型: {type(e).__name__}")
                        # 如果视频发送失败，发送文本形式
                        fallback_text = f"视频路径: {params['video_path']}"
                        wechat_instance.send_text(target_id, fallback_text)
                        self.logger.info("发送了fallback文本")
                    return

                # 检查是否有文件发送参数
                if 'file_path' in params:
                    self.logger.info("检测到文件发送格式，尝试发送图片/文件")
                    try:
                        self.logger.info(f"调用send_image: conversation_id={target_id}, file_path={params['file_path']}")

                        success = self.wechat_handler.send_image(
                            conversation_id=target_id,
                            file_path=params['file_path']
                        )

                        if success:
                            self.logger.info(f"✅ 成功发送图片/文件给私聊用户: {from_wxid}, 聊天ID: {room_wxid}")
                        else:
                            raise Exception("发送图片/文件失败")

                    except Exception as e:
                        self.logger.error(f"❌ 发送图片/文件失败: {str(e)}")
                        self.logger.error(f"异常类型: {type(e).__name__}")
                        # 如果图片/文件发送失败，发送文本形式
                        fallback_text = f"文件路径: {params['file_path']}"
                        wechat_instance.send_text(target_id, fallback_text)
                        self.logger.info("发送了fallback文本")
                    return

                # 检查是否有用户卡片参数
                if 'user_id' in params:
                    self.logger.info("检测到用户卡片格式，尝试发送用户卡片")
                    try:
                        self.logger.info(f"调用send_card: conversation_id={target_id}, user_id={params['user_id']}")

                        success = self.wechat_handler.send_card(
                            conversation_id=target_id,
                            user_id=params['user_id']
                        )

                        if success:
                            self.logger.info(f"✅ 成功发送用户卡片给私聊用户: {from_wxid}, 聊天ID: {room_wxid}")
                        else:
                            raise Exception("发送用户卡片失败")

                    except Exception as e:
                        self.logger.error(f"❌ 发送用户卡片失败: {str(e)}")
                        self.logger.error(f"异常类型: {type(e).__name__}")
                        # 如果用户卡片发送失败，发送文本形式
                        fallback_text = f"推荐用户: {params['user_id']}"
                        wechat_instance.send_text(target_id, fallback_text)
                        self.logger.info("发送了fallback文本")
                    return

                # 检查是否有链接卡片参数
                required_params = ['title', 'desc', 'url', 'img']
                missing_params = [p for p in required_params if p not in params]

                if not missing_params:
                    self.logger.info("所有链接卡片参数都存在，尝试发送链接卡片")
                    # 发送链接卡片
                    try:
                        self.logger.info(f"调用send_link_card: conversation_id={target_id}, title={params['title']}, desc={params['desc']}, url={params['url']}, image_url={params['img']}")

                        success = self.wechat_handler.send_link_card(
                            conversation_id=target_id,
                            title=params['title'],
                            desc=params['desc'],
                            url=params['url'],
                            image_url=params['img']
                        )

                        if success:
                            self.logger.info(f"✅ 成功发送链接卡片给私聊用户: {from_wxid}, 聊天ID: {room_wxid}")
                        else:
                            raise Exception("发送链接卡片失败")

                    except Exception as e:
                        self.logger.error(f"❌ 发送链接卡片失败: {str(e)}")
                        self.logger.error(f"异常类型: {type(e).__name__}")
                        # 如果链接卡片发送失败，发送文本形式
                        fallback_text = f"标题: {params['title']}\n描述: {params['desc']}\n链接: {params['url']}"
                        wechat_instance.send_text(target_id, fallback_text)
                        self.logger.info("发送了fallback文本")
                        
                else:
                    self.logger.warning(f"链接卡片参数不完整，缺失: {missing_params}")
                    # 如果没有完整的链接卡片参数，发送普通文本
                    normal_text = self._extract_normal_text_from_special_reply(reply_content)
                    if normal_text:
                        wechat_instance.send_text(target_id, normal_text)
                        self.logger.info(f"发送普通文本回复给私聊用户: {from_wxid}, 聊天ID: {room_wxid}")
                    else:
                        # 如果没有普通文本，发送原始内容
                        wechat_instance.send_text(target_id, reply_content)
                        self.logger.info(f"发送原始内容给私聊用户: {from_wxid}, 聊天ID: {room_wxid}")
            else:
                self.logger.info("普通文本回复")
                # 普通文本回复
                wechat_instance.send_text(target_id, reply_content)
                self.logger.info(f"发送选项回复给私聊用户: {from_wxid}, 聊天ID: {room_wxid}")

        except Exception as e:
            self.logger.error(f"处理特殊回复格式失败: {str(e)}")
            self.logger.error(f"异常类型: {type(e).__name__}")
            # 发送原始内容作为备用
            wechat_instance.send_text(target_id, reply_content)

    def _parse_special_reply_params(self, reply_content: str) -> Dict[str, str]:
        """
        解析特殊回复格式的参数

        Args:
            reply_content: 回复内容

        Returns:
            解析出的参数字典
        """
        params = {}
        lines = reply_content.split('\n')

        for line in lines:
            line = line.strip()
            if '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip()

                # 支持的所有参数
                supported_params = [
                    'wait_reply', 'title', 'desc', 'url', 'img', 'user_id', 'file_path', 'video_path',
                    'aes_key', 'file_id', 'size', 'appicon', 'appid', 'appname',
                    'page_path', 'username'
                ]

                if key in supported_params:
                    params[key] = value

        return params

    def _extract_normal_text_from_special_reply(self, reply_content: str) -> str:
        """
        从特殊回复格式中提取普通文本

        Args:
            reply_content: 回复内容

        Returns:
            提取的普通文本
        """
        lines = reply_content.split('\n')
        normal_lines = []

        for line in lines:
            line = line.strip()
            # 如果不是特殊参数行，则作为普通文本
            # 排除所有特殊参数
            special_params = [
                'wait_reply', 'title', 'desc', 'url', 'img', 'user_id', 'file_path', 'video_path',
                'aes_key', 'file_id', 'size', 'appicon', 'appid', 'appname',
                'page_path', 'username'
            ]

            if not any(line.startswith(param + '=') for param in special_params):
                normal_lines.append(line)

        return '\n'.join(normal_lines).strip()
