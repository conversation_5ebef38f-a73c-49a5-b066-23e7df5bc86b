# -*- coding: utf-8 -*-
"""
测试用户卡片特殊格式功能
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.core.wechat_handler import WeChatHandler
from src.core.message_processor import MessageProcessor


def test_user_card_format():
    """测试用户卡片特殊格式"""
    print("测试用户卡片特殊格式功能...")
    
    try:
        # 创建微信处理器和消息处理器
        wechat_handler = WeChatHandler()
        message_processor = MessageProcessor(wechat_handler)
        
        # 测试用户卡片格式解析
        test_reply_content = """wait_reply=正在为您推荐专业客服...
user_id=customer_service_001"""
        
        print(f"测试回复内容:\n{test_reply_content}")
        
        # 解析特殊格式参数
        params = message_processor._parse_special_reply_params(test_reply_content)
        print(f"解析到的参数: {params}")
        
        # 验证参数
        expected_params = {
            'wait_reply': '正在为您推荐专业客服...',
            'user_id': 'customer_service_001'
        }
        
        if params == expected_params:
            print("✅ 参数解析正确")
        else:
            print(f"❌ 参数解析错误，期望: {expected_params}, 实际: {params}")
            return False
        
        # 测试链接卡片格式（确保兼容性）
        test_link_card_content = """wait_reply=正在为您查询...
title=查询结果
desc=这是查询结果的描述
url=https://example.com
img=https://example.com/image.jpg"""
        
        print(f"\n测试链接卡片内容:\n{test_link_card_content}")
        
        link_params = message_processor._parse_special_reply_params(test_link_card_content)
        print(f"解析到的链接卡片参数: {link_params}")
        
        expected_link_params = {
            'wait_reply': '正在为您查询...',
            'title': '查询结果',
            'desc': '这是查询结果的描述',
            'url': 'https://example.com',
            'img': 'https://example.com/image.jpg'
        }
        
        if link_params == expected_link_params:
            print("✅ 链接卡片参数解析正确")
        else:
            print(f"❌ 链接卡片参数解析错误")
            return False
        
        # 测试混合格式（包含普通文本）
        test_mixed_content = """这是普通文本内容
wait_reply=请稍等...
user_id=support_agent_002
这是另一行普通文本"""
        
        print(f"\n测试混合内容:\n{test_mixed_content}")
        
        mixed_params = message_processor._parse_special_reply_params(test_mixed_content)
        normal_text = message_processor._extract_normal_text_from_special_reply(test_mixed_content)
        
        print(f"解析到的混合参数: {mixed_params}")
        print(f"提取的普通文本: {normal_text}")
        
        expected_mixed_params = {
            'wait_reply': '请稍等...',
            'user_id': 'support_agent_002'
        }
        expected_normal_text = "这是普通文本内容\n这是另一行普通文本"
        
        if mixed_params == expected_mixed_params and normal_text == expected_normal_text:
            print("✅ 混合内容解析正确")
        else:
            print(f"❌ 混合内容解析错误")
            return False
        
        print("\n🎉 所有用户卡片格式测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_wechat_handler_send_card():
    """测试微信处理器的send_card方法"""
    print("\n测试微信处理器的send_card方法...")
    
    try:
        wechat_handler = WeChatHandler()
        
        # 测试发送用户卡片
        result = wechat_handler.send_card("test_conversation", "test_user_123")
        
        if result:
            print("✅ send_card方法调用成功")
            return True
        else:
            print("❌ send_card方法调用失败")
            return False
            
    except Exception as e:
        print(f"❌ send_card方法测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("=" * 60)
    print("用户卡片特殊格式功能测试")
    print("=" * 60)
    
    # 测试格式解析
    format_test_ok = test_user_card_format()
    
    # 测试发送方法
    send_test_ok = test_wechat_handler_send_card()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print(f"格式解析测试: {'✅ 通过' if format_test_ok else '❌ 失败'}")
    print(f"发送方法测试: {'✅ 通过' if send_test_ok else '❌ 失败'}")
    
    if format_test_ok and send_test_ok:
        print("\n🎉 所有测试通过！用户卡片功能已成功添加！")
        print("\n使用方法:")
        print("在选项回复内容中使用以下格式:")
        print("wait_reply=等待消息（可选）")
        print("user_id=用户ID")
        return True
    else:
        print("\n❌ 部分测试失败，请检查问题。")
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        input("\n按回车键退出...")
    sys.exit(0 if success else 1)
