# -*- coding: utf-8 -*-
"""
微信私聊选项选择机器人 - 主入口文件
模块化版本 v2.1.0

这是一个企业微信私聊自动回复机器人，支持：
- 树状选项菜单配置
- 私聊消息处理
- 特殊回复格式（链接卡片等）
- 用户会话管理
- GUI界面管理

项目结构：
- src/core/: 核心业务逻辑（微信处理、消息处理）
- src/gui/: 用户界面模块
- src/config/: 配置管理模块
- src/utils/: 工具类模块
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.gui.main_window import WeChatGUI


def main():
    """主函数 - 程序入口点"""
    try:
        # 创建并运行主窗口
        app = WeChatGUI()
        app.run()
        
    except Exception as e:
        import traceback
        print(f"程序启动失败: {e}")
        print(traceback.format_exc())
        input("按回车键退出...")


if __name__ == "__main__":
    main()
