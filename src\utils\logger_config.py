# -*- coding: utf-8 -*-
"""
日志配置工具模块 - 提供统一的日志配置功能
"""

import os
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from logging.handlers import RotatingFileHandler


class LoggerConfig:
    """
    日志配置工具类 - 提供统一的日志配置功能
    """
    
    @staticmethod
    def create_logger_config(
        log_file_path: Optional[str] = None,
        console_output: bool = True,
        log_level: str = 'INFO',
        max_file_size: int = 10,
        backup_count: int = 5,
        auto_create_path: bool = True
    ) -> Dict[str, Any]:
        """
        创建日志配置字典
        
        Args:
            log_file_path: 日志文件保存路径，如果为None则不保存文件
            console_output: 是否输出到控制台
            log_level: 日志级别 ('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')
            max_file_size: 最大文件大小(MB)
            backup_count: 备份文件数量
            auto_create_path: 是否自动创建日志目录
            
        Returns:
            日志配置字典
        """
        # 如果启用自动路径创建且未指定路径，则创建默认路径
        if auto_create_path and log_file_path is None:
            log_file_path = LoggerConfig.get_default_log_path()
        
        return {
            'log_file_path': log_file_path,
            'console_output': console_output,
            'log_level': log_level.upper(),
            'max_file_size': max_file_size,
            'backup_count': backup_count
        }
    
    @staticmethod
    def get_default_log_path() -> str:
        """
        获取默认日志文件路径
        
        Returns:
            默认日志文件路径
        """
        # 创建logs目录
        logs_dir = os.path.join(os.getcwd(), 'logs')
        
        # 生成带时间戳的日志文件名
        timestamp = datetime.now().strftime('%Y%m%d')
        log_filename = f'wechat_bot_{timestamp}.log'
        
        return os.path.join(logs_dir, log_filename)
    
    @staticmethod
    def setup_logger(
        logger_name: str,
        log_config: Dict[str, Any]
    ) -> logging.Logger:
        """
        设置并返回配置好的logger
        
        Args:
            logger_name: logger名称
            log_config: 日志配置字典
            
        Returns:
            配置好的logger实例
        """
        # 获取配置参数
        log_file_path = log_config.get('log_file_path')
        console_output = log_config.get('console_output', True)
        log_level = getattr(logging, log_config.get('log_level', 'INFO').upper())
        max_file_size = log_config.get('max_file_size', 10) * 1024 * 1024  # 转换为字节
        backup_count = log_config.get('backup_count', 5)
        
        # 创建logger
        logger = logging.getLogger(logger_name)
        logger.setLevel(log_level)
        
        # 清除已有的处理器
        logger.handlers.clear()
        
        # 设置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 添加控制台处理器
        if console_output:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(log_level)
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
        
        # 添加文件处理器
        if log_file_path:
            try:
                # 确保日志目录存在
                log_dir = os.path.dirname(log_file_path)
                if log_dir and not os.path.exists(log_dir):
                    os.makedirs(log_dir, exist_ok=True)
                
                # 使用RotatingFileHandler支持日志轮转
                file_handler = RotatingFileHandler(
                    log_file_path,
                    maxBytes=max_file_size,
                    backupCount=backup_count,
                    encoding='utf-8'
                )
                file_handler.setLevel(log_level)
                file_handler.setFormatter(formatter)
                logger.addHandler(file_handler)
                
                logger.info(f"日志文件已配置: {log_file_path}")
                
            except Exception as e:
                # 如果文件处理器配置失败，至少保证控制台输出可用
                if not console_output:
                    console_handler = logging.StreamHandler()
                    console_handler.setLevel(log_level)
                    console_handler.setFormatter(formatter)
                    logger.addHandler(console_handler)
                
                logger.error(f"配置日志文件失败: {e}")
        
        return logger
    
    @staticmethod
    def get_preset_configs() -> Dict[str, Dict[str, Any]]:
        """
        获取预设的日志配置
        
        Returns:
            预设配置字典
        """
        return {
            'development': LoggerConfig.create_logger_config(
                console_output=True,
                log_level='DEBUG',
                max_file_size=5,
                backup_count=3
            ),
            'production': LoggerConfig.create_logger_config(
                console_output=False,
                log_level='INFO',
                max_file_size=50,
                backup_count=10
            ),
            'debug': LoggerConfig.create_logger_config(
                console_output=True,
                log_level='DEBUG',
                max_file_size=1,
                backup_count=2
            ),
            'minimal': LoggerConfig.create_logger_config(
                log_file_path=None,
                console_output=True,
                log_level='WARNING'
            )
        }


# 便捷函数
def get_logger_config(preset: str = 'development', **kwargs) -> Dict[str, Any]:
    """
    获取日志配置的便捷函数
    
    Args:
        preset: 预设配置名称 ('development', 'production', 'debug', 'minimal')
        **kwargs: 额外的配置参数，会覆盖预设配置
        
    Returns:
        日志配置字典
    """
    presets = LoggerConfig.get_preset_configs()
    config = presets.get(preset, presets['development']).copy()
    config.update(kwargs)
    return config


def setup_logger(logger_name: str, preset: str = 'development', **kwargs) -> logging.Logger:
    """
    设置logger的便捷函数
    
    Args:
        logger_name: logger名称
        preset: 预设配置名称
        **kwargs: 额外的配置参数
        
    Returns:
        配置好的logger实例
    """
    config = get_logger_config(preset, **kwargs)
    return LoggerConfig.setup_logger(logger_name, config)
