# -*- coding: utf-8 -*-
"""
模拟ttkbootstrap模块 - 用于在没有ttkbootstrap时提供基本功能
"""

import tkinter as tk
from tkinter import ttk as tkinter_ttk


class Style:
    """模拟ttkbootstrap的Style类"""
    
    def __init__(self, theme='default'):
        self.theme = theme
        self.master = tk.Tk()
        
    def configure(self, style, **kwargs):
        """模拟样式配置"""
        pass


# 模拟ttkbootstrap的组件，使用标准tkinter.ttk
Frame = tkinter_ttk.Frame
Label = tkinter_ttk.Label
Button = tkinter_ttk.Button
Entry = tkinter_ttk.Entry
Text = tk.Text
Scrollbar = tkinter_ttk.Scrollbar
Treeview = tkinter_ttk.Treeview
Notebook = tkinter_ttk.Notebook
LabelFrame = tkinter_ttk.LabelFrame
Radiobutton = tkinter_ttk.Radiobutton
Checkbutton = tkinter_ttk.Checkbutton
Combobox = tkinter_ttk.Combobox
Progressbar = tkinter_ttk.Progressbar
Scale = tkinter_ttk.Scale
Separator = tkinter_ttk.Separator
Sizegrip = tkinter_ttk.Sizegrip
Panedwindow = tkinter_ttk.Panedwindow

# 模拟bootstyle参数（实际上会被忽略）
def _ignore_bootstyle(**kwargs):
    """移除bootstyle参数，避免错误"""
    if 'bootstyle' in kwargs:
        del kwargs['bootstyle']
    return kwargs

# 重写组件类以忽略bootstyle参数
class Frame(tkinter_ttk.Frame):
    def __init__(self, master=None, **kwargs):
        kwargs = _ignore_bootstyle(**kwargs)
        super().__init__(master, **kwargs)

class Label(tkinter_ttk.Label):
    def __init__(self, master=None, **kwargs):
        kwargs = _ignore_bootstyle(**kwargs)
        super().__init__(master, **kwargs)

class Button(tkinter_ttk.Button):
    def __init__(self, master=None, **kwargs):
        kwargs = _ignore_bootstyle(**kwargs)
        super().__init__(master, **kwargs)

class Entry(tkinter_ttk.Entry):
    def __init__(self, master=None, **kwargs):
        kwargs = _ignore_bootstyle(**kwargs)
        super().__init__(master, **kwargs)

class Scrollbar(tkinter_ttk.Scrollbar):
    def __init__(self, master=None, **kwargs):
        kwargs = _ignore_bootstyle(**kwargs)
        super().__init__(master, **kwargs)

class Treeview(tkinter_ttk.Treeview):
    def __init__(self, master=None, **kwargs):
        kwargs = _ignore_bootstyle(**kwargs)
        super().__init__(master, **kwargs)

class Notebook(tkinter_ttk.Notebook):
    def __init__(self, master=None, **kwargs):
        kwargs = _ignore_bootstyle(**kwargs)
        super().__init__(master, **kwargs)

class LabelFrame(tkinter_ttk.LabelFrame):
    def __init__(self, master=None, **kwargs):
        kwargs = _ignore_bootstyle(**kwargs)
        super().__init__(master, **kwargs)

class Radiobutton(tkinter_ttk.Radiobutton):
    def __init__(self, master=None, **kwargs):
        kwargs = _ignore_bootstyle(**kwargs)
        super().__init__(master, **kwargs)

class Checkbutton(tkinter_ttk.Checkbutton):
    def __init__(self, master=None, **kwargs):
        kwargs = _ignore_bootstyle(**kwargs)
        super().__init__(master, **kwargs)

class Combobox(tkinter_ttk.Combobox):
    def __init__(self, master=None, **kwargs):
        kwargs = _ignore_bootstyle(**kwargs)
        super().__init__(master, **kwargs)

class Progressbar(tkinter_ttk.Progressbar):
    def __init__(self, master=None, **kwargs):
        kwargs = _ignore_bootstyle(**kwargs)
        super().__init__(master, **kwargs)

class Scale(tkinter_ttk.Scale):
    def __init__(self, master=None, **kwargs):
        kwargs = _ignore_bootstyle(**kwargs)
        super().__init__(master, **kwargs)

class Separator(tkinter_ttk.Separator):
    def __init__(self, master=None, **kwargs):
        kwargs = _ignore_bootstyle(**kwargs)
        super().__init__(master, **kwargs)

class Sizegrip(tkinter_ttk.Sizegrip):
    def __init__(self, master=None, **kwargs):
        kwargs = _ignore_bootstyle(**kwargs)
        super().__init__(master, **kwargs)

class Panedwindow(tkinter_ttk.Panedwindow):
    def __init__(self, master=None, **kwargs):
        kwargs = _ignore_bootstyle(**kwargs)
        super().__init__(master, **kwargs)
