#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查当前间隔时间设置的脚本
"""

import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def check_config_file():
    """检查配置文件中的间隔时间设置"""
    print("=== 检查配置文件 ===")
    
    config_files = ['config.json', 'config-private.json']
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"\n--- {config_file} ---")
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                private_config = config.get('group_option_configs', {}).get('private_chat', {})
                interval_minutes = private_config.get('interval_minutes')
                
                if interval_minutes is not None:
                    print(f"间隔时间设置: {interval_minutes}分钟 ({interval_minutes * 60}秒)")
                else:
                    print("未找到间隔时间设置")
                    
            except Exception as e:
                print(f"读取配置文件失败: {e}")
        else:
            print(f"{config_file} 不存在")


def check_default_config():
    """检查默认配置"""
    print("\n=== 检查默认配置 ===")
    
    try:
        from src.config.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        default_config = config_manager._get_default_config()
        
        private_config = default_config.get('group_option_configs', {}).get('private_chat', {})
        interval_minutes = private_config.get('interval_minutes')
        
        if interval_minutes is not None:
            print(f"默认间隔时间: {interval_minutes}分钟 ({interval_minutes * 60}秒)")
        else:
            print("默认配置中未找到间隔时间设置")
            
    except Exception as e:
        print(f"检查默认配置失败: {e}")


def simulate_message_processor():
    """模拟消息处理器的初始化过程"""
    print("\n=== 模拟消息处理器初始化 ===")
    
    try:
        # 模拟 ntwork 模块
        class MockNtwork:
            class WeWork:
                def send_text(self, conversation_id, text):
                    pass

        sys.modules['ntwork'] = MockNtwork()
        
        from src.core.message_processor import MessageProcessor
        
        # 创建模拟的微信处理器
        class MockWeChatHandler:
            def __init__(self):
                self.user_id = "test_bot"
                self.name = "测试机器人"
                self.exit_flag = False
        
        mock_wechat_handler = MockWeChatHandler()
        
        # 创建消息处理器
        processor = MessageProcessor(mock_wechat_handler)
        
        print(f"消息处理器默认间隔时间: {processor.interval_seconds}秒 ({processor.interval_seconds // 60}分钟)")
        
        # 模拟配置加载
        from src.config.config_manager import ConfigManager
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        private_config = config.get('group_option_configs', {}).get('private_chat', {})
        interval_minutes = private_config.get('interval_minutes', 30)
        
        print(f"配置文件中的间隔时间: {interval_minutes}分钟")
        
        # 应用配置
        processor.set_interval_seconds(interval_minutes * 60)
        
        print(f"应用配置后的间隔时间: {processor.interval_seconds}秒 ({processor.interval_seconds // 60}分钟)")
        
    except Exception as e:
        print(f"模拟消息处理器失败: {e}")
        import traceback
        print(f"异常堆栈: {traceback.format_exc()}")


if __name__ == "__main__":
    check_config_file()
    check_default_config()
    simulate_message_processor()
