2025-07-21 07:51:39 - src.core.message_processor.1464070459888 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250721.log
2025-07-21 07:51:39 - src.core.message_processor.1464070459888 - INFO - 消息处理器日志系统初始化完成
2025-07-21 07:51:39 - src.core.message_processor.1464070459888 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250721.log
2025-07-21 07:51:39 - src.core.message_processor.1464070459888 - INFO - 消息处理器日志系统初始化完成
2025-07-21 07:51:39 - src.core.message_processor.1464070459888 - INFO - 用户会话超时，清理会话: test_user_123
2025-07-21 07:51:39 - src.core.message_processor.1464070459888 - ERROR - 发送会话超时消息失败: 
2025-07-21 21:11:24 - src.core.message_processor.3040528492240 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250721.log
2025-07-21 21:11:24 - src.core.message_processor.3040528492240 - INFO - 消息处理器日志系统初始化完成
2025-07-21 21:11:24 - src.core.message_processor.3040528492240 - INFO - 收到原始消息: {"data": {"room_wxid": "S:1234567890", "from_wxid": "user123", "from_name": "测试用户", "msg": "1", "content_type": 0}}
2025-07-21 21:11:24 - src.core.message_processor.3040528492240 - INFO - 机器人名字: None
2025-07-21 21:11:24 - src.core.message_processor.3040528492240 - INFO - 收到系统消息: {"data": {"type": "friend_verify", "content": "用户通过了好友验证", "conversation_id": "S:1234567890", "from_wxid": "user456"}}
2025-07-21 21:11:24 - src.core.message_processor.3040528492240 - INFO - 处理新联系人: user456, 会话ID: S:1234567890
2025-07-21 21:11:24 - src.core.message_processor.3040528492240 - INFO - 向新联系人发送欢迎消息并创建会话: user456, 会话ID: user456
2025-07-21 21:11:24 - src.core.message_processor.3040528492240 - INFO - 忽略重复接收的消息: None
2025-07-21 21:11:24 - src.core.message_processor.3040528492240 - INFO - 收到系统消息: {"data": {"type": "friend_verify", "content": "用户通过了好友验证", "conversation_id": "S:1234567890", "from_wxid": "user456"}}
2025-07-21 21:11:24 - src.core.message_processor.3040528492240 - INFO - 处理新联系人: user456, 会话ID: S:1234567890
2025-07-21 21:11:24 - src.core.message_processor.3040528492240 - INFO - 用户 user456 已有活跃会话，跳过创建
2025-07-21 21:11:24 - src.core.message_processor.3040528493440 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250721.log
2025-07-21 21:11:24 - src.core.message_processor.3040528493440 - INFO - 消息处理器日志系统初始化完成
2025-07-21 21:11:24 - src.core.message_processor.3040528493440 - INFO - 用户会话超时，清理会话: expired_user
2025-07-21 21:11:24 - src.core.message_processor.3040528493440 - ERROR - 发送会话超时消息失败: 
2025-07-21 21:11:24 - src.core.message_processor.3040528493440 - INFO - 清理过期会话: expired_user_0
2025-07-21 21:11:24 - src.core.message_processor.3040528493440 - ERROR - 发送会话超时消息失败: 
2025-07-21 21:11:24 - src.core.message_processor.3040528493440 - INFO - 清理过期会话: expired_user_1
2025-07-21 21:11:24 - src.core.message_processor.3040528493440 - ERROR - 发送会话超时消息失败: 
2025-07-21 21:11:24 - src.core.message_processor.3040528493440 - INFO - 清理过期会话: expired_user_2
2025-07-21 21:11:24 - src.core.message_processor.3040528493440 - ERROR - 发送会话超时消息失败: 
2025-07-21 21:11:24 - src.core.message_processor.3040528493440 - INFO - 清理了 3 个过期会话
2025-07-21 21:13:17 - src.core.message_processor.2191157528368 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250721.log
2025-07-21 21:13:17 - src.core.message_processor.2191157528368 - INFO - 消息处理器日志系统初始化完成
2025-07-21 21:19:37 - src.core.message_processor.2191157528368 - INFO - 收到原始消息: {"data": {"appinfo": "1314938783483953314", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "1856", "receiver": "1688854710715177", "send_time": "1753103975", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1018200"}, "type": 11041}
2025-07-21 21:19:37 - src.core.message_processor.2191157528368 - INFO - 机器人名字: 邾俊勇
2025-07-21 21:19:37 - src.core.message_processor.2191157528368 - INFO - 消息类型: 2
2025-07-21 21:19:37 - src.core.message_processor.2191157528368 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 1
2025-07-21 21:19:37 - src.core.message_processor.2191157528368 - INFO - 消息类型: 私聊
2025-07-21 21:19:37 - src.core.message_processor.2191157528368 - INFO - 用户 7881299718050125 没有活跃会话，忽略文本消息
2025-07-21 21:20:46 - src.core.message_processor.2191157528368 - INFO - 收到原始消息: {"data": {"appinfo": "dVownwYJRjmTLPF", "at_list": [], "content": "我是嫒宝粑粑", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "1857", "receiver": "1688854710715177", "send_time": "1753104045", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1018222"}, "type": 11041}
2025-07-21 21:20:46 - src.core.message_processor.2191157528368 - INFO - 机器人名字: 邾俊勇
2025-07-21 21:20:46 - src.core.message_processor.2191157528368 - INFO - 消息类型: 2
2025-07-21 21:20:46 - src.core.message_processor.2191157528368 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 我是嫒宝粑粑
2025-07-21 21:20:46 - src.core.message_processor.2191157528368 - INFO - 消息类型: 私聊
2025-07-21 21:20:46 - src.core.message_processor.2191157528368 - INFO - 用户 7881299718050125 没有活跃会话，忽略文本消息
2025-07-21 21:20:47 - src.core.message_processor.2191157528368 - INFO - 收到系统消息: {"data": {"appinfo": "DAvzChpZQ8CDcm-", "content": "以上是打招呼内容", "content_type": 1011, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "receiver": "1688854710715177", "send_time": "1753104045", "sender": "7881299718050125", "sender_name": "", "server_id": "1018224"}, "type": 11187}
2025-07-21 21:20:47 - src.core.message_processor.2191157528368 - INFO - 收到系统消息: {"data": {"appinfo": "QSqD6UROTb-3Usk", "content": "你已添加了嫒宝粑粑，现在可以开始聊天了。", "content_type": 1011, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "receiver": "1688854710715177", "send_time": "1753104046", "sender": "7881299718050125", "sender_name": "", "server_id": "1018227"}, "type": 11187}
2025-07-21 21:21:23 - src.core.message_processor.2191157528368 - INFO - 收到原始消息: {"data": {"appinfo": "7391672643839379369", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "1860", "receiver": "1688854710715177", "send_time": "1753104080", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1018231"}, "type": 11041}
2025-07-21 21:21:23 - src.core.message_processor.2191157528368 - INFO - 机器人名字: 邾俊勇
2025-07-21 21:21:23 - src.core.message_processor.2191157528368 - INFO - 消息类型: 2
2025-07-21 21:21:23 - src.core.message_processor.2191157528368 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 1
2025-07-21 21:21:23 - src.core.message_processor.2191157528368 - INFO - 消息类型: 私聊
2025-07-21 21:21:23 - src.core.message_processor.2191157528368 - INFO - 用户 7881299718050125 没有活跃会话，忽略文本消息
2025-07-21 21:35:00 - src.core.message_processor.2451385641232 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250721.log
2025-07-21 21:35:00 - src.core.message_processor.2451385641232 - INFO - 消息处理器日志系统初始化完成
2025-07-21 21:36:58 - src.core.message_processor.2451385641232 - INFO - 收到原始消息: {"data": {"appinfo": "ejDrYHF4SiiEOLS", "at_list": [], "content": "我是嫒宝粑粑", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "1861", "receiver": "1688854710715177", "send_time": "1753105017", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1018255"}, "type": 11041}
2025-07-21 21:36:58 - src.core.message_processor.2451385641232 - INFO - 机器人名字: 邾俊勇
2025-07-21 21:36:58 - src.core.message_processor.2451385641232 - INFO - 消息类型: 2
2025-07-21 21:36:58 - src.core.message_processor.2451385641232 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 我是嫒宝粑粑
2025-07-21 21:36:58 - src.core.message_processor.2451385641232 - INFO - 消息类型: 私聊
2025-07-21 21:36:58 - src.core.message_processor.2451385641232 - INFO - 用户 7881299718050125 没有活跃会话，忽略文本消息
2025-07-21 21:36:58 - src.core.message_processor.2451385641232 - INFO - 收到系统消息: {"data": {"appinfo": "I-RVjZo4SHerxF4", "content": "以上是打招呼内容", "content_type": 1011, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "receiver": "1688854710715177", "send_time": "1753105017", "sender": "7881299718050125", "sender_name": "", "server_id": "1018258"}, "type": 11187}
2025-07-21 21:36:58 - src.core.message_processor.2451385641232 - INFO - 收到系统消息: {"data": {"appinfo": "Z-jkl7opTcGFAs1", "content": "你已添加了嫒宝粑粑，现在可以开始聊天了。", "content_type": 1011, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "receiver": "1688854710715177", "send_time": "1753105017", "sender": "7881299718050125", "sender_name": "", "server_id": "1018260"}, "type": 11187}
2025-07-21 21:36:58 - src.core.message_processor.2451385641232 - ERROR - 系统消息处理异常: not all arguments converted during string formatting
Traceback (most recent call last):
  File "d:\python\wechat\ntwork\客服选项选择\src\core\message_processor.py", line 202, in on_sys_message
    if self._is_new_contact_message(data):
  File "d:\python\wechat\ntwork\客服选项选择\src\core\message_processor.py", line 222, in _is_new_contact_message
    self.logger.info("检查是否是新联系人消息----","已添加了" in content or "为你服务" in content or "当前为客服聊天" in content)
  File "D:\ProgramData\miniconda3\envs\ntwork\lib\logging\__init__.py", line 1446, in info
    self._log(INFO, msg, args, **kwargs)
  File "D:\ProgramData\miniconda3\envs\ntwork\lib\logging\__init__.py", line 1589, in _log
    self.handle(record)
  File "D:\ProgramData\miniconda3\envs\ntwork\lib\logging\__init__.py", line 1599, in handle
    self.callHandlers(record)
  File "D:\ProgramData\miniconda3\envs\ntwork\lib\logging\__init__.py", line 1661, in callHandlers
    hdlr.handle(record)
  File "D:\ProgramData\miniconda3\envs\ntwork\lib\logging\__init__.py", line 952, in handle
    self.emit(record)
  File "d:\python\wechat\ntwork\客服选项选择\src\utils\logging_handler.py", line 32, in emit
    msg = self.format(record)
  File "D:\ProgramData\miniconda3\envs\ntwork\lib\logging\__init__.py", line 927, in format
    return fmt.format(record)
  File "D:\ProgramData\miniconda3\envs\ntwork\lib\logging\__init__.py", line 663, in format
    record.message = record.getMessage()
  File "D:\ProgramData\miniconda3\envs\ntwork\lib\logging\__init__.py", line 367, in getMessage
    msg = msg % self.args
TypeError: not all arguments converted during string formatting
2025-07-21 21:36:58 - src.core.message_processor.2451385641232 - ERROR - 系统消息处理异常: not all arguments converted during string formatting
Traceback (most recent call last):
  File "d:\python\wechat\ntwork\客服选项选择\src\core\message_processor.py", line 202, in on_sys_message
    if self._is_new_contact_message(data):
  File "d:\python\wechat\ntwork\客服选项选择\src\core\message_processor.py", line 222, in _is_new_contact_message
    self.logger.info("检查是否是新联系人消息----","已添加了" in content or "为你服务" in content or "当前为客服聊天" in content)
  File "D:\ProgramData\miniconda3\envs\ntwork\lib\logging\__init__.py", line 1446, in info
    self._log(INFO, msg, args, **kwargs)
  File "D:\ProgramData\miniconda3\envs\ntwork\lib\logging\__init__.py", line 1589, in _log
    self.handle(record)
  File "D:\ProgramData\miniconda3\envs\ntwork\lib\logging\__init__.py", line 1599, in handle
    self.callHandlers(record)
  File "D:\ProgramData\miniconda3\envs\ntwork\lib\logging\__init__.py", line 1661, in callHandlers
    hdlr.handle(record)
  File "D:\ProgramData\miniconda3\envs\ntwork\lib\logging\__init__.py", line 952, in handle
    self.emit(record)
  File "d:\python\wechat\ntwork\客服选项选择\src\utils\logging_handler.py", line 32, in emit
    msg = self.format(record)
  File "D:\ProgramData\miniconda3\envs\ntwork\lib\logging\__init__.py", line 927, in format
    return fmt.format(record)
  File "D:\ProgramData\miniconda3\envs\ntwork\lib\logging\__init__.py", line 663, in format
    record.message = record.getMessage()
  File "D:\ProgramData\miniconda3\envs\ntwork\lib\logging\__init__.py", line 367, in getMessage
    msg = msg % self.args
TypeError: not all arguments converted during string formatting
2025-07-21 21:41:11 - src.core.message_processor.2214198470928 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250721.log
2025-07-21 21:41:11 - src.core.message_processor.2214198470928 - INFO - 消息处理器日志系统初始化完成
2025-07-21 21:42:19 - src.core.message_processor.2214198470928 - INFO - 收到原始消息: {"data": {"appinfo": "4WuouNbWRMS3DN6", "at_list": [], "content": "我是嫒宝粑粑", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "1864", "receiver": "1688854710715177", "send_time": "1753105338", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1018281"}, "type": 11041}
2025-07-21 21:42:19 - src.core.message_processor.2214198470928 - INFO - 机器人名字: 邾俊勇
2025-07-21 21:42:19 - src.core.message_processor.2214198470928 - INFO - 消息类型: 2
2025-07-21 21:42:19 - src.core.message_processor.2214198470928 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 我是嫒宝粑粑
2025-07-21 21:42:19 - src.core.message_processor.2214198470928 - INFO - 消息类型: 私聊
2025-07-21 21:42:19 - src.core.message_processor.2214198470928 - INFO - 用户 7881299718050125 没有活跃会话，忽略文本消息
2025-07-21 21:42:19 - src.core.message_processor.2214198470928 - INFO - 收到系统消息: {"data": {"appinfo": "6hR1WB_hRmmU99b", "content": "以上是打招呼内容", "content_type": 1011, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "receiver": "1688854710715177", "send_time": "1753105338", "sender": "7881299718050125", "sender_name": "", "server_id": "1018284"}, "type": 11187}
2025-07-21 21:42:19 - src.core.message_processor.2214198470928 - INFO - 收到系统消息: {"data": {"appinfo": "cxgd60p0S7uyYtn", "content": "你已添加了嫒宝粑粑，现在可以开始聊天了。", "content_type": 1011, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "receiver": "1688854710715177", "send_time": "1753105338", "sender": "7881299718050125", "sender_name": "", "server_id": "1018286"}, "type": 11187}
2025-07-21 21:42:19 - src.core.message_processor.2214198470928 - INFO - 检查是否是新联系人消息----False
2025-07-21 21:42:19 - src.core.message_processor.2214198470928 - INFO - 检查是否是新联系人消息----True
2025-07-21 21:49:30 - src.core.message_processor.2306875116608 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250721.log
2025-07-21 21:49:30 - src.core.message_processor.2306875116608 - INFO - 消息处理器日志系统初始化完成
2025-07-21 21:49:30 - src.core.message_processor.2306875116608 - INFO - 检查是否是新联系人消息----True
2025-07-21 21:49:30 - src.core.message_processor.2306875116608 - INFO - 检查是否是新联系人消息----True
2025-07-21 21:49:30 - src.core.message_processor.2306875116608 - INFO - 检查是否是新联系人消息----True
2025-07-21 21:49:30 - src.core.message_processor.2306875116608 - INFO - 检查是否是新联系人消息----False
2025-07-21 21:49:30 - src.core.message_processor.2306875116608 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250721.log
2025-07-21 21:49:30 - src.core.message_processor.2306875116608 - INFO - 消息处理器日志系统初始化完成
2025-07-21 21:49:30 - src.core.message_processor.2306875116608 - INFO - 收到系统消息: {"data": {"appinfo": "cxgd60p0S7uyYtn", "content": "你已添加了嫒宝粑粑，现在可以开始聊天了。", "content_type": 1011, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "receiver": "1688854710715177", "send_time": "1753105338", "sender": "7881299718050125", "sender_name": "", "server_id": "1018286"}, "type": 11187}
2025-07-21 21:49:30 - src.core.message_processor.2306875116608 - INFO - 检查是否是新联系人消息----True
2025-07-21 21:49:30 - src.core.message_processor.2306875116608 - INFO - 收到原始消息: {"data": {"room_wxid": "S:1688854710715177_7881299718050125", "from_wxid": "7881299718050125", "from_name": "嫒宝粑粑", "msg": "1", "content_type": 0}}
2025-07-21 21:49:30 - src.core.message_processor.2306875116608 - INFO - 机器人名字: None
2025-07-21 21:58:06 - src.core.message_processor.2880638613296 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250721.log
2025-07-21 21:58:06 - src.core.message_processor.2880638613296 - INFO - 消息处理器日志系统初始化完成
2025-07-21 22:02:03 - src.core.message_processor.1428067624464 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250721.log
2025-07-21 22:02:03 - src.core.message_processor.1428067624464 - INFO - 消息处理器日志系统初始化完成
2025-07-21 22:03:21 - src.core.message_processor.1428067624464 - INFO - 收到原始消息: {"data": {"appinfo": "XZ7pcialSoGGfua", "at_list": [], "content": "我是嫒宝粑粑", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "1867", "receiver": "1688854710715177", "send_time": "1753106600", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1018307"}, "type": 11041}
2025-07-21 22:03:21 - src.core.message_processor.1428067624464 - INFO - 机器人名字: 邾俊勇
2025-07-21 22:03:21 - src.core.message_processor.1428067624464 - INFO - 消息类型: 2
2025-07-21 22:03:21 - src.core.message_processor.1428067624464 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 我是嫒宝粑粑
2025-07-21 22:03:21 - src.core.message_processor.1428067624464 - INFO - 消息类型: 私聊
2025-07-21 22:03:21 - src.core.message_processor.1428067624464 - INFO - 用户 7881299718050125 没有活跃会话，忽略文本消息
2025-07-21 22:03:21 - src.core.message_processor.1428067624464 - INFO - 收到系统消息: {"data": {"appinfo": "8J_SwGZJRUuvI-Z", "content": "以上是打招呼内容", "content_type": 1011, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "receiver": "1688854710715177", "send_time": "1753106600", "sender": "7881299718050125", "sender_name": "", "server_id": "1018310"}, "type": 11187}
2025-07-21 22:03:21 - src.core.message_processor.1428067624464 - INFO - 收到系统消息: {"data": {"appinfo": "CrhiFav6SMu-n7T", "content": "你已添加了嫒宝粑粑，现在可以开始聊天了。", "content_type": 1011, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "receiver": "1688854710715177", "send_time": "1753106600", "sender": "7881299718050125", "sender_name": "", "server_id": "1018312"}, "type": 11187}
2025-07-21 22:03:21 - src.core.message_processor.1428067624464 - INFO - 检查是否是新联系人消息----False
2025-07-21 22:03:21 - src.core.message_processor.1428067624464 - INFO - 检查是否是新联系人消息----True
2025-07-21 22:03:21 - src.core.message_processor.1428067624464 - INFO - 不是新联系人消息，跳过处理
2025-07-21 22:03:21 - src.core.message_processor.1428067624464 - INFO - 不是新联系人消息，跳过处理
2025-07-21 22:07:51 - src.core.message_processor.2037063677600 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250721.log
2025-07-21 22:07:51 - src.core.message_processor.2037063677600 - INFO - 消息处理器日志系统初始化完成
2025-07-21 22:09:41 - src.core.message_processor.2037063677600 - INFO - 收到系统消息: {"data": {"appinfo": "tYlYgOpmSeGj2jQ", "content": "以上是打招呼内容", "content_type": 1011, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "receiver": "1688854710715177", "send_time": "1753106980", "sender": "7881299718050125", "sender_name": "", "server_id": "1018335"}, "type": 11187}
2025-07-21 22:09:41 - src.core.message_processor.2037063677600 - INFO - 收到系统消息: {"data": {"appinfo": "FwggBJreS6SS1bq", "content": "你已添加了嫒宝粑粑，现在可以开始聊天了。", "content_type": 1011, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "receiver": "1688854710715177", "send_time": "1753106980", "sender": "7881299718050125", "sender_name": "", "server_id": "1018338"}, "type": 11187}
2025-07-21 22:09:41 - src.core.message_processor.2037063677600 - INFO - 检查是否是新联系人消息----True
2025-07-21 22:09:41 - src.core.message_processor.2037063677600 - INFO - 检查是否是新联系人消息----False
2025-07-21 22:09:41 - src.core.message_processor.2037063677600 - INFO - 不是新联系人消息，跳过处理
2025-07-21 22:09:41 - src.core.message_processor.2037063677600 - INFO - 收到原始消息: {"data": {"appinfo": "omcIHWiGTOux1Q9", "at_list": [], "content": "我是嫒宝粑粑", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "1870", "receiver": "1688854710715177", "send_time": "1753106979", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1018333"}, "type": 11041}
2025-07-21 22:09:41 - src.core.message_processor.2037063677600 - INFO - 不是新联系人消息，跳过处理
2025-07-21 22:09:41 - src.core.message_processor.2037063677600 - INFO - 机器人名字: 邾俊勇
2025-07-21 22:09:41 - src.core.message_processor.2037063677600 - INFO - 消息类型: 2
2025-07-21 22:09:41 - src.core.message_processor.2037063677600 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 我是嫒宝粑粑
2025-07-21 22:09:41 - src.core.message_processor.2037063677600 - INFO - 消息类型: 私聊
2025-07-21 22:09:41 - src.core.message_processor.2037063677600 - INFO - 用户 7881299718050125 没有活跃会话，忽略文本消息
2025-07-21 22:10:13 - src.core.message_processor.2037063677600 - INFO - 收到原始消息: {"data": {"appinfo": "1904763891221916015", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "1873", "receiver": "1688854710715177", "send_time": "1753107011", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1018340"}, "type": 11041}
2025-07-21 22:10:13 - src.core.message_processor.2037063677600 - INFO - 机器人名字: 邾俊勇
2025-07-21 22:10:13 - src.core.message_processor.2037063677600 - INFO - 消息类型: 2
2025-07-21 22:10:13 - src.core.message_processor.2037063677600 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 1
2025-07-21 22:10:13 - src.core.message_processor.2037063677600 - INFO - 消息类型: 私聊
2025-07-21 22:10:13 - src.core.message_processor.2037063677600 - INFO - 用户 7881299718050125 没有活跃会话，忽略文本消息
2025-07-21 22:14:09 - src.core.message_processor.1940107556656 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250721.log
2025-07-21 22:14:09 - src.core.message_processor.1940107556656 - INFO - 消息处理器日志系统初始化完成
2025-07-21 22:15:10 - src.core.message_processor.1940107556656 - INFO - 收到系统消息: {"data": {"appinfo": "-BGsmcfeSD6biI2", "content": "以上是打招呼内容", "content_type": 1011, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "receiver": "1688854710715177", "send_time": "1753107309", "sender": "7881299718050125", "sender_name": "", "server_id": "1018364"}, "type": 11187}
2025-07-21 22:15:10 - src.core.message_processor.1940107556656 - INFO - 检查是否是新联系人消息----True
2025-07-21 22:15:10 - src.core.message_processor.1940107556656 - INFO - 收到原始消息: {"data": {"appinfo": "lDeh1vaOTM2-x2P", "at_list": [], "content": "我是嫒宝粑粑", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "1874", "receiver": "1688854710715177", "send_time": "1753107309", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1018362"}, "type": 11041}
2025-07-21 22:15:10 - src.core.message_processor.1940107556656 - INFO - 检测到新联系人消息，开始处理...
2025-07-21 22:15:10 - src.core.message_processor.1940107556656 - INFO - 机器人名字: 邾俊勇
2025-07-21 22:15:10 - src.core.message_processor.1940107556656 - INFO - 系统消息解析: conversation_id=S:1688854710715177_7881299718050125, from_wxid=7881299718050125
2025-07-21 22:15:10 - src.core.message_processor.1940107556656 - INFO - 消息类型: 2
2025-07-21 22:15:10 - src.core.message_processor.1940107556656 - INFO - 处理新联系人: 7881299718050125, 会话ID: S:1688854710715177_7881299718050125
2025-07-21 22:15:10 - src.core.message_processor.1940107556656 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 我是嫒宝粑粑
2025-07-21 22:15:10 - src.core.message_processor.1940107556656 - INFO - 向新联系人发送欢迎消息并创建会话: 7881299718050125, 会话ID: 7881299718050125
2025-07-21 22:15:10 - src.core.message_processor.1940107556656 - INFO - 消息类型: 私聊
2025-07-21 22:15:10 - src.core.message_processor.1940107556656 - INFO - 用户输入无效选项: 我是嫒宝粑粑, 提示重新输入, 聊天ID: S:1688854710715177_7881299718050125
2025-07-21 22:15:10 - src.core.message_processor.1940107556656 - INFO - 收到系统消息: {"data": {"appinfo": "R5Wr0UX9Sb2V4XI", "content": "你已添加了嫒宝粑粑，现在可以开始聊天了。", "content_type": 1011, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "receiver": "1688854710715177", "send_time": "1753107309", "sender": "7881299718050125", "sender_name": "", "server_id": "1018367"}, "type": 11187}
2025-07-21 22:15:10 - src.core.message_processor.1940107556656 - INFO - 检查是否是新联系人消息----True
2025-07-21 22:15:10 - src.core.message_processor.1940107556656 - INFO - 检测到新联系人消息，开始处理...
2025-07-21 22:15:10 - src.core.message_processor.1940107556656 - INFO - 系统消息解析: conversation_id=S:1688854710715177_7881299718050125, from_wxid=7881299718050125
2025-07-21 22:15:10 - src.core.message_processor.1940107556656 - INFO - 处理新联系人: 7881299718050125, 会话ID: S:1688854710715177_7881299718050125
2025-07-21 22:15:10 - src.core.message_processor.1940107556656 - INFO - 用户 7881299718050125 已有活跃会话，跳过创建
2025-07-21 22:15:10 - src.core.message_processor.1940107556656 - INFO - 收到原始消息: {"data": {"appinfo": "CAEQ7pb5wwYYqYbwiJKAgAMgBg==", "at_list": [], "content": "您好！欢迎咨询，请问您需要以下哪项服务？请直接回复对应数字：\n1.查询快递物流信息\n2.投诉/反馈问题\n3.联系人工客服", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 1, "local_id": "1876", "receiver": "7881299718050125", "send_time": "1753107309", "sender": "1688854710715177", "sender_name": "邾俊勇", "server_id": "1018369"}, "type": 11041}
2025-07-21 22:15:10 - src.core.message_processor.1940107556656 - INFO - 机器人名字: 邾俊勇
2025-07-21 22:15:10 - src.core.message_processor.1940107556656 - INFO - 忽略自己发送的消息
2025-07-21 22:15:10 - src.core.message_processor.1940107556656 - INFO - 收到原始消息: {"data": {"appinfo": "CAEQ7pb5wwYYqYbwiJKAgAMgBw==", "at_list": [], "content": "请回复对应的数字序号（1-3）", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 1, "local_id": "1877", "receiver": "7881299718050125", "send_time": "1753107309", "sender": "1688854710715177", "sender_name": "邾俊勇", "server_id": "1018371"}, "type": 11041}
2025-07-21 22:15:10 - src.core.message_processor.1940107556656 - INFO - 机器人名字: 邾俊勇
2025-07-21 22:15:10 - src.core.message_processor.1940107556656 - INFO - 忽略自己发送的消息
2025-07-21 22:16:37 - src.core.message_processor.1940107556656 - INFO - 收到原始消息: {"data": {"appinfo": "2470594354416138467", "at_list": [], "content": "3", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "1879", "receiver": "1688854710715177", "send_time": "1753107395", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1018373"}, "type": 11041}
2025-07-21 22:16:37 - src.core.message_processor.1940107556656 - INFO - 机器人名字: 邾俊勇
2025-07-21 22:16:37 - src.core.message_processor.1940107556656 - INFO - 消息类型: 2
2025-07-21 22:16:37 - src.core.message_processor.1940107556656 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 3
2025-07-21 22:16:37 - src.core.message_processor.1940107556656 - INFO - 消息类型: 私聊
2025-07-21 22:16:37 - src.core.message_processor.1940107556656 - INFO - 开始处理特殊回复，内容长度: 17
2025-07-21 22:16:37 - src.core.message_processor.1940107556656 - INFO - 普通文本回复
2025-07-21 22:16:37 - src.core.message_processor.1940107556656 - INFO - 发送选项回复给私聊用户: 7881299718050125, 聊天ID: S:1688854710715177_7881299718050125
2025-07-21 22:16:37 - src.core.message_processor.1940107556656 - INFO - 对话结束，清理会话: 7881299718050125
2025-07-21 22:16:37 - src.core.message_processor.1940107556656 - INFO - 收到原始消息: {"data": {"appinfo": "CAEQxZf5wwYYqYbwiJKAgAMgCA==", "at_list": [], "content": "正在为您转接人工客服，请稍候...", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 1, "local_id": "1880", "receiver": "7881299718050125", "send_time": "1753107396", "sender": "1688854710715177", "sender_name": "邾俊勇", "server_id": "1018376"}, "type": 11041}
2025-07-21 22:16:37 - src.core.message_processor.1940107556656 - INFO - 机器人名字: 邾俊勇
2025-07-21 22:16:37 - src.core.message_processor.1940107556656 - INFO - 忽略自己发送的消息
