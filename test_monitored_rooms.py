# -*- coding: utf-8 -*-
"""
测试监控群列表获取功能
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.core.wechat_handler import WeChatHandler
from src.core.message_processor import MessageProcessor
from src.gui.main_window import WeChatGUI
from src.gui.room_management import RoomManagementTab


def test_monitored_rooms_access():
    """测试监控群列表的访问路径"""
    print("测试监控群列表访问路径...")
    
    try:
        # 创建模拟的GUI结构
        import tkinter as tk
        
        # 创建根窗口（隐藏）
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 创建模拟的notebook
        try:
            import ttkbootstrap as ttk
        except ImportError:
            from tkinter import ttk
        
        notebook = ttk.Notebook(root)
        
        # 创建模拟的主窗口类
        class MockMainWindow:
            def __init__(self):
                self.root = root
                self.notebook = notebook
                self.room_tab = RoomManagementTab(notebook, self)
                self.wechat_handler = None
                self.message_processor = None
                self.config_manager = None
                self.paused_rooms = []
                self.group_option_configs = {}
                self.current_selected_groups = []
                self.user_sessions = {}
        
        # 创建模拟主窗口
        mock_gui = MockMainWindow()
        
        # 创建微信处理器和消息处理器
        wechat_handler = WeChatHandler()
        message_processor = MessageProcessor(wechat_handler, mock_gui)
        
        # 测试1: 检查GUI结构
        print("1. 检查GUI结构...")
        print(f"   mock_gui有room_tab属性: {hasattr(mock_gui, 'room_tab')}")
        print(f"   room_tab有monitored_rooms_tree属性: {hasattr(mock_gui.room_tab, 'monitored_rooms_tree')}")
        
        # 测试2: 添加一些测试数据到监控群列表
        print("\n2. 添加测试数据...")
        test_rooms = [
            {"name": "测试群1", "id": "R:test_room_1"},
            {"name": "测试群2", "id": "R:test_room_2"},
            {"name": "老城极兔韵达快递", "id": "R:10881056632797218"}
        ]
        
        for room in test_rooms:
            item_id = f"monitored_{room['id']}"
            mock_gui.room_tab.monitored_rooms_tree.insert('', 'end', iid=item_id,
                text=room['name'],
                values=(room['id'],))
            print(f"   添加了群: {room['name']} ({room['id']})")
        
        # 测试3: 获取监控群列表
        print("\n3. 测试获取监控群列表...")
        monitored_rooms = message_processor._get_monitored_rooms()
        print(f"   获取到的监控群列表: {monitored_rooms}")
        
        # 验证结果
        expected_rooms = ["R:test_room_1", "R:test_room_2", "R:10881056632797218"]
        if monitored_rooms == expected_rooms:
            print("   ✅ 监控群列表获取正确")
            success = True
        else:
            print(f"   ❌ 监控群列表获取错误，期望: {expected_rooms}")
            success = False
        
        # 测试4: 测试特定群是否在监控列表中
        print("\n4. 测试群监控检查...")
        test_group_id = "R:10881056632797218"
        is_monitored = test_group_id in monitored_rooms
        print(f"   群 {test_group_id} 是否在监控列表中: {is_monitored}")
        
        if is_monitored:
            print("   ✅ 群监控检查正确")
        else:
            print("   ❌ 群监控检查失败")
            success = False
        
        # 清理
        root.destroy()
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_message_processing_logic():
    """测试消息处理逻辑中的监控群检查"""
    print("\n测试消息处理逻辑中的监控群检查...")
    
    try:
        # 模拟消息数据
        mock_message_data = {
            "conversation_id": "R:10881056632797218",
            "sender": "7881299718050125",
            "sender_name": "嫒宝粑粑",
            "content": "1"
        }
        
        print(f"模拟消息数据: {mock_message_data}")
        
        # 创建简单的测试环境
        wechat_handler = WeChatHandler()
        
        # 创建模拟GUI（简化版）
        class SimpleGUI:
            def __init__(self):
                self.room_tab = SimpleRoomTab()
        
        class SimpleRoomTab:
            def __init__(self):
                self.monitored_rooms_tree = SimpleTree()
        
        class SimpleTree:
            def __init__(self):
                self.items = {
                    "item1": {"values": ["R:10881056632797218"]},
                    "item2": {"values": ["R:test_room_2"]}
                }
            
            def get_children(self):
                return list(self.items.keys())
            
            def item(self, item_id):
                return self.items.get(item_id, {"values": []})
        
        simple_gui = SimpleGUI()
        message_processor = MessageProcessor(wechat_handler, simple_gui)
        
        # 测试获取监控群列表
        monitored_rooms = message_processor._get_monitored_rooms()
        print(f"获取到的监控群列表: {monitored_rooms}")
        
        # 检查目标群是否在监控列表中
        target_room = mock_message_data["conversation_id"]
        is_monitored = target_room in monitored_rooms
        
        print(f"目标群 {target_room} 是否在监控列表中: {is_monitored}")
        
        if is_monitored:
            print("✅ 消息处理逻辑测试通过")
            return True
        else:
            print("❌ 消息处理逻辑测试失败")
            return False
        
    except Exception as e:
        print(f"❌ 消息处理逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("=" * 70)
    print("监控群列表获取功能测试")
    print("=" * 70)
    
    # 测试1: 监控群列表访问
    access_test_ok = test_monitored_rooms_access()
    
    # 测试2: 消息处理逻辑
    logic_test_ok = test_message_processing_logic()
    
    # 总结
    print("\n" + "=" * 70)
    print("测试结果总结:")
    print(f"监控群访问测试: {'✅ 通过' if access_test_ok else '❌ 失败'}")
    print(f"消息处理逻辑测试: {'✅ 通过' if logic_test_ok else '❌ 失败'}")
    
    if access_test_ok and logic_test_ok:
        print("\n🎉 所有测试通过！监控群列表获取功能已修复！")
        print("\n修复内容:")
        print("- 修正了监控群列表的访问路径")
        print("- 从 self.gui.monitored_rooms_tree 改为 self.gui.room_tab.monitored_rooms_tree")
        print("- 添加了更完整的属性检查")
        return True
    else:
        print("\n❌ 部分测试失败，请检查问题。")
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        input("\n按回车键退出...")
    sys.exit(0 if success else 1)
