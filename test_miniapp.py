# -*- coding: utf-8 -*-
"""
测试小程序发送特殊格式功能
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.core.wechat_handler import WeChatHandler
from src.core.message_processor import MessageProcessor


def test_miniapp_format():
    """测试小程序发送特殊格式"""
    print("测试小程序发送特殊格式功能...")
    
    try:
        # 创建微信处理器和消息处理器
        wechat_handler = WeChatHandler()
        message_processor = MessageProcessor(wechat_handler)
        
        # 测试完整的小程序格式解析
        test_reply_content = """wait_reply=正在为您打开小程序...
aes_key=abc123def456
file_id=file_12345
size=1024
appicon=https://example.com/icon.png
appid=wx1234567890abcdef
appname=测试小程序
conversation_id=conv_123
page_path=pages/index/index
title=小程序标题
username=testuser"""
        
        print(f"测试回复内容:\n{test_reply_content}")
        
        # 解析特殊格式参数
        params = message_processor._parse_special_reply_params(test_reply_content)
        print(f"解析到的参数: {params}")
        
        # 验证参数
        expected_params = {
            'wait_reply': '正在为您打开小程序...',
            'aes_key': 'abc123def456',
            'file_id': 'file_12345',
            'size': '1024',
            'appicon': 'https://example.com/icon.png',
            'appid': 'wx1234567890abcdef',
            'appname': '测试小程序',
            'conversation_id': 'conv_123',
            'page_path': 'pages/index/index',
            'title': '小程序标题',
            'username': 'testuser'
        }
        
        if params == expected_params:
            print("✅ 小程序参数解析正确")
        else:
            print(f"❌ 小程序参数解析错误")
            print(f"期望: {expected_params}")
            print(f"实际: {params}")
            return False
        
        # 测试不同的小程序配置
        test_cases = [
            {
                'name': '电商小程序',
                'content': """wait_reply=正在打开商城...
aes_key=shop_key_123
file_id=shop_file_456
size=2048
appicon=https://shop.com/icon.png
appid=wx_shop_123456
appname=购物商城
conversation_id=shop_conv_789
page_path=pages/product/detail
title=商品详情
username=shopper""",
                'expected': {
                    'wait_reply': '正在打开商城...',
                    'aes_key': 'shop_key_123',
                    'file_id': 'shop_file_456',
                    'size': '2048',
                    'appicon': 'https://shop.com/icon.png',
                    'appid': 'wx_shop_123456',
                    'appname': '购物商城',
                    'conversation_id': 'shop_conv_789',
                    'page_path': 'pages/product/detail',
                    'title': '商品详情',
                    'username': 'shopper'
                }
            },
            {
                'name': '游戏小程序',
                'content': """aes_key=game_key_789
file_id=game_file_012
size=4096
appicon=https://game.com/icon.png
appid=wx_game_789012
appname=休闲游戏
conversation_id=game_conv_345
page_path=pages/game/main
title=开始游戏
username=gamer""",
                'expected': {
                    'aes_key': 'game_key_789',
                    'file_id': 'game_file_012',
                    'size': '4096',
                    'appicon': 'https://game.com/icon.png',
                    'appid': 'wx_game_789012',
                    'appname': '休闲游戏',
                    'conversation_id': 'game_conv_345',
                    'page_path': 'pages/game/main',
                    'title': '开始游戏',
                    'username': 'gamer'
                }
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n测试用例 {i}: {test_case['name']}")
            print(f"内容: {test_case['content']}")
            
            params = message_processor._parse_special_reply_params(test_case['content'])
            print(f"解析结果: {params}")
            
            if params == test_case['expected']:
                print(f"✅ 测试用例 {i} 通过")
            else:
                print(f"❌ 测试用例 {i} 失败")
                return False
        
        # 测试混合格式（包含普通文本和小程序）
        test_mixed_content = """推荐您使用我们的小程序：
wait_reply=小程序加载中...
aes_key=mixed_key_123
file_id=mixed_file_456
size=1536
appicon=https://mixed.com/icon.png
appid=wx_mixed_123456
appname=混合测试
conversation_id=mixed_conv_789
page_path=pages/mixed/index
title=混合小程序
username=mixeduser
点击即可使用完整功能"""
        
        print(f"\n测试混合内容:\n{test_mixed_content}")
        
        mixed_params = message_processor._parse_special_reply_params(test_mixed_content)
        normal_text = message_processor._extract_normal_text_from_special_reply(test_mixed_content)
        
        print(f"解析到的混合参数: {mixed_params}")
        print(f"提取的普通文本: {normal_text}")
        
        expected_mixed_params = {
            'wait_reply': '小程序加载中...',
            'aes_key': 'mixed_key_123',
            'file_id': 'mixed_file_456',
            'size': '1536',
            'appicon': 'https://mixed.com/icon.png',
            'appid': 'wx_mixed_123456',
            'appname': '混合测试',
            'conversation_id': 'mixed_conv_789',
            'page_path': 'pages/mixed/index',
            'title': '混合小程序',
            'username': 'mixeduser'
        }
        expected_normal_text = "推荐您使用我们的小程序：\n点击即可使用完整功能"
        
        if mixed_params == expected_mixed_params and normal_text == expected_normal_text:
            print("✅ 混合内容解析正确")
        else:
            print(f"❌ 混合内容解析错误")
            print(f"期望参数: {expected_mixed_params}")
            print(f"实际参数: {mixed_params}")
            print(f"期望文本: {expected_normal_text}")
            print(f"实际文本: {normal_text}")
            return False
        
        print("\n🎉 所有小程序格式测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_wechat_handler_send_miniapp():
    """测试微信处理器的send_miniapp方法"""
    print("\n测试微信处理器的send_miniapp方法...")
    
    try:
        wechat_handler = WeChatHandler()
        
        # 测试发送小程序
        test_miniapps = [
            {
                'name': '测试小程序1',
                'params': {
                    'aes_key': 'test_key_1',
                    'file_id': 'test_file_1',
                    'size': '1024',
                    'appicon': 'https://test1.com/icon.png',
                    'appid': 'wx_test_1',
                    'appname': '测试应用1',
                    'conversation_id': 'test_conv_1',
                    'page_path': 'pages/test1',
                    'title': '测试标题1',
                    'username': 'testuser1'
                }
            },
            {
                'name': '测试小程序2',
                'params': {
                    'aes_key': 'test_key_2',
                    'file_id': 'test_file_2',
                    'size': '2048',
                    'appicon': 'https://test2.com/icon.png',
                    'appid': 'wx_test_2',
                    'appname': '测试应用2',
                    'conversation_id': 'test_conv_2',
                    'page_path': 'pages/test2',
                    'title': '测试标题2',
                    'username': 'testuser2'
                }
            }
        ]
        
        for miniapp in test_miniapps:
            print(f"测试发送: {miniapp['name']}")
            result = wechat_handler.send_miniapp(**miniapp['params'])
            
            if result:
                print(f"✅ 发送 {miniapp['name']} 成功")
            else:
                print(f"❌ 发送 {miniapp['name']} 失败")
                return False
        
        print("✅ send_miniapp方法测试通过")
        return True
            
    except Exception as e:
        print(f"❌ send_miniapp方法测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("=" * 70)
    print("小程序发送特殊格式功能测试")
    print("=" * 70)
    
    # 测试格式解析
    format_test_ok = test_miniapp_format()
    
    # 测试发送方法
    send_test_ok = test_wechat_handler_send_miniapp()
    
    # 总结
    print("\n" + "=" * 70)
    print("测试结果总结:")
    print(f"格式解析测试: {'✅ 通过' if format_test_ok else '❌ 失败'}")
    print(f"发送方法测试: {'✅ 通过' if send_test_ok else '❌ 失败'}")
    
    if all([format_test_ok, send_test_ok]):
        print("\n🎉 所有测试通过！小程序发送功能已成功添加！")
        print("\n支持的特殊格式:")
        print("1. 📎 链接卡片: wait_reply= + title= + desc= + url= + img=")
        print("2. 👤 用户卡片: wait_reply= + user_id=")
        print("3. 📁 文件发送: wait_reply= + file_path=")
        print("4. 📱 小程序: wait_reply= + aes_key= + file_id= + size= + appicon= + appid= + appname= + conversation_id= + page_path= + title= + username=")
        print("\n小程序格式使用方法:")
        print("wait_reply=等待消息（可选）")
        print("aes_key=AES密钥")
        print("file_id=文件ID")
        print("size=大小")
        print("appicon=应用图标URL")
        print("appid=应用ID")
        print("appname=应用名称")
        print("conversation_id=会话ID")
        print("page_path=页面路径")
        print("title=标题")
        print("username=用户名")
        return True
    else:
        print("\n❌ 部分测试失败，请检查问题。")
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        input("\n按回车键退出...")
    sys.exit(0 if success else 1)
