# 微信私聊机器人 - 使用示例

## 完整的用户交互流程

### 1. 新用户通过好友验证

**系统消息触发**:
```json
{
    "data": {
        "type": "friend_verify",
        "content": "张三通过了好友验证",
        "conversation_id": "S:1234567890",
        "from_wxid": "zhangsan123"
    }
}
```

**机器人自动响应**:
```
您好！欢迎咨询，请问您需要以下哪项服务？请直接回复对应数字：
1.产品咨询
2.技术支持
3.售后服务
4.投诉建议
5.联系人工客服
```

**会话创建**:
```python
user_sessions["zhangsan123"] = {
    'start_time': 1642781234.567,
    'waiting_for_option': True,
    'chat_id': 'S:1234567890',
    'current_path': [],
    'source': 'system_message'
}
```

### 2. 用户进行选项选择

**用户发送文本消息**:
```json
{
    "data": {
        "room_wxid": "S:1234567890",
        "from_wxid": "zhangsan123",
        "from_name": "张三",
        "msg": "1",
        "content_type": 0
    }
}
```

**机器人处理逻辑**:
1. 检查用户是否有活跃会话 ✅
2. 解析用户选择的选项 "1"
3. 查找对应的回复内容
4. 发送回复消息

**机器人回复**:
```
感谢您对我们产品的关注！请详细描述您想了解的产品信息，我们会为您详细介绍。
```

### 3. 用户选择最终选项

**用户继续选择**:
```
用户: "5"
```

**机器人回复**:
```
正在为您转接人工客服，请稍候...
```

**会话结束**:
```python
# 如果选项类型是 "final"，会话自动结束
del user_sessions["zhangsan123"]
```

### 4. 会话超时处理

**5分钟后无活动**:
```python
# 定期清理检测到超时会话
current_time - session['start_time'] > 300  # 超过5分钟
```

**机器人发送超时消息**:
```
您的会话已超时，如需继续咨询请重新发送消息。
```

**清理会话**:
```python
del user_sessions["zhangsan123"]
```

## 配置示例

### 基础配置
```json
{
    "group_option_configs": {
        "private_chat": {
            "auto_reply": "您好！欢迎咨询，请问您需要以下哪项服务？请直接回复对应数字：\n1.产品咨询\n2.技术支持\n3.售后服务\n4.投诉建议\n5.联系人工客服",
            "option_replies": {
                "1": "感谢您对我们产品的关注！请详细描述您想了解的产品信息，我们会为您详细介绍。",
                "2": "我们的技术支持团队随时为您服务！请描述您遇到的技术问题，我们会尽快为您解决。",
                "3": "请提供您的订单号或产品信息，我们的售后团队会为您处理相关问题。",
                "4": "您的意见对我们很重要！请详细说明您的建议或投诉内容，我们会认真处理。",
                "5": "正在为您转接人工客服，请稍候..."
            }
        }
    }
}
```

### 高级配置（树状菜单）
```json
{
    "group_option_configs": {
        "private_chat": {
            "auto_reply": "请选择服务类型：\n1.技术支持\n2.商务咨询",
            "tree_options": {
                "1": {
                    "text": "技术支持",
                    "type": "menu",
                    "reply": "请选择技术支持类型：\n1.软件问题\n2.硬件问题",
                    "children": {
                        "1": {
                            "text": "软件问题",
                            "type": "final",
                            "reply": "请详细描述您的软件问题，我们的技术人员会尽快为您解决。"
                        },
                        "2": {
                            "text": "硬件问题", 
                            "type": "final",
                            "reply": "请详细描述您的硬件问题，我们会安排专业人员为您处理。"
                        }
                    }
                },
                "2": {
                    "text": "商务咨询",
                    "type": "final",
                    "reply": "感谢您的咨询！请留下您的联系方式，我们的商务人员会尽快与您联系。"
                }
            }
        }
    }
}
```

## 日志示例

### 系统消息处理日志
```
2025-07-21 21:11:24 - INFO - 收到系统消息: {"data": {"type": "friend_verify", "content": "用户通过了好友验证", "conversation_id": "S:1234567890", "from_wxid": "user456"}}
2025-07-21 21:11:24 - INFO - 处理新联系人: user456, 会话ID: S:1234567890
2025-07-21 21:11:24 - INFO - 向新联系人发送欢迎消息并创建会话: user456, 会话ID: user456
```

### 文本消息处理日志
```
2025-07-21 21:11:25 - INFO - 收到原始消息: {"data": {"room_wxid": "S:1234567890", "from_wxid": "user456", "msg": "1", "content_type": 0}}
2025-07-21 21:11:25 - INFO - 消息类型: 私聊
2025-07-21 21:11:25 - INFO - 发送选项回复给私聊用户: user456, 聊天ID: S:1234567890
```

### 会话超时日志
```
2025-07-21 21:16:24 - INFO - 清理过期会话: user456
2025-07-21 21:16:24 - INFO - 发送会话超时消息给用户: user456
2025-07-21 21:16:24 - INFO - 清理了 1 个过期会话
```

## 错误处理

### 无会话的文本消息
```
2025-07-21 21:11:24 - INFO - 用户 user123 没有活跃会话，忽略文本消息
```

### 重复系统消息
```
2025-07-21 21:11:24 - INFO - 用户 user456 已有活跃会话，跳过创建
```

### 配置缺失
```
2025-07-21 21:11:24 - WARNING - 私聊配置未找到，无法发送欢迎消息
```

## 监控和维护

### 会话状态监控
- 当前活跃会话数量
- 会话平均持续时间
- 超时会话清理频率

### 性能指标
- 系统消息响应时间
- 文本消息处理时间
- 内存使用情况

### 维护建议
1. 定期检查日志文件
2. 监控会话数量变化
3. 根据使用情况调整超时时间
4. 定期备份配置文件

这个新的会话管理机制确保了：
- 只有真正的新用户才会收到欢迎消息
- 避免了垃圾消息创建无用会话
- 提供了完整的会话生命周期管理
- 优化了系统资源使用
