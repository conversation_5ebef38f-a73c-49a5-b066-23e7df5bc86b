2025-07-23 07:39:16 - src.core.message_processor.1895385310736 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250723.log
2025-07-23 07:39:16 - src.core.message_processor.1895385310736 - INFO - 消息处理器日志系统初始化完成
2025-07-23 07:40:28 - src.core.message_processor.1895385310736 - INFO - 收到原始消息: {"data": {"appinfo": "SteUJro-QtW3NWt", "at_list": [], "content": "我是嫒宝粑粑", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "1970", "receiver": "1688854710715177", "send_time": "1753227629", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1018812"}, "type": 11041}
2025-07-23 07:40:28 - src.core.message_processor.1895385310736 - INFO - 机器人名字: 邾俊勇
2025-07-23 07:40:28 - src.core.message_processor.1895385310736 - INFO - 消息类型: 2
2025-07-23 07:40:28 - src.core.message_processor.1895385310736 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 我是嫒宝粑粑
2025-07-23 07:40:28 - src.core.message_processor.1895385310736 - INFO - 消息类型: 私聊
2025-07-23 07:40:28 - src.core.message_processor.1895385310736 - INFO - 用户 7881299718050125 没有活跃会话，忽略文本消息
2025-07-23 07:40:28 - src.core.message_processor.1895385310736 - INFO - 收到系统消息: {"data": {"appinfo": "7hKmS3yrRF-LGaP", "content": "以上是打招呼内容", "content_type": 1011, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "receiver": "1688854710715177", "send_time": "1753227629", "sender": "7881299718050125", "sender_name": "", "server_id": "1018815"}, "type": 11187}
2025-07-23 07:40:28 - src.core.message_processor.1895385310736 - INFO - 收到系统消息: {"data": {"appinfo": "y8utGJAsSCCs42R", "content": "你已添加了嫒宝粑粑，现在可以开始聊天了。", "content_type": 1011, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "receiver": "1688854710715177", "send_time": "1753227629", "sender": "7881299718050125", "sender_name": "", "server_id": "1018817"}, "type": 11187}
2025-07-23 07:40:28 - src.core.message_processor.1895385310736 - INFO - 检查是否是新联系人消息----True
2025-07-23 07:40:28 - src.core.message_processor.1895385310736 - INFO - 检查是否是新联系人消息----True
2025-07-23 07:40:28 - src.core.message_processor.1895385310736 - INFO - 检测到新联系人消息，开始处理...
2025-07-23 07:40:28 - src.core.message_processor.1895385310736 - INFO - 检测到新联系人消息，开始处理...
2025-07-23 07:40:28 - src.core.message_processor.1895385310736 - INFO - 系统消息解析: conversation_id=S:1688854710715177_7881299718050125, from_wxid=7881299718050125
2025-07-23 07:40:28 - src.core.message_processor.1895385310736 - INFO - 系统消息解析: conversation_id=S:1688854710715177_7881299718050125, from_wxid=7881299718050125
2025-07-23 07:40:28 - src.core.message_processor.1895385310736 - INFO - 处理新联系人: 7881299718050125, 会话ID: S:1688854710715177_7881299718050125
2025-07-23 07:40:28 - src.core.message_processor.1895385310736 - INFO - 处理新联系人: 7881299718050125, 会话ID: S:1688854710715177_7881299718050125
2025-07-23 07:40:28 - src.core.message_processor.1895385310736 - INFO - 向新联系人发送欢迎消息并创建会话: 7881299718050125, 会话ID: 7881299718050125
2025-07-23 07:40:28 - src.core.message_processor.1895385310736 - INFO - 用户 7881299718050125 已有活跃会话，跳过创建
2025-07-23 07:40:29 - src.core.message_processor.1895385310736 - INFO - 收到原始消息: {"data": {"appinfo": "CAEQ7MKAxAYYqYbwiJKAgAMgAQ==", "at_list": [], "content": "您好！欢迎咨询，请问您需要以下哪项服务？请直接回复对应数字：\n1.查询快递物流信息\n2.投诉/反馈问题\n3.联系人工客服", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 1, "local_id": "1973", "receiver": "7881299718050125", "send_time": "1753227629", "sender": "1688854710715177", "sender_name": "邾俊勇", "server_id": "1018819"}, "type": 11041}
2025-07-23 07:40:29 - src.core.message_processor.1895385310736 - INFO - 机器人名字: 邾俊勇
2025-07-23 07:40:29 - src.core.message_processor.1895385310736 - INFO - 忽略自己发送的消息
2025-07-23 07:40:39 - src.core.message_processor.1895385310736 - INFO - 收到原始消息: {"data": {"appinfo": "326590053754254184", "at_list": [], "content": "3", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "1974", "receiver": "1688854710715177", "send_time": "1753227638", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1018821"}, "type": 11041}
2025-07-23 07:40:39 - src.core.message_processor.1895385310736 - INFO - 机器人名字: 邾俊勇
2025-07-23 07:40:39 - src.core.message_processor.1895385310736 - INFO - 消息类型: 2
2025-07-23 07:40:39 - src.core.message_processor.1895385310736 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 3
2025-07-23 07:40:39 - src.core.message_processor.1895385310736 - INFO - 消息类型: 私聊
2025-07-23 07:40:39 - src.core.message_processor.1895385310736 - INFO - 开始处理特殊回复，内容长度: 17
2025-07-23 07:40:39 - src.core.message_processor.1895385310736 - INFO - 普通文本回复
2025-07-23 07:40:39 - src.core.message_processor.1895385310736 - INFO - 发送选项回复给私聊用户: 7881299718050125, 聊天ID: S:1688854710715177_7881299718050125
2025-07-23 07:40:39 - src.core.message_processor.1895385310736 - INFO - 对话结束，清理会话: 7881299718050125
2025-07-23 07:40:39 - src.core.message_processor.1895385310736 - INFO - 收到原始消息: {"data": {"appinfo": "CAEQ98KAxAYYqYbwiJKAgAMgAw==", "at_list": [], "content": "正在为您转接人工客服，请稍候...", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 1, "local_id": "1975", "receiver": "7881299718050125", "send_time": "1753227640", "sender": "1688854710715177", "sender_name": "邾俊勇", "server_id": "1018826"}, "type": 11041}
2025-07-23 07:40:39 - src.core.message_processor.1895385310736 - INFO - 机器人名字: 邾俊勇
2025-07-23 07:40:39 - src.core.message_processor.1895385310736 - INFO - 忽略自己发送的消息
2025-07-23 07:40:51 - src.core.message_processor.1895385310736 - INFO - 收到原始消息: {"data": {"appinfo": "6746583158491011684", "at_list": [], "content": "你好", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "1976", "receiver": "1688854710715177", "send_time": "1753227650", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1018828"}, "type": 11041}
2025-07-23 07:40:51 - src.core.message_processor.1895385310736 - INFO - 机器人名字: 邾俊勇
2025-07-23 07:40:51 - src.core.message_processor.1895385310736 - INFO - 消息类型: 2
2025-07-23 07:40:51 - src.core.message_processor.1895385310736 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 你好
2025-07-23 07:40:51 - src.core.message_processor.1895385310736 - INFO - 消息类型: 私聊
2025-07-23 07:40:51 - src.core.message_processor.1895385310736 - INFO - 用户 7881299718050125 没有活跃会话，忽略文本消息
2025-07-23 22:35:36 - src.core.message_processor.1840353897008 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250723.log
2025-07-23 22:35:36 - src.core.message_processor.1840353897008 - INFO - 消息处理器日志系统初始化完成
2025-07-23 22:35:36 - src.core.message_processor.1840353897008 - INFO - 设置间隔时间为: 5秒 (0分钟)
2025-07-23 22:35:36 - src.core.message_processor.1840353897008 - INFO - 收到系统消息: {"data": {"content": "你已添加了测试用户，现在可以开始聊天了。", "conversation_id": "S:1234567890", "sender": "test_user_123"}}
2025-07-23 22:35:36 - src.core.message_processor.1840353897008 - INFO - 检查是否是新联系人消息----True
2025-07-23 22:35:36 - src.core.message_processor.1840353897008 - INFO - 检测到新联系人消息，开始处理...
2025-07-23 22:35:36 - src.core.message_processor.1840353897008 - INFO - 系统消息解析: conversation_id=S:1234567890, from_wxid=test_user_123
2025-07-23 22:35:36 - src.core.message_processor.1840353897008 - INFO - 处理新联系人: test_user_123, 会话ID: S:1234567890
2025-07-23 22:35:36 - src.core.message_processor.1840353897008 - INFO - 向新联系人发送欢迎消息并创建会话: test_user_123, 会话ID: test_user_123
2025-07-23 22:35:36 - src.core.message_processor.1840353897008 - INFO - 收到原始消息: {"data": {"room_wxid": "S:1234567890", "from_wxid": "test_user_123", "from_name": "测试用户", "msg": "1", "content_type": 0}}
2025-07-23 22:35:36 - src.core.message_processor.1840353897008 - INFO - 机器人名字: None
2025-07-23 22:41:37 - src.core.message_processor.1602499060688 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250723.log
2025-07-23 22:41:37 - src.core.message_processor.1602499060688 - INFO - 消息处理器日志系统初始化完成
2025-07-23 22:41:37 - src.core.message_processor.1602499060688 - INFO - 设置间隔时间为: 1800秒 (30分钟)
2025-07-23 22:48:46 - src.core.message_processor.1602499060688 - INFO - 收到原始消息: {"data": {"appinfo": "2368406431951678065", "at_list": [], "content": "你好", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "2150", "receiver": "1688854710715177", "send_time": "1753282124", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1019507"}, "type": 11041}
2025-07-23 22:48:46 - src.core.message_processor.1602499060688 - INFO - 机器人名字: 邾俊勇
2025-07-23 22:48:46 - src.core.message_processor.1602499060688 - INFO - 消息类型: 2
2025-07-23 22:48:46 - src.core.message_processor.1602499060688 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 你好
2025-07-23 22:48:46 - src.core.message_processor.1602499060688 - INFO - 消息类型: 私聊
2025-07-23 22:48:46 - src.core.message_processor.1602499060688 - INFO - 用户 7881299718050125 没有活跃会话且未超过间隔时间，忽略文本消息
2025-07-23 23:04:43 - src.core.message_processor.1557429360096 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250723.log
2025-07-23 23:04:43 - src.core.message_processor.1557429360096 - INFO - 消息处理器日志系统初始化完成
2025-07-23 23:04:43 - src.core.message_processor.1557429360096 - INFO - 设置间隔时间为: 1800秒 (30分钟)
2025-07-23 23:04:43 - src.core.message_processor.1557429360096 - INFO - 设置间隔时间为: 1800秒 (30分钟)
2025-07-23 23:04:49 - src.core.message_processor.1557429360096 - INFO - 设置间隔时间为: 300秒 (5分钟)
2025-07-23 23:04:49 - src.core.message_processor.1557429360096 - INFO - 设置间隔时间为: 300秒 (5分钟)
2025-07-23 23:06:13 - src.core.message_processor.1557429360096 - INFO - 收到原始消息: {"data": {"appinfo": "2990912095058154166", "at_list": [], "content": "你好", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "2151", "receiver": "1688854710715177", "send_time": "1753283171", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1019514"}, "type": 11041}
2025-07-23 23:06:13 - src.core.message_processor.1557429360096 - INFO - 机器人名字: 邾俊勇
2025-07-23 23:06:13 - src.core.message_processor.1557429360096 - INFO - 消息类型: 2
2025-07-23 23:06:13 - src.core.message_processor.1557429360096 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 你好
2025-07-23 23:06:13 - src.core.message_processor.1557429360096 - INFO - 消息类型: 私聊
2025-07-23 23:06:13 - src.core.message_processor.1557429360096 - INFO - 用户 7881299718050125 没有活跃会话且未超过间隔时间，忽略文本消息
2025-07-23 23:12:16 - src.core.message_processor.1557429360096 - INFO - 收到原始消息: {"data": {"appinfo": "7771786692866518807", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "2152", "receiver": "1688854710715177", "send_time": "1753283535", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1019523"}, "type": 11041}
2025-07-23 23:12:16 - src.core.message_processor.1557429360096 - INFO - 机器人名字: 邾俊勇
2025-07-23 23:12:16 - src.core.message_processor.1557429360096 - INFO - 消息类型: 2
2025-07-23 23:12:16 - src.core.message_processor.1557429360096 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 1
2025-07-23 23:12:16 - src.core.message_processor.1557429360096 - INFO - 消息类型: 私聊
2025-07-23 23:12:16 - src.core.message_processor.1557429360096 - INFO - 用户 7881299718050125 没有活跃会话且未超过间隔时间，忽略文本消息
2025-07-23 23:34:56 - src.core.message_processor.2779576932912 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250723.log
2025-07-23 23:34:56 - src.core.message_processor.2779576932912 - INFO - 消息处理器日志系统初始化完成
2025-07-23 23:34:56 - src.core.message_processor.2779576932912 - ERROR - 清理旧会话记录失败: 'MessageProcessor' object has no attribute 'session_db'
2025-07-23 23:34:56 - src.core.message_processor.2779576932912 - INFO - 设置间隔时间为: 3秒 (0分钟)
2025-07-23 23:34:56 - src.core.message_processor.2779576932912 - INFO - 收到系统消息: {"data": {"content": "你已添加了测试用户，现在可以开始聊天了。", "conversation_id": "S:9876543210", "sender": "test_user_789"}}
2025-07-23 23:34:56 - src.core.message_processor.2779576932912 - INFO - 检查是否是新联系人消息----True
2025-07-23 23:34:56 - src.core.message_processor.2779576932912 - INFO - 检测到新联系人消息，开始处理...
2025-07-23 23:34:56 - src.core.message_processor.2779576932912 - INFO - 系统消息解析: conversation_id=S:9876543210, from_wxid=test_user_789
2025-07-23 23:34:56 - src.core.message_processor.2779576932912 - INFO - 处理新联系人: test_user_789, 会话ID: S:9876543210
2025-07-23 23:34:56 - src.core.message_processor.2779576932912 - INFO - 向新联系人发送欢迎消息并创建会话: test_user_789, 会话ID: test_user_789
2025-07-23 23:34:56 - src.core.message_processor.2779576932912 - INFO - 收到原始消息: {"data": {"room_wxid": "S:9876543210", "from_wxid": "test_user_789", "from_name": "测试用户", "msg": "1", "content_type": 0}}
2025-07-23 23:34:56 - src.core.message_processor.2779576932912 - INFO - 机器人名字: None
2025-07-23 23:34:56 - src.core.message_processor.2779576932912 - INFO - 忽略重复接收的消息: None
2025-07-23 23:35:00 - src.core.message_processor.2779576932912 - INFO - 忽略重复接收的消息: None
2025-07-23 23:49:54 - src.core.message_processor.2409412338112 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250723.log
2025-07-23 23:49:54 - src.core.message_processor.2409412338112 - INFO - 消息处理器日志系统初始化完成
2025-07-23 23:49:55 - src.core.message_processor.2409412338112 - INFO - 设置间隔时间为: 1800秒 (30分钟)
2025-07-23 23:49:55 - src.core.message_processor.2409412338112 - INFO - 设置间隔时间为: 300秒 (5分钟)
2025-07-23 23:50:36 - src.core.message_processor.2409412338112 - INFO - 收到原始消息: {"data": {"appinfo": "4560662831350591803", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:1688854710715177_7881299718050125", "is_pc": 0, "local_id": "2153", "receiver": "1688854710715177", "send_time": "1753285835", "sender": "7881299718050125", "sender_name": "嫒宝粑粑", "server_id": "1019528"}, "type": 11041}
2025-07-23 23:50:36 - src.core.message_processor.2409412338112 - INFO - 机器人名字: 邾俊勇
2025-07-23 23:50:36 - src.core.message_processor.2409412338112 - INFO - 消息类型: 2
2025-07-23 23:50:36 - src.core.message_processor.2409412338112 - INFO - 消息处理开始 | 发送者: 嫒宝粑粑(7881299718050125) | 会话ID: S:1688854710715177_7881299718050125 | 内容: 1
2025-07-23 23:50:36 - src.core.message_processor.2409412338112 - INFO - 消息类型: 私聊
2025-07-23 23:50:36 - src.core.message_processor.2409412338112 - ERROR - 消息处理异常: 'NoneType' object has no attribute 'get_active_session'
Traceback (most recent call last):
  File "d:\python\wechat\ntwork\客服选项选择\src\core\message_processor.py", line 214, in on_recv_message
    self._process_message(wechat_instance, room_wxid, from_wxid, sender_name, content, is_group, is_private)
  File "d:\python\wechat\ntwork\客服选项选择\src\core\message_processor.py", line 376, in _process_message
    session = self._get_existing_session(session_id, current_time)
  File "d:\python\wechat\ntwork\客服选项选择\src\core\message_processor.py", line 504, in _get_existing_session
    db_session = self.session_db.get_active_session(session_id)
AttributeError: 'NoneType' object has no attribute 'get_active_session'
2025-07-23 23:59:23 - src.core.message_processor.1752614851632 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250723.log
2025-07-23 23:59:23 - src.core.message_processor.1752614851632 - INFO - 消息处理器日志系统初始化完成
2025-07-23 23:59:23 - src.core.message_processor.1752614851632 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250723.log
2025-07-23 23:59:23 - src.core.message_processor.1752614851632 - INFO - 消息处理器日志系统初始化完成
2025-07-23 23:59:23 - src.core.message_processor.1752614851632 - INFO - 设置间隔时间为: 3秒 (0分钟)
2025-07-23 23:59:23 - src.core.message_processor.1752614851632 - INFO - 收到系统消息: {"data": {"content": "你已添加了测试用户，现在可以开始聊天了。", "conversation_id": "S:test_db_room", "sender": "test_db_user"}}
2025-07-23 23:59:23 - src.core.message_processor.1752614851632 - INFO - 检查是否是新联系人消息----True
2025-07-23 23:59:23 - src.core.message_processor.1752614851632 - INFO - 检测到新联系人消息，开始处理...
2025-07-23 23:59:23 - src.core.message_processor.1752614851632 - INFO - 系统消息解析: conversation_id=S:test_db_room, from_wxid=test_db_user
2025-07-23 23:59:23 - src.core.message_processor.1752614851632 - INFO - 处理新联系人: test_db_user, 会话ID: S:test_db_room
2025-07-23 23:59:23 - src.core.message_processor.1752614851632 - INFO - 数据库创建会话成功: test_db_user
2025-07-23 23:59:23 - src.core.message_processor.1752614851632 - INFO - 向新联系人发送欢迎消息并创建会话: test_db_user, 会话ID: test_db_user
2025-07-23 23:59:23 - src.core.message_processor.1752614851632 - INFO - 收到原始消息: {"data": {"room_wxid": "S:test_db_room", "from_wxid": "test_db_user", "from_name": "测试用户", "msg": "1", "content_type": 0}}
2025-07-23 23:59:23 - src.core.message_processor.1752614851632 - INFO - 机器人名字: None
2025-07-23 23:59:23 - src.core.message_processor.1752614851632 - INFO - 忽略重复接收的消息: None
2025-07-23 23:59:27 - src.core.message_processor.1752614851632 - INFO - 忽略重复接收的消息: None
2025-07-23 23:59:27 - src.core.message_processor.1752614851632 - INFO - 日志文件已配置: D:\python\wechat\ntwork\客服选项选择\logs\wechat_bot_20250723.log
2025-07-23 23:59:27 - src.core.message_processor.1752614851632 - INFO - 消息处理器日志系统初始化完成
2025-07-23 23:59:27 - src.core.message_processor.1752614851632 - INFO - 收到系统消息: {"data": {"content": "你已添加了测试用户，现在可以开始聊天了。", "conversation_id": "S:test_fallback_room", "sender": "test_fallback_user"}}
2025-07-23 23:59:27 - src.core.message_processor.1752614851632 - INFO - 检查是否是新联系人消息----True
2025-07-23 23:59:27 - src.core.message_processor.1752614851632 - INFO - 检测到新联系人消息，开始处理...
2025-07-23 23:59:27 - src.core.message_processor.1752614851632 - INFO - 系统消息解析: conversation_id=S:test_fallback_room, from_wxid=test_fallback_user
2025-07-23 23:59:27 - src.core.message_processor.1752614851632 - INFO - 处理新联系人: test_fallback_user, 会话ID: S:test_fallback_room
2025-07-23 23:59:27 - src.core.message_processor.1752614851632 - INFO - 向新联系人发送欢迎消息并创建会话: test_fallback_user, 会话ID: test_fallback_user
2025-07-23 23:59:27 - src.core.message_processor.1752614851632 - INFO - 收到原始消息: {"data": {"room_wxid": "S:test_fallback_room", "from_wxid": "test_fallback_user", "from_name": "测试用户", "msg": "1", "content_type": 0}}
2025-07-23 23:59:27 - src.core.message_processor.1752614851632 - INFO - 机器人名字: None
